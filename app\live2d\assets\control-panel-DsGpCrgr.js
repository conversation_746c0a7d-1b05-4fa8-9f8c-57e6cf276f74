import{L as M,M as v,a as w,b as E}from"./lappdelegate-BlEL7FXX.js";const f=class f{static getAudioContext(){return this.audioContext||(this.audioContext=new(window.AudioContext||window.webkitAudioContext)),this.audioContext}static detectAudioFormat(e){try{const s=e.replace(/^data:audio\/[^;]+;base64,/,""),t=atob(s),n=new Uint8Array(Math.min(12,t.length));for(let o=0;o<n.length;o++)n[o]=t.charCodeAt(o);if(n.length>=12){const o=String.fromCharCode(n[0],n[1],n[2],n[3]),i=String.fromCharCode(n[8],n[9],n[10],n[11]);if(o==="RIFF"&&i==="WAVE")return"wav"}return n.length>=3&&(n[0]===73&&n[1]===68&&n[2]===51||n[0]===255&&(n[1]&224)===224)?"mp3":"unknown"}catch(s){return console.error("检测音频格式失败:",s),"unknown"}}static base64ToArrayBuffer(e){const s=e.replace(/^data:audio\/[^;]+;base64,/,""),t=atob(s),n=new Uint8Array(t.length);for(let o=0;o<t.length;o++)n[o]=t.charCodeAt(o);return n.buffer}static async convertMp3ToWav(e){try{const t=await this.getAudioContext().decodeAudioData(e.slice(0));return this.audioBufferToWav(t)}catch(s){console.error("MP3转WAV失败:",s);const t=s instanceof Error?s.message:String(s);throw new Error(`MP3转WAV失败: ${t}`)}}static audioBufferToWav(e){const s=e.numberOfChannels,t=e.sampleRate,n=e.length,o=16,i=o/8,a=n*s*i,l=44+a,d=new ArrayBuffer(l),u=new DataView(d);let c=0;this.writeString(u,c,"RIFF"),c+=4,u.setUint32(c,l-8,!0),c+=4,this.writeString(u,c,"WAVE"),c+=4,this.writeString(u,c,"fmt "),c+=4,u.setUint32(c,16,!0),c+=4,u.setUint16(c,1,!0),c+=2,u.setUint16(c,s,!0),c+=2,u.setUint32(c,t,!0),c+=4,u.setUint32(c,t*s*i,!0),c+=4,u.setUint16(c,s*i,!0),c+=2,u.setUint16(c,o,!0),c+=2,this.writeString(u,c,"data"),c+=4,u.setUint32(c,a,!0),c+=4;for(let p=0;p<s;p++){const B=e.getChannelData(p);let y=44+p*i;for(let b=0;b<n;b++){const m=Math.max(-1,Math.min(1,B[b])),_=m<0?m*32768:m*32767;u.setInt16(y,_,!0),y+=s*i}}return d}static writeString(e,s,t){for(let n=0;n<t.length;n++)e.setUint8(s+n,t.charCodeAt(n))}static async processBase64Audio(e){const s=this.detectAudioFormat(e),t=this.base64ToArrayBuffer(e);switch(s){case"wav":return console.log("检测到WAV格式，直接使用"),t;case"mp3":return console.log("检测到MP3格式，转换为WAV"),await this.convertMp3ToWav(t);default:console.warn("未知音频格式，尝试作为MP3处理");try{return await this.convertMp3ToWav(t)}catch{return console.error("作为MP3处理失败，尝试作为WAV处理"),t}}}};f.audioContext=null;let g=f;class S{constructor(){this._currentModelIndex=0,this._speechBubble=null,this._bubbleText=null,this._bubblePosition="center",this._bubbleTimeout=null,this._isDragging=!1,this._dragOffset={x:0,y:0},this._bubbleManuallyPositioned=!1}async initialize(){this._delegate=M.getInstance(),this._delegate.initialize(),this._live2DManager=this._delegate.getLive2DManager(),await this.initializeUI(),this._delegate.run()}async initializeUI(){await this.initializeModelSelector(),this.initializeControlButtons(),this.initializeSpeechBubble(),this.initializeModelUpload(),this.setupModelLoadListener()}async initializeModelSelector(){const e=document.getElementById("modelSelector"),s=document.getElementById("deleteModelBtn"),t=await this._live2DManager.detectAvailableModels();e.innerHTML='<option value="">选择模型...</option>',t.forEach((n,o)=>{const i=document.createElement("option");i.value=o.toString(),i.textContent=`${n.name} ${n.status==="active"?"✓":"⚠️"}`,i.setAttribute("data-model-name",n.name),i.setAttribute("data-has-expressions",n.hasExpressions.toString()),i.setAttribute("data-has-motions",n.hasMotions.toString()),e.appendChild(i)}),e.addEventListener("change",async n=>{const o=n.target,i=parseInt(o.value);if(!isNaN(i)&&t[i]){const a=t[i];await this.switchModelByName(a.name),s.style.display="block",s.setAttribute("data-model-name",a.name),this.displayModelInfo(a)}else s.style.display="none",this.hideModelInfo()}),s.addEventListener("click",()=>{const n=s.getAttribute("data-model-name");n&&this.deleteModel(n)}),t.length>0&&(e.value="0",await this.switchModelByName(t[0].name),s.style.display="block",s.setAttribute("data-model-name",t[0].name),this.displayModelInfo(t[0]))}initializeControlButtons(){const e=document.getElementById("panelToggleBtn");e==null||e.addEventListener("click",()=>{this.toggleControlPanel()});const s=document.getElementById("randomMotionBtn");s==null||s.addEventListener("click",()=>{this._live2DManager.playRandomMotion(v),this.updateStatus("播放随机动作")});const t=document.getElementById("stopMotionBtn");t==null||t.addEventListener("click",()=>{this._live2DManager.stopAllMotions(),this.updateStatus("停止所有动作")});const n=document.getElementById("randomExpressionBtn");n==null||n.addEventListener("click",()=>{this._live2DManager.setRandomExpression(),this.updateStatus("设置随机表情")});const o=document.getElementById("resetExpressionBtn");o==null||o.addEventListener("click",()=>{const l=this._live2DManager.getCurrentModel();l&&(l.getExpressionManager().stopAllMotions(),this.updateStatus("重置表情"))});const i=document.getElementById("testLipSyncBtn");i==null||i.addEventListener("click",()=>{this.testLipSync()});const a=document.getElementById("resetTransformBtn");a==null||a.addEventListener("click",()=>{this.resetTransform()})}initializeSpeechBubble(){this._speechBubble=document.getElementById("speechBubble"),this._bubbleText=document.getElementById("bubbleText");const e=document.getElementById("showBubbleBtn");e==null||e.addEventListener("click",()=>{this.showSpeechBubble()});const s=document.getElementById("hideBubbleBtn");s==null||s.addEventListener("click",()=>{this.hideSpeechBubble()});const t=document.getElementById("bubbleTextInput");t==null||t.addEventListener("input",o=>{const i=o.target;this._bubbleText&&(this._bubbleText.textContent=i.value)});const n=document.querySelectorAll(".position-button");n.forEach(o=>{o.addEventListener("click",i=>{const a=i.target,l=a.getAttribute("data-position");l&&(n.forEach(d=>d.classList.remove("active")),a.classList.add("active"),this.setBubblePosition(l))})}),this.setBubblePosition(this._bubblePosition),this.setupBubbleDragging(),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="欢迎使用Live2D控制面板！",this.showSpeechBubble())},3e3)}setupModelLoadListener(){const e=async()=>{this._live2DManager.isModelReady()?(this.updateModelInfo(),await this.updateMotionButtons(),await this.updateExpressionButtons(),this.updateStatus("模型加载完成")):setTimeout(e,100)};e()}switchModel(e){this._currentModelIndex=e,this._live2DManager.switchToModel(e),this.updateStatus("正在加载模型..."),this.clearButtons(),this.setupModelLoadListener()}async switchModelByName(e){this._live2DManager.loadModelByName(e),this.updateStatus(`正在加载模型: ${e}...`),this.clearButtons(),this.setupModelLoadListener();try{const s=await this._live2DManager.validateModel(e);s&&!s.isValid&&this.updateStatus(`模型 ${e} 存在问题: ${s.analysis.issues.join(", ")}`)}catch(s){console.warn("模型验证失败:",s)}}displayModelInfo(e){const s=document.getElementById("modelInfo"),t=document.getElementById("motionCount"),n=document.getElementById("expressionCount");s&&t&&n&&(s.style.display="block",t.textContent=e.motionGroups?e.motionGroups.length.toString():"0",n.textContent=e.expressions?e.expressions.length.toString():"0")}hideModelInfo(){const e=document.getElementById("modelInfo");e&&(e.style.display="none")}getCurrentModelName(){const e=document.getElementById("modelSelector");return e&&e.selectedIndex>0?e.options[e.selectedIndex].getAttribute("data-model-name"):null}updateModelInfo(){const e=this._live2DManager.getCurrentModel();if(!e||!e.getModelSetting())return;const s=this._live2DManager.getMotionGroups(),t=this._live2DManager.getExpressionNames();let n=0;s.forEach(l=>{n+=this._live2DManager.getMotionCount(l)});const o=document.getElementById("motionCount"),i=document.getElementById("expressionCount"),a=document.getElementById("modelInfo");o&&(o.textContent=n.toString()),i&&(i.textContent=t.length.toString()),a&&(a.style.display="grid")}async updateMotionButtons(){const e=document.getElementById("motionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载动作...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelMotionsFromServer(n);e.innerHTML="";for(const i in o)o.hasOwnProperty(i)&&o[i].forEach((l,d)=>{if(l.exists){const u=document.createElement("button");u.className="control-button motion",u.textContent=`${i} ${d+1}`,u.addEventListener("click",()=>{this._live2DManager.playMotion(i,d),this.updateStatus(`播放动作: ${i} ${d+1}`),this.showMotionBubble(i,d)}),e.appendChild(u)}});e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>');return}}}catch(t){console.warn("从服务器获取动作信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getMotionGroups().forEach(t=>{const n=this._live2DManager.getMotionCount(t);for(let o=0;o<n;o++){const i=document.createElement("button");i.className="control-button motion",i.textContent=`${t} ${o+1}`,i.addEventListener("click",()=>{this._live2DManager.playMotion(t,o),this.updateStatus(`播放动作: ${t} ${o+1}`),this.showMotionBubble(t,o)}),e.appendChild(i)}}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>')}async updateExpressionButtons(){const e=document.getElementById("expressionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载表情...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelExpressionsFromServer(n);e.innerHTML="",o.forEach(i=>{const a=document.createElement("button");a.className="control-button expression",a.textContent=i,a.addEventListener("click",()=>{this._live2DManager.setExpression(i),this.updateStatus(`设置表情: ${i}`)}),e.appendChild(a)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>');return}}}catch(t){console.warn("从服务器获取表情信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getExpressionNames().forEach(t=>{const n=document.createElement("button");n.className="control-button expression",n.textContent=t,n.addEventListener("click",()=>{this._live2DManager.setExpression(t),this.updateStatus(`设置表情: ${t}`)}),e.appendChild(n)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>')}clearButtons(){const e=document.getElementById("motionButtons"),s=document.getElementById("expressionButtons"),t=document.getElementById("modelInfo");e&&(e.innerHTML='<div class="loading">加载中...</div>'),s&&(s.innerHTML='<div class="loading">加载中...</div>'),t&&(t.style.display="none")}showSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.add("show"),this.updateStatus("显示文字气泡"),this._bubbleTimeout=window.setTimeout(()=>{this.hideSpeechBubble()},5e3))}hideSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.remove("show"),this.updateStatus("隐藏文字气泡"))}showMotionBubble(e,s){if(!this._bubbleText)return;const n={Idle:["我在这里等你哦~","今天天气真不错呢！","你想和我聊什么呢？","我正在想你呢~","有什么我可以帮助你的吗？"],TapBody:["哎呀，你在摸我呢！","好痒啊~","嘻嘻，你真调皮！","不要乱摸啦~","你的手好温暖呢！"]}[e]||["正在播放动作..."];let o;s<n.length?o=n[s]:o=n[Math.floor(Math.random()*n.length)],this._bubbleText.textContent=o;const i=document.getElementById("bubbleTextInput");i&&(i.value=o),this.showSpeechBubble()}setupBubbleDragging(){this._speechBubble&&(this._speechBubble.addEventListener("mousedown",e=>{this._isDragging=!0;const s=document.querySelector(".canvas-container");if(!s)return;const t=s.getBoundingClientRect(),n=this._speechBubble.getBoundingClientRect(),o=n.left-t.left,i=n.top-t.top;this._dragOffset.x=e.clientX-t.left-o,this._dragOffset.y=e.clientY-t.top-i,e.preventDefault()}),document.addEventListener("mousemove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=document.querySelector(".canvas-container");if(!s)return;const t=s.getBoundingClientRect();let n=e.clientX-t.left-this._dragOffset.x,o=e.clientY-t.top-this._dragOffset.y;const i=this._speechBubble.offsetWidth,a=this._speechBubble.offsetHeight;n=Math.max(10,Math.min(n,t.width-i-10)),o=Math.max(10,Math.min(o,t.height-a-10)),this._speechBubble.style.left=`${n}px`,this._speechBubble.style.top=`${o}px`,this._speechBubble.style.right="auto",this._speechBubble.style.bottom="auto",this._speechBubble.style.transform="none",this._speechBubble.classList.add("manually-positioned")}),document.addEventListener("mouseup",()=>{this._isDragging&&(this._isDragging=!1)}),this._speechBubble.addEventListener("touchstart",e=>{this._isDragging=!0;const s=e.touches[0],t=document.querySelector(".canvas-container");if(!t)return;const n=t.getBoundingClientRect(),o=this._speechBubble.getBoundingClientRect(),i=o.left-n.left,a=o.top-n.top;this._dragOffset.x=s.clientX-n.left-i,this._dragOffset.y=s.clientY-n.top-a,e.preventDefault()}),document.addEventListener("touchmove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=e.touches[0],t=document.querySelector(".canvas-container");if(!t)return;const n=t.getBoundingClientRect();let o=s.clientX-n.left-this._dragOffset.x,i=s.clientY-n.top-this._dragOffset.y;const a=this._speechBubble.offsetWidth,l=this._speechBubble.offsetHeight;o=Math.max(10,Math.min(o,n.width-a-10)),i=Math.max(10,Math.min(i,n.height-l-10)),this._speechBubble.style.left=`${o}px`,this._speechBubble.style.top=`${i}px`,this._speechBubble.style.right="auto",this._speechBubble.style.bottom="auto",this._speechBubble.style.transform="none",this._speechBubble.classList.add("manually-positioned"),e.preventDefault()}),document.addEventListener("touchend",()=>{this._isDragging&&(this._isDragging=!1)}))}setBubblePosition(e){if(this._speechBubble&&!this._bubbleManuallyPositioned)switch(this._bubblePosition=e,this._speechBubble.classList.remove("top","bottom","left","right"),this._speechBubble.style.left="",this._speechBubble.style.top="",this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform="",e){case"bottom-left":this._speechBubble.style.left="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom");break;case"bottom-right":this._speechBubble.style.right="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom","right");break;case"top-left":this._speechBubble.style.left="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top");break;case"top-right":this._speechBubble.style.right="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top","right");break;case"center":default:this._speechBubble.style.left="50%",this._speechBubble.style.top="50%",this._speechBubble.style.transform="translate(-50%, -50%)";break}}resetTransform(){try{const e=this._live2DManager;e?e.getCurrentModel()?(window.parent.postMessage({type:"live2d_reset_transform"},"*"),this.updateStatus("已重置Live2D模型位置和缩放")):this.updateStatus("没有找到当前模型"):this.updateStatus("Live2D管理器未初始化")}catch(e){console.error("重置变换失败:",e),this.updateStatus("重置变换失败")}}testLipSync(){const e=this._live2DManager.getCurrentModel();if(!e){this.updateStatus("请先选择一个模型");return}this._bubbleText&&(this._bubbleText.textContent="正在测试口型同步功能...",this.showSpeechBubble());try{const s="../../Resources/测试.wav";e._lipsync=!0;const t=new Audio(s);t.volume=.8;const n=e._wavFileHandler;n?(Promise.all([t.play(),Promise.resolve(n.start(s))]).then(()=>{this.updateStatus("开始播放测试音频，观察口型同步效果"),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="口型同步测试中，请观察嘴部动画！")},1e3)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("口型同步测试完成"),this._bubbleText&&(this._bubbleText.textContent="口型同步测试完成！")})):this.updateStatus("音频处理器未初始化")}catch(s){console.error("测试口型同步失败:",s),this.updateStatus("测试口型同步失败，请检查音频文件")}}async changeModel(e){try{if(typeof e=="number"){e>=0&&e<w?(this._live2DManager.switchToModel(e),this.updateStatus(`切换到模型 ${e+1}`)):this.updateStatus(`无效的模型索引: ${e}`);return}if(typeof e=="string"){const s=await this._live2DManager.detectAvailableModels();s.find(n=>n.name===e)?(await this.switchModelByName(e),this.updateStatus(`切换到模型: ${e}`)):(this.updateStatus(`无效的模型名称: ${e}`),console.warn(`[Control Panel] 无效的模型名称: ${e}`),console.log("[Control Panel] 可用模型:",s.map(n=>n.name)));return}this.updateStatus(`无效的模型标识符类型: ${typeof e}`)}catch(s){console.error("[Control Panel] 切换模型失败:",s);const t=s instanceof Error?s.message:String(s);this.updateStatus(`切换模型失败: ${t}`)}}playMotion(e,s=0){this._live2DManager.getCurrentModel()?(this._live2DManager.playMotion(e,s),this.updateStatus(`播放动作: ${e} ${s+1}`),this.showMotionBubble(e,s)):this.updateStatus("请先选择一个模型")}playExpression(e){const s=this._live2DManager.getExpressionNames();try{let t=null;if(typeof e=="number")if(e>=0&&e<s.length)t=s[e];else{this.updateStatus(`无效的表情索引: ${e}`);return}if(typeof e=="string")if(s.includes(e))t=e;else{this.updateStatus(`无效的表情ID: ${e}`),console.warn(`[Control Panel] 无效的表情ID: ${e}`),console.log("[Control Panel] 可用表情:",s);return}t?(this._live2DManager.setExpression(t),this.updateStatus(`播放表情: ${t}`)):this.updateStatus(`无效的表情标识符类型: ${typeof e}`)}catch(t){console.error("[Control Panel] 播放表情失败:",t);const n=t instanceof Error?t.message:String(t);this.updateStatus(`播放表情失败: ${n}`)}}playRandomExpression(){this._live2DManager.setRandomExpression(),this.updateStatus("播放随机表情")}showBubble(e,s=!0){if(this._bubbleText){this._bubbleText.textContent=e;const t=document.getElementById("bubbleTextInput");t&&(t.value=e)}this.showSpeechBubble(),s||this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null)}hideBubble(){this.hideSpeechBubble()}playAudioWithLipSync(e){const s=this._live2DManager.getCurrentModel();if(!s){this.updateStatus("请先选择一个模型");return}try{s._lipsync=!0;const t=new Audio(`../../Resources/${e}`);t.volume=.8;const n=s._wavFileHandler;n?(Promise.all([t.play(),Promise.resolve(n.start(`../../Resources/${e}`))]).then(()=>{this.updateStatus(`播放音频: ${e}`)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("音频播放完成")})):this.updateStatus("音频处理器未初始化")}catch(t){console.error("播放音频失败:",t),this.updateStatus("播放音频失败，请检查音频文件")}}async playAudioFromBase64(e){const s=this._live2DManager.getCurrentModel();if(!s){this.updateStatus("请先选择一个模型");return}try{this.updateStatus("正在处理音频数据...");const t=await g.processBase64Audio(e);s._lipsync=!0;const n=new Blob([t],{type:"audio/wav"}),o=URL.createObjectURL(n),i=new Audio(o);i.volume=.8;const a=s._wavFileHandler;a?(Promise.all([i.play(),Promise.resolve(a.startFromArrayBuffer(t))]).then(()=>{this.updateStatus("播放base64音频成功")}).catch(l=>{console.error("播放base64音频失败:",l),this.updateStatus("播放base64音频失败，请检查音频数据和浏览器权限")}),i.addEventListener("ended",()=>{this.updateStatus("base64音频播放完成"),URL.revokeObjectURL(o)}),i.addEventListener("error",l=>{console.error("音频播放错误:",l),this.updateStatus("音频播放出错"),URL.revokeObjectURL(o)})):this.updateStatus("音频处理器未初始化")}catch(t){console.error("处理base64音频失败:",t);const n=t instanceof Error?t.message:String(t);this.updateStatus(`处理base64音频失败: ${n}`)}}getCurrentModelInfo(){return this._live2DManager.getCurrentModel()?{modelName:"Current Model",hasModel:!0,motionGroups:this._live2DManager.getMotionGroups(),expressions:this._live2DManager.getExpressionNames()}:{modelName:"No Model",hasModel:!1,motionGroups:[],expressions:[]}}toggleControlPanel(){const e=document.querySelector(".control-panel");e&&(e.classList.toggle("hidden"),console.log("控制面板显示状态已切换"))}updateStatus(e){const s=document.getElementById("statusText");s&&(s.textContent=e),E.printMessage(`[Control Panel] ${e}`)}initializeModelUpload(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectFileBtn"),t=document.getElementById("uploadBtn"),n=document.getElementById("selectedFileName");s.addEventListener("click",()=>{e.click()}),e.addEventListener("change",o=>{var l;const i=o.target,a=(l=i.files)==null?void 0:l[0];a&&(a.type==="application/zip"||a.name.endsWith(".zip")?(n.textContent=`已选择: ${a.name}`,n.style.display="block",t.style.display="block",this.updateStatus(`已选择文件: ${a.name}`)):(alert("请选择ZIP格式的文件"),i.value=""))}),t.addEventListener("click",()=>{var i;const o=(i=e.files)==null?void 0:i[0];o&&this.uploadModel(o)})}async uploadModel(e){const s=document.getElementById("uploadStatus"),t=document.getElementById("uploadStatusText"),n=document.getElementById("progressBar"),o=document.getElementById("uploadBtn");try{s.style.display="block",s.className="upload-status",t.textContent="正在上传...",n.style.width="0%",o.disabled=!0;const i=new FormData;i.append("modelZip",e);const a=await fetch("http://localhost:3001/api/upload-model",{method:"POST",body:i});if(a.ok){const l=await a.json();if(s.className="upload-status success",t.textContent=`上传成功! 模型 ${l.modelName} 已添加`,n.style.width="100%",l.analysis){const d=[];l.analysis.hasExpressions&&d.push(`表情: ${l.analysis.expressions.length}个`),l.analysis.hasMotions&&d.push(`动作组: ${l.analysis.motionGroups.length}个`),l.analysis.issues&&l.analysis.issues.length>0&&d.push(`问题: ${l.analysis.issues.length}个`),d.length>0&&(t.textContent+=` (${d.join(", ")})`)}await this.refreshModelList(),this.clearFileSelection(),this.notifyMainAppModelUploaded(l.modelName),this.updateStatus(`模型 ${l.modelName} 上传成功`)}else{const l=await a.json();throw new Error(l.error||"上传失败")}}catch(i){const a=i instanceof Error?i.message:"未知错误";s.className="upload-status error",t.textContent=`上传失败: ${a}`,this.updateStatus(`上传失败: ${a}`)}finally{o.disabled=!1}}async refreshModelList(){try{await this.initializeModelSelector(),this.updateStatus("模型列表已更新")}catch(e){console.error("刷新模型列表失败:",e),this.updateStatus("刷新模型列表失败")}}clearFileSelection(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectedFileName"),t=document.getElementById("uploadBtn");e.value="",s.style.display="none",t.style.display="none"}async deleteModel(e){if(confirm(`确定要删除模型 "${e}" 吗？此操作不可撤销。`))try{this.updateStatus(`正在删除模型: ${e}...`);const s=await fetch(`http://localhost:3001/api/models/${e}`,{method:"DELETE"});if(s.ok){await s.json(),this.updateStatus(`模型 ${e} 删除成功`),await this.refreshModelList();const t=document.getElementById("deleteModelBtn");t.style.display="none",this.notifyMainAppModelDeleted(e)}else{const t=await s.json();throw new Error(t.error||"删除失败")}}catch(s){const t=s instanceof Error?s.message:"未知错误";this.updateStatus(`删除失败: ${t}`),alert(`删除模型失败: ${t}`)}}notifyMainAppModelUploaded(e){try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"live2d_model_uploaded",modelName:e},"*"),window.embeddedLive2DManager&&typeof window.embeddedLive2DManager.refreshEmbeddedData=="function"&&window.embeddedLive2DManager.refreshEmbeddedData()}catch(s){console.warn("通知主应用模型上传失败:",s)}}notifyMainAppModelDeleted(e){try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"live2d_model_deleted",modelName:e},"*"),window.embeddedLive2DManager&&typeof window.embeddedLive2DManager.refreshEmbeddedData=="function"&&window.embeddedLive2DManager.refreshEmbeddedData()}catch(s){console.warn("通知主应用模型删除失败:",s)}}release(){M.releaseInstance()}}let h;window.addEventListener("DOMContentLoaded",async()=>{h=new S,await h.initialize(),window.Live2DControlPanel={changeModel:r=>h.changeModel(r),playMotion:(r,e=0)=>h.playMotion(r,e),playExpression:r=>h.playExpression(r),playRandomExpression:()=>h.playRandomExpression(),showBubble:(r,e=!0)=>h.showBubble(r,e),hideBubble:()=>h.hideBubble(),playAudio:r=>h.playAudioWithLipSync(r),playAudioFromBase64:r=>h.playAudioFromBase64(r),uploadModel:r=>h.uploadModel(r),refreshModelList:()=>h.refreshModelList(),deleteModel:r=>h.deleteModel(r),getModelInfo:()=>h.getCurrentModelInfo(),_app:h},window.addEventListener("message",r=>{var e,s,t;if(r.data&&r.data.type==="toggle_panel"){const n=document.querySelector(".control-panel");n&&(n.classList.toggle("hidden"),console.log("控制面板显示状态已切换"));return}if(r.data&&r.data.type==="live2d_api_call"){const{messageId:n,method:o,args:i}=r.data;try{const a=window.Live2DControlPanel;if(a&&typeof a[o]=="function"){const l=a[o](...i);l&&typeof l.then=="function"?l.then(d=>{var u;(u=r.source)==null||u.postMessage({type:"live2d_api_response",messageId:n,success:!0,result:d},"*")}).catch(d=>{var u;(u=r.source)==null||u.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:d.message||"调用失败"},"*")}):(e=r.source)==null||e.postMessage({type:"live2d_api_response",messageId:n,success:!0,result:l},"*")}else(s=r.source)==null||s.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:`方法 ${o} 不存在`},"*")}catch(a){(t=r.source)==null||t.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:a.message||"调用失败"},"*")}}})});window.addEventListener("beforeunload",()=>{h&&h.release()});
//# sourceMappingURL=control-panel-DsGpCrgr.js.map
