#!/usr/bin/env node

/**
 * 测试 Windows 配置脚本
 * 验证所有 Windows 相关的路径和依赖是否正确配置
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 测试 Windows 配置...\n');

// 检查 Windows Node.js
console.log('🔧 检查 Windows Node.js:');
const windowsNodePath = path.join(__dirname, 'node-v23.6.1-windows-x64', 'node.exe');
const windowsNodeExists = fs.existsSync(windowsNodePath);
console.log(`  ${windowsNodeExists ? '✅' : '❌'} Windows Node.js: ${windowsNodePath}`);

if (windowsNodeExists) {
  const stats = fs.statSync(windowsNodePath);
  console.log(`    📋 文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
}

// 检查 Windows NPM
const windowsNpmPath = path.join(__dirname, 'node-v23.6.1-windows-x64', 'npm.cmd');
const windowsNpmExists = fs.existsSync(windowsNpmPath);
console.log(`  ${windowsNpmExists ? '✅' : '❌'} Windows NPM: ${windowsNpmPath}`);

// 检查 Windows NPX
const windowsNpxPath = path.join(__dirname, 'node-v23.6.1-windows-x64', 'npx.cmd');
const windowsNpxExists = fs.existsSync(windowsNpxPath);
console.log(`  ${windowsNpxExists ? '✅' : '❌'} Windows NPX: ${windowsNpxPath}`);

// 检查 UV 工具
console.log('\n🛠️  检查 UV 工具:');
const uvPath = path.join(__dirname, 'app', 'assets', 'bin', 'uv.exe');
const uvExists = fs.existsSync(uvPath);
console.log(`  ${uvExists ? '✅' : '❌'} UV: ${uvPath}`);

const uvxPath = path.join(__dirname, 'app', 'assets', 'bin', 'uvx.exe');
const uvxExists = fs.existsSync(uvxPath);
console.log(`  ${uvxExists ? '✅' : '❌'} UVX: ${uvxPath}`);

const uvwPath = path.join(__dirname, 'app', 'assets', 'bin', 'uvw.exe');
const uvwExists = fs.existsSync(uvwPath);
console.log(`  ${uvwExists ? '✅' : '❌'} UVW: ${uvwPath}`);

// 检查 Live2DAgentServer
const agentServerPath = path.join(__dirname, 'app', 'assets', 'bin', 'Live2DAgentServer.exe');
const agentServerExists = fs.existsSync(agentServerPath);
console.log(`  ${agentServerExists ? '✅' : '❌'} Live2DAgentServer: ${agentServerPath}`);

// 检查 package.json 配置
console.log('\n📦 检查 package.json 配置:');
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// 检查 extraResources
const extraResources = packageJson.build.extraResources;
const hasWindowsNode = extraResources.some(resource => 
  resource.from === 'node-v23.6.1-windows-x64'
);
console.log(`  ${hasWindowsNode ? '✅' : '❌'} extraResources 包含 Windows Node.js`);

// 检查 asarUnpack
const asarUnpack = packageJson.build.asarUnpack;
const hasWindowsNodeUnpack = asarUnpack.includes('node-v23.6.1-windows-x64/**/*');
console.log(`  ${hasWindowsNodeUnpack ? '✅' : '❌'} asarUnpack 包含 Windows Node.js`);

// 检查 Live2D 相关文件
console.log('\n🎭 检查 Live2D 相关文件:');
const live2dServerPath = path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js');
const live2dServerExists = fs.existsSync(live2dServerPath);
console.log(`  ${live2dServerExists ? '✅' : '❌'} Live2D 服务器脚本: ${live2dServerPath}`);

const live2dPackagePath = path.join(__dirname, 'app', 'live2d', 'package.json');
const live2dPackageExists = fs.existsSync(live2dPackagePath);
console.log(`  ${live2dPackageExists ? '✅' : '❌'} Live2D package.json: ${live2dPackagePath}`);

// 总结
console.log('\n📊 配置总结:');
const allChecks = [
  windowsNodeExists,
  windowsNpmExists,
  windowsNpxExists,
  uvExists,
  uvxExists,
  agentServerExists,
  hasWindowsNode,
  hasWindowsNodeUnpack,
  live2dServerExists,
  live2dPackageExists
];

const passedChecks = allChecks.filter(check => check).length;
const totalChecks = allChecks.length;

console.log(`  ✅ 通过检查: ${passedChecks}/${totalChecks}`);

if (passedChecks === totalChecks) {
  console.log('\n🎉 所有 Windows 配置检查通过！');
  console.log('💡 现在可以运行 npm run build 来构建 Windows 版本');
} else {
  console.log('\n❌ 部分配置检查失败，请检查上述问题');
  process.exit(1);
}
