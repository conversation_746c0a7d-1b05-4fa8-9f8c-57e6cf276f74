#!/usr/bin/env node

/**
 * 查找内嵌Node.js的位置
 * 帮助调试为什么Live2D服务器无法启动
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 查找内嵌Node.js位置...\n');

// 显示当前环境信息
console.log('📍 环境信息:');
console.log('  当前工作目录:', process.cwd());
console.log('  __dirname:', __dirname);
console.log('  process.execPath:', process.execPath);
console.log('  process.argv[0]:', process.argv[0]);
console.log('  平台:', process.platform);

// 模拟打包环境的resourcesPath
let resourcesPath;
if (process.env.NODE_ENV === 'production' || process.argv.includes('--simulate-packaged')) {
  // 模拟打包环境
  resourcesPath = path.join(__dirname, 'dist', 'mac', '兴河 AI Assistant.app', 'Contents', 'Resources');
  console.log('  模拟打包环境');
} else {
  // 开发环境
  resourcesPath = __dirname;
  console.log('  开发环境');
}

console.log('  resourcesPath:', resourcesPath);
console.log('');

// 可能的Node.js路径列表
const possiblePaths = [];

if (process.platform === 'win32') {
  possiblePaths.push(
    path.join(resourcesPath, 'bundled_node', 'node.exe'),
    path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'bundled_node', 'node.exe'),
    path.join(__dirname, 'node_modules', 'bundled_node', 'node.exe'),
    path.join(path.dirname(process.execPath), 'bundled_node', 'node.exe'),
    // Windows 特定的内嵌Node.js路径
    path.join(__dirname, 'node-v23.6.1-windows-x64', 'node.exe'),
    path.join(resourcesPath, 'node-v23.6.1-windows-x64', 'node.exe'),
    path.join(resourcesPath, 'app', 'assets', 'bin', 'node.exe'),
    path.join(__dirname, 'app', 'assets', 'bin', 'node.exe')
  );
} else {
  possiblePaths.push(
    path.join(resourcesPath, 'bundled_node', 'node'),
    path.join(resourcesPath, 'app.asar.unpacked', 'node_modules', 'bundled_node', 'node'),
    path.join(__dirname, 'node_modules', 'bundled_node', 'node'),
    path.join(path.dirname(process.execPath), 'bundled_node', 'node'),
    // macOS特殊路径
    path.join(resourcesPath, 'app', 'node_modules', 'bundled_node', 'node'),
    path.join(resourcesPath, '..', 'MacOS', 'bundled_node', 'node'),
    // 更多可能的路径
    path.join(__dirname, 'app', 'assets', 'bin', 'node'),
    path.join(resourcesPath, 'app', 'assets', 'bin', 'node'),
    // 实际的内嵌Node.js路径
    path.join(__dirname, 'node-v23.6.1-darwin-arm64', 'bin', 'node'),
    path.join(resourcesPath, 'node-v23.6.1-darwin-arm64', 'bin', 'node')
  );
}

console.log('🔎 检查可能的Node.js路径:');

let foundNodePath = null;
for (const nodePath of possiblePaths) {
  const exists = fs.existsSync(nodePath);
  console.log(`  ${exists ? '✅' : '❌'} ${nodePath}`);
  
  if (exists && !foundNodePath) {
    foundNodePath = nodePath;
    
    // 检查文件是否可执行
    try {
      const stats = fs.statSync(nodePath);
      const isExecutable = !!(stats.mode & parseInt('111', 8));
      console.log(`    📋 文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
      console.log(`    🔐 可执行权限: ${isExecutable ? '是' : '否'}`);
    } catch (error) {
      console.log(`    ❌ 无法读取文件信息: ${error.message}`);
    }
  }
}

console.log('');

if (foundNodePath) {
  console.log('🎉 找到内嵌Node.js:');
  console.log('  路径:', foundNodePath);
  
  // 测试执行
  console.log('\n🧪 测试Node.js执行:');
  const { spawn } = require('child_process');
  
  const testProcess = spawn(foundNodePath, ['--version'], {
    stdio: 'pipe'
  });
  
  testProcess.stdout.on('data', (data) => {
    console.log('  ✅ Node.js版本:', data.toString().trim());
  });
  
  testProcess.stderr.on('data', (data) => {
    console.log('  ❌ 错误输出:', data.toString().trim());
  });
  
  testProcess.on('error', (err) => {
    console.log('  ❌ 执行失败:', err.message);
  });
  
  testProcess.on('exit', (code) => {
    console.log('  📤 退出码:', code);
    
    if (code === 0) {
      console.log('\n✅ 内嵌Node.js工作正常！');
    } else {
      console.log('\n❌ 内嵌Node.js执行失败');
    }
  });
  
} else {
  console.log('❌ 未找到内嵌Node.js');
  console.log('\n💡 建议检查:');
  console.log('  1. 是否正确安装了bundled_node依赖');
  console.log('  2. 构建配置是否包含了bundled_node');
  console.log('  3. 是否在正确的目录中运行');
  
  // 显示当前目录结构
  console.log('\n📁 当前目录结构:');
  try {
    const items = fs.readdirSync(__dirname);
    items.forEach(item => {
      const itemPath = path.join(__dirname, item);
      const stats = fs.statSync(itemPath);
      console.log(`  ${stats.isDirectory() ? '📁' : '📄'} ${item}`);
    });
  } catch (error) {
    console.log('  ❌ 无法读取目录:', error.message);
  }
}

// 检查Live2D相关文件
console.log('\n🎭 检查Live2D相关文件:');
const live2dPaths = [
  path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js'),
  path.join(__dirname, 'app', 'live2d', 'package.json'),
  path.join(__dirname, 'app', 'live2d', 'node_modules')
];

live2dPaths.forEach(p => {
  const exists = fs.existsSync(p);
  console.log(`  ${exists ? '✅' : '❌'} ${p}`);
});
