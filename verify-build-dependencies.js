#!/usr/bin/env node

/**
 * 验证构建后的应用是否包含Live2D依赖
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证构建后的Live2D依赖...\n');

// 构建后的应用路径
const appPath = path.join(__dirname, 'dist', 'mac-arm64', '兴河 AI Assistant.app');
const resourcesPath = path.join(appPath, 'Contents', 'Resources');
const live2dPath = path.join(resourcesPath, 'app', 'live2d');
const nodeModulesPath = path.join(live2dPath, 'node_modules');

console.log('📍 检查路径:');
console.log('  应用路径:', appPath);
console.log('  Resources路径:', resourcesPath);
console.log('  Live2D路径:', live2dPath);
console.log('  node_modules路径:', nodeModulesPath);

// 检查关键路径是否存在
console.log('\n📁 路径检查:');
const pathChecks = [
  { name: '应用包', path: appPath },
  { name: 'Resources目录', path: resourcesPath },
  { name: 'Live2D目录', path: live2dPath },
  { name: 'Live2D node_modules', path: nodeModulesPath }
];

let allPathsExist = true;
pathChecks.forEach(check => {
  const exists = fs.existsSync(check.path);
  console.log(`  ${exists ? '✅' : '❌'} ${check.name}: ${check.path}`);
  if (!exists) allPathsExist = false;
});

if (!allPathsExist) {
  console.log('\n❌ 某些关键路径不存在，请检查构建配置');
  process.exit(1);
}

// 检查Live2D依赖
console.log('\n📦 检查Live2D依赖:');
const requiredDeps = ['express', 'multer', 'jszip', 'cors'];

if (fs.existsSync(nodeModulesPath)) {
  console.log('✅ Live2D node_modules目录存在');
  
  try {
    const installedDeps = fs.readdirSync(nodeModulesPath);
    console.log(`  📋 已安装依赖数量: ${installedDeps.length}`);
    
    console.log('\n  🔍 检查必需依赖:');
    let allDepsExist = true;
    
    requiredDeps.forEach(dep => {
      const depPath = path.join(nodeModulesPath, dep);
      const exists = fs.existsSync(depPath);
      console.log(`    ${exists ? '✅' : '❌'} ${dep}`);
      
      if (exists) {
        // 检查依赖的package.json
        const depPackageJson = path.join(depPath, 'package.json');
        if (fs.existsSync(depPackageJson)) {
          try {
            const depInfo = JSON.parse(fs.readFileSync(depPackageJson, 'utf8'));
            console.log(`      版本: ${depInfo.version}`);
          } catch (error) {
            console.log(`      ⚠️  无法读取版本信息`);
          }
        }
      } else {
        allDepsExist = false;
      }
    });
    
    if (allDepsExist) {
      console.log('\n✅ 所有必需依赖都存在');
    } else {
      console.log('\n❌ 某些必需依赖缺失');
    }
    
  } catch (error) {
    console.log('❌ 无法读取node_modules目录:', error.message);
  }
} else {
  console.log('❌ Live2D node_modules目录不存在');
}

// 检查内嵌Node.js
console.log('\n🔧 检查内嵌Node.js:');
let nodePath;
if (process.platform === 'win32') {
  nodePath = path.join(resourcesPath, 'node-v23.6.1-windows-x64', 'node.exe');
} else {
  nodePath = path.join(resourcesPath, 'node-v23.6.1-darwin-arm64', 'bin', 'node');
}
const nodeExists = fs.existsSync(nodePath);
console.log(`  ${nodeExists ? '✅' : '❌'} 内嵌Node.js: ${nodePath}`);

if (nodeExists) {
  try {
    const stats = fs.statSync(nodePath);
    const isExecutable = !!(stats.mode & parseInt('111', 8));
    console.log(`    📏 文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    console.log(`    🔐 可执行权限: ${isExecutable ? '是' : '否'}`);
  } catch (error) {
    console.log(`    ❌ 无法读取文件信息: ${error.message}`);
  }
}

// 检查Live2D服务器脚本
console.log('\n📜 检查Live2D服务器脚本:');
const serverScripts = [
  'start-live2d-server.js',
  'server/model-manager.js',
  'control-panel.html'
];

serverScripts.forEach(script => {
  const scriptPath = path.join(live2dPath, script);
  const exists = fs.existsSync(scriptPath);
  console.log(`  ${exists ? '✅' : '❌'} ${script}`);
});

console.log('\n🎯 验证结果:');
if (allPathsExist && fs.existsSync(nodeModulesPath) && nodeExists) {
  console.log('✅ 构建验证通过！Live2D依赖已正确包含');
  console.log('\n📋 下一步:');
  console.log('  1. 安装新构建的应用');
  console.log('  2. 启动应用测试Live2D功能');
  console.log('  3. 访问 http://localhost:3001 验证服务器');
} else {
  console.log('❌ 构建验证失败，请检查构建配置');
  process.exit(1);
}

console.log('\n💡 如果应用启动后仍有问题，请运行:');
if (process.platform === 'win32') {
  console.log('  cd "C:\\Program Files\\兴河 AI Assistant\\resources\\app\\live2d"');
  console.log('  ..\\..\\node-v23.6.1-windows-x64\\node.exe start-live2d-server.js');
} else {
  console.log('  cd "/Applications/兴河 AI Assistant.app/Contents/Resources/app/live2d"');
  console.log('  ../../node-v23.6.1-darwin-arm64/bin/node start-live2d-server.js');
}
console.log('  查看详细错误信息');
