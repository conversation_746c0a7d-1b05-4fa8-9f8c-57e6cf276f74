import{L as M,M as _,a as E,b as w}from"./lappdelegate-BlEL7FXX.js";const v=class v{static getAudioContext(){return this.audioContext||(this.audioContext=new(window.AudioContext||window.webkitAudioContext)),this.audioContext}static detectAudioFormat(e){try{const s=e.replace(/^data:audio\/[^;]+;base64,/,""),t=atob(s),n=new Uint8Array(Math.min(12,t.length));for(let o=0;o<n.length;o++)n[o]=t.charCodeAt(o);if(n.length>=12){const o=String.fromCharCode(n[0],n[1],n[2],n[3]),a=String.fromCharCode(n[8],n[9],n[10],n[11]);if(o==="RIFF"&&a==="WAVE")return"wav"}return n.length>=3&&(n[0]===73&&n[1]===68&&n[2]===51||n[0]===255&&(n[1]&224)===224)?"mp3":"unknown"}catch(s){return console.error("检测音频格式失败:",s),"unknown"}}static base64ToArrayBuffer(e){const s=e.replace(/^data:audio\/[^;]+;base64,/,""),t=atob(s),n=new Uint8Array(t.length);for(let o=0;o<t.length;o++)n[o]=t.charCodeAt(o);return n.buffer}static async convertMp3ToWav(e){try{const t=await this.getAudioContext().decodeAudioData(e.slice(0));return this.audioBufferToWav(t)}catch(s){console.error("MP3转WAV失败:",s);const t=s instanceof Error?s.message:String(s);throw new Error(`MP3转WAV失败: ${t}`)}}static audioBufferToWav(e){const s=e.numberOfChannels,t=e.sampleRate,n=e.length,o=16,a=o/8,i=n*s*a,l=44+i,u=new ArrayBuffer(l),d=new DataView(u);let r=0;this.writeString(d,r,"RIFF"),r+=4,d.setUint32(r,l-8,!0),r+=4,this.writeString(d,r,"WAVE"),r+=4,this.writeString(d,r,"fmt "),r+=4,d.setUint32(r,16,!0),r+=4,d.setUint16(r,1,!0),r+=2,d.setUint16(r,s,!0),r+=2,d.setUint32(r,t,!0),r+=4,d.setUint32(r,t*s*a,!0),r+=4,d.setUint16(r,s*a,!0),r+=2,d.setUint16(r,o,!0),r+=2,this.writeString(d,r,"data"),r+=4,d.setUint32(r,i,!0),r+=4;for(let b=0;b<s;b++){const y=e.getChannelData(b);let p=44+b*a;for(let m=0;m<n;m++){const g=Math.max(-1,Math.min(1,y[m])),f=g<0?g*32768:g*32767;d.setInt16(p,f,!0),p+=s*a}}return u}static writeString(e,s,t){for(let n=0;n<t.length;n++)e.setUint8(s+n,t.charCodeAt(n))}static async processBase64Audio(e){const s=this.detectAudioFormat(e),t=this.base64ToArrayBuffer(e);switch(s){case"wav":return console.log("检测到WAV格式，直接使用"),t;case"mp3":return console.log("检测到MP3格式，转换为WAV"),await this.convertMp3ToWav(t);default:console.warn("未知音频格式，尝试作为MP3处理");try{return await this.convertMp3ToWav(t)}catch{return console.error("作为MP3处理失败，尝试作为WAV处理"),t}}}};v.audioContext=null;let B=v;class S{constructor(){this._currentModelIndex=0,this._speechBubble=null,this._bubbleText=null,this._bubblePosition="center",this._bubbleTimeout=null,this._isDragging=!1,this._dragOffset={x:0,y:0},this._bubbleManuallyPositioned=!1,this._immersiveMode=!1,this._backgroundOpacity=0,this._currentBackgroundFile=null,this._currentBackgroundUrl=null}async initialize(){this._delegate=M.getInstance(),this._delegate.initialize(),this._live2DManager=this._delegate.getLive2DManager(),await this.initializeUI(),this._delegate.run()}async initializeUI(){await this.initializeModelSelector(),this.initializeControlButtons(),this.initializeSpeechBubble(),this.initializeModelUpload(),this.setupModelLoadListener()}async initializeModelSelector(){const e=document.getElementById("modelSelector"),s=document.getElementById("deleteModelBtn"),t=await this._live2DManager.detectAvailableModels();e.innerHTML='<option value="">选择模型...</option>',t.forEach((n,o)=>{const a=document.createElement("option");a.value=o.toString(),a.textContent=`${n.name} ${n.status==="active"?"✓":"⚠️"}`,a.setAttribute("data-model-name",n.name),a.setAttribute("data-has-expressions",n.hasExpressions.toString()),a.setAttribute("data-has-motions",n.hasMotions.toString()),e.appendChild(a)}),e.addEventListener("change",async n=>{const o=n.target,a=parseInt(o.value);if(!isNaN(a)&&t[a]){const i=t[a];await this.switchModelByName(i.name),s.style.display="block",s.setAttribute("data-model-name",i.name),this.displayModelInfo(i)}else s.style.display="none",this.hideModelInfo()}),s.addEventListener("click",()=>{const n=s.getAttribute("data-model-name");n&&this.deleteModel(n)}),t.length>0&&(e.value="0",await this.switchModelByName(t[0].name),s.style.display="block",s.setAttribute("data-model-name",t[0].name),this.displayModelInfo(t[0]))}initializeControlButtons(){const e=document.getElementById("panelToggleBtn");e==null||e.addEventListener("click",()=>{this.toggleControlPanel()});const s=document.getElementById("randomMotionBtn");s==null||s.addEventListener("click",()=>{this._live2DManager.playRandomMotion(_),this.updateStatus("播放随机动作")});const t=document.getElementById("stopMotionBtn");t==null||t.addEventListener("click",()=>{this._live2DManager.stopAllMotions(),this.updateStatus("停止所有动作")});const n=document.getElementById("randomExpressionBtn");n==null||n.addEventListener("click",()=>{this._live2DManager.setRandomExpression(),this.updateStatus("设置随机表情")});const o=document.getElementById("resetExpressionBtn");o==null||o.addEventListener("click",()=>{const p=this._live2DManager.getCurrentModel();p&&(p.getExpressionManager().stopAllMotions(),this.updateStatus("重置表情"))});const a=document.getElementById("testLipSyncBtn");a==null||a.addEventListener("click",()=>{this.testLipSync()});const i=document.getElementById("resetTransformBtn");i==null||i.addEventListener("click",()=>{this.resetTransform()});const l=document.getElementById("backgroundOpacitySlider"),u=document.getElementById("opacityValue");l==null||l.addEventListener("input",p=>{const m=p.target,g=parseInt(m.value);this._backgroundOpacity=g,u&&(u.textContent=`${g}%`),this.updateBackgroundOpacity()});const d=document.getElementById("selectBackgroundBtn"),r=document.getElementById("backgroundFileInput");d==null||d.addEventListener("click",()=>{r==null||r.click()}),r==null||r.addEventListener("change",p=>{var f;const g=(f=p.target.files)==null?void 0:f[0];g&&this.handleBackgroundFileSelect(g)});const b=document.getElementById("applyBackgroundBtn");b==null||b.addEventListener("click",()=>{this.applyBackground()});const y=document.getElementById("clearBackgroundBtn");y==null||y.addEventListener("click",()=>{this.clearBackground()})}initializeSpeechBubble(){this._speechBubble=document.getElementById("speechBubble"),this._bubbleText=document.getElementById("bubbleText");const e=document.getElementById("showBubbleBtn");e==null||e.addEventListener("click",()=>{this.showSpeechBubble()});const s=document.getElementById("hideBubbleBtn");s==null||s.addEventListener("click",()=>{this.hideSpeechBubble()});const t=document.getElementById("bubbleTextInput");t==null||t.addEventListener("input",o=>{const a=o.target;this._bubbleText&&(this._bubbleText.textContent=a.value)});const n=document.querySelectorAll(".position-button");n.forEach(o=>{o.addEventListener("click",a=>{const i=a.target,l=i.getAttribute("data-position");l&&(n.forEach(u=>u.classList.remove("active")),i.classList.add("active"),this.setBubblePosition(l))})}),this.setBubblePosition(this._bubblePosition),this.setupBubbleDragging(),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="欢迎使用Live2D控制面板！",this.showSpeechBubble())},3e3)}setupModelLoadListener(){const e=async()=>{this._live2DManager.isModelReady()?(this.updateModelInfo(),await this.updateMotionButtons(),await this.updateExpressionButtons(),this.updateStatus("模型加载完成")):setTimeout(e,100)};e()}switchModel(e){this._currentModelIndex=e,this._live2DManager.switchToModel(e),this.updateStatus("正在加载模型..."),this.clearButtons(),this.setupModelLoadListener()}async switchModelByName(e){this._live2DManager.loadModelByName(e),this.updateStatus(`正在加载模型: ${e}...`),this.clearButtons(),this.setupModelLoadListener();try{const s=await this._live2DManager.validateModel(e);s&&!s.isValid&&this.updateStatus(`模型 ${e} 存在问题: ${s.analysis.issues.join(", ")}`)}catch(s){console.warn("模型验证失败:",s)}}displayModelInfo(e){const s=document.getElementById("modelInfo"),t=document.getElementById("motionCount"),n=document.getElementById("expressionCount");s&&t&&n&&(s.style.display="block",t.textContent=e.motionGroups?e.motionGroups.length.toString():"0",n.textContent=e.expressions?e.expressions.length.toString():"0")}hideModelInfo(){const e=document.getElementById("modelInfo");e&&(e.style.display="none")}getCurrentModelName(){const e=document.getElementById("modelSelector");return e&&e.selectedIndex>0?e.options[e.selectedIndex].getAttribute("data-model-name"):null}updateModelInfo(){const e=this._live2DManager.getCurrentModel();if(!e||!e.getModelSetting())return;const s=this._live2DManager.getMotionGroups(),t=this._live2DManager.getExpressionNames();let n=0;s.forEach(l=>{n+=this._live2DManager.getMotionCount(l)});const o=document.getElementById("motionCount"),a=document.getElementById("expressionCount"),i=document.getElementById("modelInfo");o&&(o.textContent=n.toString()),a&&(a.textContent=t.length.toString()),i&&(i.style.display="grid")}async updateMotionButtons(){const e=document.getElementById("motionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载动作...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelMotionsFromServer(n);e.innerHTML="";for(const a in o)o.hasOwnProperty(a)&&o[a].forEach((l,u)=>{if(l.exists){const d=document.createElement("button");d.className="control-button motion",d.textContent=`${a} ${u+1}`,d.addEventListener("click",()=>{this._live2DManager.playMotion(a,u),this.updateStatus(`播放动作: ${a} ${u+1}`)}),e.appendChild(d)}});e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>');return}}}catch(t){console.warn("从服务器获取动作信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getMotionGroups().forEach(t=>{const n=this._live2DManager.getMotionCount(t);for(let o=0;o<n;o++){const a=document.createElement("button");a.className="control-button motion",a.textContent=`${t} ${o+1}`,a.addEventListener("click",()=>{this._live2DManager.playMotion(t,o),this.updateStatus(`播放动作: ${t} ${o+1}`)}),e.appendChild(a)}}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>')}async updateExpressionButtons(){const e=document.getElementById("expressionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载表情...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelExpressionsFromServer(n);e.innerHTML="",o.forEach(a=>{const i=document.createElement("button");i.className="control-button expression",i.textContent=a,i.addEventListener("click",()=>{this._live2DManager.setExpression(a),this.updateStatus(`设置表情: ${a}`)}),e.appendChild(i)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>');return}}}catch(t){console.warn("从服务器获取表情信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getExpressionNames().forEach(t=>{const n=document.createElement("button");n.className="control-button expression",n.textContent=t,n.addEventListener("click",()=>{this._live2DManager.setExpression(t),this.updateStatus(`设置表情: ${t}`)}),e.appendChild(n)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>')}clearButtons(){const e=document.getElementById("motionButtons"),s=document.getElementById("expressionButtons"),t=document.getElementById("modelInfo");e&&(e.innerHTML='<div class="loading">加载中...</div>'),s&&(s.innerHTML='<div class="loading">加载中...</div>'),t&&(t.style.display="none")}showSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.add("show"),this.updateStatus("显示文字气泡"),this._bubbleTimeout=window.setTimeout(()=>{this.hideSpeechBubble()},5e3))}hideSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.remove("show"),this.updateStatus("隐藏文字气泡"))}setupBubbleDragging(){this._speechBubble&&(this._speechBubble.addEventListener("mousedown",e=>{this._isDragging=!0;const s=document.querySelector(".canvas-container");if(!s)return;const t=s.getBoundingClientRect(),n=this._speechBubble.getBoundingClientRect(),o=n.left-t.left,a=n.top-t.top;this._dragOffset.x=e.clientX-t.left-o,this._dragOffset.y=e.clientY-t.top-a,e.preventDefault()}),document.addEventListener("mousemove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=document.querySelector(".canvas-container");if(!s)return;const t=s.getBoundingClientRect();let n=e.clientX-t.left-this._dragOffset.x,o=e.clientY-t.top-this._dragOffset.y;const a=this._speechBubble.offsetWidth,i=this._speechBubble.offsetHeight;n=Math.max(10,Math.min(n,t.width-a-10)),o=Math.max(10,Math.min(o,t.height-i-10)),this._speechBubble.style.left=`${n}px`,this._speechBubble.style.top=`${o}px`,this._speechBubble.style.right="auto",this._speechBubble.style.bottom="auto",this._speechBubble.style.transform="none",this._speechBubble.classList.add("manually-positioned")}),document.addEventListener("mouseup",()=>{this._isDragging&&(this._isDragging=!1)}),this._speechBubble.addEventListener("touchstart",e=>{this._isDragging=!0;const s=e.touches[0],t=document.querySelector(".canvas-container");if(!t)return;const n=t.getBoundingClientRect(),o=this._speechBubble.getBoundingClientRect(),a=o.left-n.left,i=o.top-n.top;this._dragOffset.x=s.clientX-n.left-a,this._dragOffset.y=s.clientY-n.top-i,e.preventDefault()}),document.addEventListener("touchmove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=e.touches[0],t=document.querySelector(".canvas-container");if(!t)return;const n=t.getBoundingClientRect();let o=s.clientX-n.left-this._dragOffset.x,a=s.clientY-n.top-this._dragOffset.y;const i=this._speechBubble.offsetWidth,l=this._speechBubble.offsetHeight;o=Math.max(10,Math.min(o,n.width-i-10)),a=Math.max(10,Math.min(a,n.height-l-10)),this._speechBubble.style.left=`${o}px`,this._speechBubble.style.top=`${a}px`,this._speechBubble.style.right="auto",this._speechBubble.style.bottom="auto",this._speechBubble.style.transform="none",this._speechBubble.classList.add("manually-positioned"),e.preventDefault()}),document.addEventListener("touchend",()=>{this._isDragging&&(this._isDragging=!1)}))}setBubblePosition(e){if(this._speechBubble&&!this._bubbleManuallyPositioned)switch(this._bubblePosition=e,this._speechBubble.classList.remove("top","bottom","left","right"),this._speechBubble.style.left="",this._speechBubble.style.top="",this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform="",e){case"bottom-left":this._speechBubble.style.left="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom");break;case"bottom-right":this._speechBubble.style.right="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom","right");break;case"top-left":this._speechBubble.style.left="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top");break;case"top-right":this._speechBubble.style.right="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top","right");break;case"center":default:this._speechBubble.style.left="50%",this._speechBubble.style.top="50%",this._speechBubble.style.transform="translate(-50%, -50%)";break}}resetTransform(){try{const e=this._live2DManager;e?e.getCurrentModel()?(window.parent.postMessage({type:"live2d_reset_transform"},"*"),this.updateStatus("已重置Live2D模型位置和缩放")):this.updateStatus("没有找到当前模型"):this.updateStatus("Live2D管理器未初始化")}catch(e){console.error("重置变换失败:",e),this.updateStatus("重置变换失败")}}testLipSync(){const e=this._live2DManager.getCurrentModel();if(!e){this.updateStatus("请先选择一个模型");return}this._bubbleText&&(this._bubbleText.textContent="正在测试口型同步功能...",this.showSpeechBubble());try{const s="../../Resources/测试.wav";e._lipsync=!0;const t=new Audio(s);t.volume=.8;const n=e._wavFileHandler;n?(Promise.all([t.play(),Promise.resolve(n.start(s))]).then(()=>{this.updateStatus("开始播放测试音频，观察口型同步效果"),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="口型同步测试中，请观察嘴部动画！")},1e3)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("口型同步测试完成"),this._bubbleText&&(this._bubbleText.textContent="口型同步测试完成！")})):this.updateStatus("音频处理器未初始化")}catch(s){console.error("测试口型同步失败:",s),this.updateStatus("测试口型同步失败，请检查音频文件")}}async changeModel(e){try{if(typeof e=="number"){e>=0&&e<E?(this._live2DManager.switchToModel(e),this.updateStatus(`切换到模型 ${e+1}`)):this.updateStatus(`无效的模型索引: ${e}`);return}if(typeof e=="string"){const s=await this._live2DManager.detectAvailableModels();s.find(n=>n.name===e)?(await this.switchModelByName(e),this.updateStatus(`切换到模型: ${e}`)):(this.updateStatus(`无效的模型名称: ${e}`),console.warn(`[Control Panel] 无效的模型名称: ${e}`),console.log("[Control Panel] 可用模型:",s.map(n=>n.name)));return}this.updateStatus(`无效的模型标识符类型: ${typeof e}`)}catch(s){console.error("[Control Panel] 切换模型失败:",s);const t=s instanceof Error?s.message:String(s);this.updateStatus(`切换模型失败: ${t}`)}}playMotion(e,s=0){this._live2DManager.getCurrentModel()?(this._live2DManager.playMotion(e,s),this.updateStatus(`播放动作: ${e} ${s+1}`)):this.updateStatus("请先选择一个模型")}playExpression(e){const s=this._live2DManager.getExpressionNames();try{let t=null;if(typeof e=="number")if(e>=0&&e<s.length)t=s[e];else{this.updateStatus(`无效的表情索引: ${e}`);return}if(typeof e=="string")if(s.includes(e))t=e;else{this.updateStatus(`无效的表情ID: ${e}`),console.warn(`[Control Panel] 无效的表情ID: ${e}`),console.log("[Control Panel] 可用表情:",s);return}t?(this._live2DManager.setExpression(t),this.updateStatus(`播放表情: ${t}`)):this.updateStatus(`无效的表情标识符类型: ${typeof e}`)}catch(t){console.error("[Control Panel] 播放表情失败:",t);const n=t instanceof Error?t.message:String(t);this.updateStatus(`播放表情失败: ${n}`)}}playRandomExpression(){this._live2DManager.setRandomExpression(),this.updateStatus("播放随机表情")}showBubble(e,s=!0){if(this._bubbleText){this._bubbleText.textContent=e;const t=document.getElementById("bubbleTextInput");t&&(t.value=e)}this.showSpeechBubble(),s||this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null)}hideBubble(){this.hideSpeechBubble()}playAudioWithLipSync(e){const s=this._live2DManager.getCurrentModel();if(!s){this.updateStatus("请先选择一个模型");return}try{s._lipsync=!0;const t=new Audio(`../../Resources/${e}`);t.volume=.8;const n=s._wavFileHandler;n?(Promise.all([t.play(),Promise.resolve(n.start(`../../Resources/${e}`))]).then(()=>{this.updateStatus(`播放音频: ${e}`)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("音频播放完成")})):this.updateStatus("音频处理器未初始化")}catch(t){console.error("播放音频失败:",t),this.updateStatus("播放音频失败，请检查音频文件")}}async playAudioFromBase64(e){const s=this._live2DManager.getCurrentModel();if(!s){this.updateStatus("请先选择一个模型");return}try{this.updateStatus("正在处理音频数据...");const t=await B.processBase64Audio(e);s._lipsync=!0;const n=new Blob([t],{type:"audio/wav"}),o=URL.createObjectURL(n),a=new Audio(o);a.volume=.8;const i=s._wavFileHandler;i?(Promise.all([a.play(),Promise.resolve(i.startFromArrayBuffer(t))]).then(()=>{this.updateStatus("播放base64音频成功")}).catch(l=>{console.error("播放base64音频失败:",l),this.updateStatus("播放base64音频失败，请检查音频数据和浏览器权限")}),a.addEventListener("ended",()=>{this.updateStatus("base64音频播放完成"),URL.revokeObjectURL(o)}),a.addEventListener("error",l=>{console.error("音频播放错误:",l),this.updateStatus("音频播放出错"),URL.revokeObjectURL(o)})):this.updateStatus("音频处理器未初始化")}catch(t){console.error("处理base64音频失败:",t);const n=t instanceof Error?t.message:String(t);this.updateStatus(`处理base64音频失败: ${n}`)}}getCurrentModelInfo(){return this._live2DManager.getCurrentModel()?{modelName:"Current Model",hasModel:!0,motionGroups:this._live2DManager.getMotionGroups(),expressions:this._live2DManager.getExpressionNames()}:{modelName:"No Model",hasModel:!1,motionGroups:[],expressions:[]}}toggleControlPanel(){const e=document.querySelector(".control-panel");e&&(e.classList.toggle("hidden"),console.log("控制面板显示状态已切换"))}updateStatus(e){const s=document.getElementById("statusText");s&&(s.textContent=e),w.printMessage(`[Control Panel] ${e}`)}initializeModelUpload(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectFileBtn"),t=document.getElementById("uploadBtn"),n=document.getElementById("selectedFileName");s.addEventListener("click",()=>{e.click()}),e.addEventListener("change",o=>{var l;const a=o.target,i=(l=a.files)==null?void 0:l[0];i&&(i.type==="application/zip"||i.name.endsWith(".zip")?(n.textContent=`已选择: ${i.name}`,n.style.display="block",t.style.display="block",this.updateStatus(`已选择文件: ${i.name}`)):(alert("请选择ZIP格式的文件"),a.value=""))}),t.addEventListener("click",()=>{var a;const o=(a=e.files)==null?void 0:a[0];o&&this.uploadModel(o)})}async uploadModel(e){const s=document.getElementById("uploadStatus"),t=document.getElementById("uploadStatusText"),n=document.getElementById("progressBar"),o=document.getElementById("uploadBtn");try{s.style.display="block",s.className="upload-status",t.textContent="正在上传...",n.style.width="0%",o.disabled=!0;const a=new FormData;a.append("modelZip",e);const i=await fetch("http://localhost:3001/api/upload-model",{method:"POST",body:a});if(i.ok){const l=await i.json();if(s.className="upload-status success",t.textContent=`上传成功! 模型 ${l.modelName} 已添加`,n.style.width="100%",l.analysis){const u=[];l.analysis.hasExpressions&&u.push(`表情: ${l.analysis.expressions.length}个`),l.analysis.hasMotions&&u.push(`动作组: ${l.analysis.motionGroups.length}个`),l.analysis.issues&&l.analysis.issues.length>0&&u.push(`问题: ${l.analysis.issues.length}个`),u.length>0&&(t.textContent+=` (${u.join(", ")})`)}await this.refreshModelList(),this.clearFileSelection(),this.notifyMainAppModelUploaded(l.modelName),this.updateStatus(`模型 ${l.modelName} 上传成功`)}else{const l=await i.json();throw new Error(l.error||"上传失败")}}catch(a){const i=a instanceof Error?a.message:"未知错误";s.className="upload-status error",t.textContent=`上传失败: ${i}`,this.updateStatus(`上传失败: ${i}`)}finally{o.disabled=!1}}async refreshModelList(){try{await this.initializeModelSelector(),this.updateStatus("模型列表已更新")}catch(e){console.error("刷新模型列表失败:",e),this.updateStatus("刷新模型列表失败")}}clearFileSelection(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectedFileName"),t=document.getElementById("uploadBtn");e.value="",s.style.display="none",t.style.display="none"}async deleteModel(e){if(confirm(`确定要删除模型 "${e}" 吗？此操作不可撤销。`))try{this.updateStatus(`正在删除模型: ${e}...`);const s=await fetch(`http://localhost:3001/api/models/${e}`,{method:"DELETE"});if(s.ok){await s.json(),this.updateStatus(`模型 ${e} 删除成功`),await this.refreshModelList();const t=document.getElementById("deleteModelBtn");t.style.display="none",this.notifyMainAppModelDeleted(e)}else{const t=await s.json();throw new Error(t.error||"删除失败")}}catch(s){const t=s instanceof Error?s.message:"未知错误";this.updateStatus(`删除失败: ${t}`),alert(`删除模型失败: ${t}`)}}notifyMainAppModelUploaded(e){try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"live2d_model_uploaded",modelName:e},"*"),window.embeddedLive2DManager&&typeof window.embeddedLive2DManager.refreshEmbeddedData=="function"&&window.embeddedLive2DManager.refreshEmbeddedData()}catch(s){console.warn("通知主应用模型上传失败:",s)}}notifyMainAppModelDeleted(e){try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"live2d_model_deleted",modelName:e},"*"),window.embeddedLive2DManager&&typeof window.embeddedLive2DManager.refreshEmbeddedData=="function"&&window.embeddedLive2DManager.refreshEmbeddedData()}catch(s){console.warn("通知主应用模型删除失败:",s)}}toggleImmersiveMode(){this._immersiveMode=!this._immersiveMode;const e=document.getElementById("backgroundOpacityControl");this._immersiveMode?(e==null||e.style.setProperty("display","block"),this.updateLive2DBackgroundOpacity(),this.updateStatus("沉浸模式已开启 - 可调整背景透明度")):(e==null||e.style.setProperty("display","none"),this.clearBackgroundInternal(),this.setLive2DBackgroundTransparent(),this.updateStatus("沉浸模式已关闭 - 背景已清除并设为完全透明"))}updateLive2DBackgroundOpacity(){if(!this._immersiveMode)return;const e=document.body;if(e.style.backgroundImage||document.getElementById("live2d-background-video"))this.updateBackgroundOverlay();else{const t=this._backgroundOpacity/100;e.style.backgroundColor=`rgba(255, 255, 255, ${t})`}}setLive2DBackgroundTransparent(){const e=document.body;e.style.backgroundColor="transparent",this.removeBackgroundOverlay()}updateBackgroundOverlay(){if(this.removeBackgroundOverlay(),this._backgroundOpacity===0)return;const e=document.createElement("div");e.id="live2d-background-overlay",e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.width="100vw",e.style.height="100vh",e.style.pointerEvents="none",e.style.zIndex="-5";const s=this._backgroundOpacity/100;e.style.backgroundColor=`rgba(255, 255, 255, ${s})`,document.body.appendChild(e)}removeBackgroundOverlay(){const e=document.getElementById("live2d-background-overlay");e&&e.remove()}updateBackgroundOpacity(){this._immersiveMode&&this.updateLive2DBackgroundOpacity()}setBackgroundOpacity(e){if(e<0||e>100){console.warn("透明度值应在0-100之间");return}this._backgroundOpacity=e;const s=document.getElementById("backgroundOpacitySlider"),t=document.getElementById("opacityValue");s&&(s.value=e.toString()),t&&(t.textContent=`${e}%`),this._immersiveMode&&this.updateBackgroundOpacity()}handleBackgroundFileSelect(e){this._currentBackgroundFile=e;const s=document.getElementById("backgroundFileName"),t=document.getElementById("applyBackgroundBtn");s&&(s.textContent=`已选择: ${e.name}`,s.style.display="block"),t&&(t.style.display="inline-block"),this.updateStatus(`已选择背景文件: ${e.name}`)}applyBackground(){if(!this._currentBackgroundFile){this.updateStatus("请先选择背景文件");return}this._currentBackgroundUrl&&URL.revokeObjectURL(this._currentBackgroundUrl),this._currentBackgroundUrl=URL.createObjectURL(this._currentBackgroundFile),this.setBackgroundFromUrl(this._currentBackgroundUrl,this._currentBackgroundFile.type),this.updateStatus(`背景已应用: ${this._currentBackgroundFile.name}`),this.showBackgroundStatus("success","背景设置成功")}setBackgroundFromUrl(e,s){this.clearBackgroundInternal();const t=document.body;if(s&&s.startsWith("video/")){const n=document.createElement("video");n.src=e,n.autoplay=!0,n.loop=!0,n.muted=!0,n.style.position="fixed",n.style.top="0",n.style.left="0",n.style.width="100vw",n.style.height="100vh",n.style.objectFit="cover",n.style.zIndex="-10",n.style.pointerEvents="none",n.id="live2d-background-video",t.appendChild(n),this.updateStatus("视频背景已设置到Live2D背景")}else t.style.backgroundImage=`url(${e})`,t.style.backgroundSize="cover",t.style.backgroundPosition="center",t.style.backgroundRepeat="no-repeat",t.style.backgroundAttachment="fixed",this.updateStatus("图片背景已设置到Live2D背景")}clearBackground(){this.clearBackgroundInternal();const e=document.getElementById("backgroundFileName"),s=document.getElementById("applyBackgroundBtn"),t=document.getElementById("backgroundFileInput");e&&(e.style.display="none"),s&&(s.style.display="none"),t&&(t.value=""),this._currentBackgroundUrl&&(URL.revokeObjectURL(this._currentBackgroundUrl),this._currentBackgroundUrl=null),this._currentBackgroundFile=null,this.updateStatus("背景已清除"),this.showBackgroundStatus("success","背景已清除")}clearBackgroundInternal(){const e=document.body;e.style.backgroundImage="",e.style.backgroundSize="",e.style.backgroundPosition="",e.style.backgroundRepeat="",e.style.backgroundAttachment="";const s=document.getElementById("live2d-background-video");s&&s.remove(),this.removeBackgroundOverlay(),this._immersiveMode?this.updateLive2DBackgroundOpacity():e.style.backgroundColor="transparent"}showBackgroundStatus(e,s){const t=document.getElementById("backgroundStatus"),n=document.getElementById("backgroundStatusText");t&&n&&(t.className=`upload-status ${e}`,n.textContent=s,t.style.display="block",setTimeout(()=>{t.style.display="none"},3e3))}release(){this._currentBackgroundUrl&&URL.revokeObjectURL(this._currentBackgroundUrl),M.releaseInstance()}}let h;window.addEventListener("DOMContentLoaded",async()=>{h=new S,await h.initialize(),window.Live2DControlPanel={changeModel:c=>h.changeModel(c),playMotion:(c,e=0)=>h.playMotion(c,e),playExpression:c=>h.playExpression(c),playRandomExpression:()=>h.playRandomExpression(),showBubble:(c,e=!0)=>h.showBubble(c,e),hideBubble:()=>h.hideBubble(),playAudio:c=>h.playAudioWithLipSync(c),playAudioFromBase64:c=>h.playAudioFromBase64(c),uploadModel:c=>h.uploadModel(c),refreshModelList:()=>h.refreshModelList(),deleteModel:c=>h.deleteModel(c),getModelInfo:()=>h.getCurrentModelInfo(),toggleImmersiveMode:()=>h.toggleImmersiveMode(),setBackgroundOpacity:c=>h.setBackgroundOpacity(c),setBackgroundFromUrl:(c,e)=>h.setBackgroundFromUrl(c,e),clearBackground:()=>h.clearBackground(),_app:h},window.addEventListener("message",c=>{var e,s,t;if(c.data&&c.data.type==="toggle_panel"){const n=document.querySelector(".control-panel");n&&(n.classList.toggle("hidden"),console.log("控制面板显示状态已切换"));return}if(c.data&&c.data.type==="live2d_api_call"){const{messageId:n,method:o,args:a}=c.data;try{const i=window.Live2DControlPanel;if(i&&typeof i[o]=="function"){const l=i[o](...a);l&&typeof l.then=="function"?l.then(u=>{var d;(d=c.source)==null||d.postMessage({type:"live2d_api_response",messageId:n,success:!0,result:u},"*")}).catch(u=>{var d;(d=c.source)==null||d.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:u.message||"调用失败"},"*")}):(e=c.source)==null||e.postMessage({type:"live2d_api_response",messageId:n,success:!0,result:l},"*")}else(s=c.source)==null||s.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:`方法 ${o} 不存在`},"*")}catch(i){(t=c.source)==null||t.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:i.message||"调用失败"},"*")}}})});window.addEventListener("beforeunload",()=>{h&&h.release()});
//# sourceMappingURL=control-panel-DjCowJXe.js.map
