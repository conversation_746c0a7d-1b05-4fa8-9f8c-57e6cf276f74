import{L as h,M as b,a as p,b as m}from"./lappdelegate-CcqY0PJG.js";class g{constructor(){this._currentModelIndex=0,this._speechBubble=null,this._bubbleText=null,this._bubblePosition="bottom-left",this._bubbleTimeout=null,this._isDragging=!1,this._dragOffset={x:0,y:0}}async initialize(){this._delegate=h.getInstance(),this._delegate.initialize(),this._live2DManager=this._delegate.getLive2DManager(),await this.initializeUI(),this._delegate.run()}async initializeUI(){await this.initializeModelSelector(),this.initializeControlButtons(),this.initializeSpeechBubble(),this.initializeModelUpload(),this.setupModelLoadListener()}async initializeModelSelector(){const e=document.getElementById("modelSelector"),s=document.getElementById("deleteModelBtn"),t=await this._live2DManager.detectAvailableModels();e.innerHTML='<option value="">选择模型...</option>',t.forEach((n,o)=>{const i=document.createElement("option");i.value=o.toString(),i.textContent=`${n.name} ${n.status==="active"?"✓":"⚠️"}`,i.setAttribute("data-model-name",n.name),i.setAttribute("data-has-expressions",n.hasExpressions.toString()),i.setAttribute("data-has-motions",n.hasMotions.toString()),e.appendChild(i)}),e.addEventListener("change",async n=>{const o=n.target,i=parseInt(o.value);if(!isNaN(i)&&t[i]){const l=t[i];await this.switchModelByName(l.name),s.style.display="block",s.setAttribute("data-model-name",l.name),this.displayModelInfo(l)}else s.style.display="none",this.hideModelInfo()}),s.addEventListener("click",()=>{const n=s.getAttribute("data-model-name");n&&this.deleteModel(n)}),t.length>0&&(e.value="0",await this.switchModelByName(t[0].name),s.style.display="block",s.setAttribute("data-model-name",t[0].name),this.displayModelInfo(t[0]))}initializeControlButtons(){const e=document.getElementById("randomMotionBtn");e==null||e.addEventListener("click",()=>{this._live2DManager.playRandomMotion(b),this.updateStatus("播放随机动作")});const s=document.getElementById("stopMotionBtn");s==null||s.addEventListener("click",()=>{this._live2DManager.stopAllMotions(),this.updateStatus("停止所有动作")});const t=document.getElementById("randomExpressionBtn");t==null||t.addEventListener("click",()=>{this._live2DManager.setRandomExpression(),this.updateStatus("设置随机表情")});const n=document.getElementById("resetExpressionBtn");n==null||n.addEventListener("click",()=>{const i=this._live2DManager.getCurrentModel();i&&(i.getExpressionManager().stopAllMotions(),this.updateStatus("重置表情"))});const o=document.getElementById("testLipSyncBtn");o==null||o.addEventListener("click",()=>{this.testLipSync()})}initializeSpeechBubble(){this._speechBubble=document.getElementById("speechBubble"),this._bubbleText=document.getElementById("bubbleText");const e=document.getElementById("showBubbleBtn");e==null||e.addEventListener("click",()=>{this.showSpeechBubble()});const s=document.getElementById("hideBubbleBtn");s==null||s.addEventListener("click",()=>{this.hideSpeechBubble()});const t=document.getElementById("bubbleTextInput");t==null||t.addEventListener("input",o=>{const i=o.target;this._bubbleText&&(this._bubbleText.textContent=i.value)});const n=document.querySelectorAll(".position-button");n.forEach(o=>{o.addEventListener("click",i=>{const l=i.target,a=l.getAttribute("data-position");a&&(n.forEach(u=>u.classList.remove("active")),l.classList.add("active"),this.setBubblePosition(a))})}),this.setBubblePosition(this._bubblePosition),this.setupBubbleDragging(),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="欢迎使用Live2D控制面板！",this.showSpeechBubble())},3e3)}setupModelLoadListener(){const e=async()=>{this._live2DManager.isModelReady()?(this.updateModelInfo(),await this.updateMotionButtons(),await this.updateExpressionButtons(),this.updateStatus("模型加载完成")):setTimeout(e,100)};e()}switchModel(e){this._currentModelIndex=e,this._live2DManager.switchToModel(e),this.updateStatus("正在加载模型..."),this.clearButtons(),this.setupModelLoadListener()}async switchModelByName(e){this._live2DManager.loadModelByName(e),this.updateStatus(`正在加载模型: ${e}...`),this.clearButtons(),this.setupModelLoadListener();try{const s=await this._live2DManager.validateModel(e);s&&!s.isValid&&this.updateStatus(`模型 ${e} 存在问题: ${s.analysis.issues.join(", ")}`)}catch(s){console.warn("模型验证失败:",s)}}displayModelInfo(e){const s=document.getElementById("modelInfo"),t=document.getElementById("motionCount"),n=document.getElementById("expressionCount");s&&t&&n&&(s.style.display="block",t.textContent=e.motionGroups?e.motionGroups.length.toString():"0",n.textContent=e.expressions?e.expressions.length.toString():"0")}hideModelInfo(){const e=document.getElementById("modelInfo");e&&(e.style.display="none")}getCurrentModelName(){const e=document.getElementById("modelSelector");return e&&e.selectedIndex>0?e.options[e.selectedIndex].getAttribute("data-model-name"):null}updateModelInfo(){const e=this._live2DManager.getCurrentModel();if(!e||!e.getModelSetting())return;const s=this._live2DManager.getMotionGroups(),t=this._live2DManager.getExpressionNames();let n=0;s.forEach(a=>{n+=this._live2DManager.getMotionCount(a)});const o=document.getElementById("motionCount"),i=document.getElementById("expressionCount"),l=document.getElementById("modelInfo");o&&(o.textContent=n.toString()),i&&(i.textContent=t.length.toString()),l&&(l.style.display="grid")}async updateMotionButtons(){const e=document.getElementById("motionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载动作...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelMotionsFromServer(n);e.innerHTML="";for(const i in o)o.hasOwnProperty(i)&&o[i].forEach((a,u)=>{if(a.exists){const c=document.createElement("button");c.className="control-button motion",c.textContent=`${i} ${u+1}`,c.addEventListener("click",()=>{this._live2DManager.playMotion(i,u),this.updateStatus(`播放动作: ${i} ${u+1}`),this.showMotionBubble(i,u)}),e.appendChild(c)}});e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>');return}}}catch(t){console.warn("从服务器获取动作信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getMotionGroups().forEach(t=>{const n=this._live2DManager.getMotionCount(t);for(let o=0;o<n;o++){const i=document.createElement("button");i.className="control-button motion",i.textContent=`${t} ${o+1}`,i.addEventListener("click",()=>{this._live2DManager.playMotion(t,o),this.updateStatus(`播放动作: ${t} ${o+1}`),this.showMotionBubble(t,o)}),e.appendChild(i)}}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>')}async updateExpressionButtons(){const e=document.getElementById("expressionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载表情...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelExpressionsFromServer(n);e.innerHTML="",o.forEach(i=>{const l=document.createElement("button");l.className="control-button expression",l.textContent=i,l.addEventListener("click",()=>{this._live2DManager.setExpression(i),this.updateStatus(`设置表情: ${i}`)}),e.appendChild(l)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>');return}}}catch(t){console.warn("从服务器获取表情信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getExpressionNames().forEach(t=>{const n=document.createElement("button");n.className="control-button expression",n.textContent=t,n.addEventListener("click",()=>{this._live2DManager.setExpression(t),this.updateStatus(`设置表情: ${t}`)}),e.appendChild(n)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>')}clearButtons(){const e=document.getElementById("motionButtons"),s=document.getElementById("expressionButtons"),t=document.getElementById("modelInfo");e&&(e.innerHTML='<div class="loading">加载中...</div>'),s&&(s.innerHTML='<div class="loading">加载中...</div>'),t&&(t.style.display="none")}showSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.add("show"),this.updateStatus("显示文字气泡"),this._bubbleTimeout=window.setTimeout(()=>{this.hideSpeechBubble()},5e3))}hideSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.remove("show"),this.updateStatus("隐藏文字气泡"))}showMotionBubble(e,s){if(!this._bubbleText)return;const n={Idle:["我在这里等你哦~","今天天气真不错呢！","你想和我聊什么呢？","我正在想你呢~","有什么我可以帮助你的吗？"],TapBody:["哎呀，你在摸我呢！","好痒啊~","嘻嘻，你真调皮！","不要乱摸啦~","你的手好温暖呢！"]}[e]||["正在播放动作..."];let o;s<n.length?o=n[s]:o=n[Math.floor(Math.random()*n.length)],this._bubbleText.textContent=o;const i=document.getElementById("bubbleTextInput");i&&(i.value=o),this.showSpeechBubble()}setupBubbleDragging(){this._speechBubble&&(this._speechBubble.addEventListener("mousedown",e=>{this._isDragging=!0,this._speechBubble.classList.add("dragging");const s=this._speechBubble.getBoundingClientRect();this._dragOffset.x=e.clientX-s.left,this._dragOffset.y=e.clientY-s.top,e.preventDefault()}),document.addEventListener("mousemove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=document.querySelector(".canvas-container");if(!s)return;const t=s.getBoundingClientRect();let n=e.clientX-t.left-this._dragOffset.x,o=e.clientY-t.top-this._dragOffset.y;const i=this._speechBubble.offsetWidth,l=this._speechBubble.offsetHeight;n=Math.max(10,Math.min(n,t.width-i-10)),o=Math.max(10,Math.min(o,t.height-l-10)),this._speechBubble.style.left=`${n}px`,this._speechBubble.style.top=`${o}px`,this._speechBubble.style.right="",this._speechBubble.style.bottom=""}),document.addEventListener("mouseup",()=>{this._isDragging&&this._speechBubble&&(this._isDragging=!1,this._speechBubble.classList.remove("dragging"))}),this._speechBubble.addEventListener("touchstart",e=>{this._isDragging=!0,this._speechBubble.classList.add("dragging");const s=e.touches[0],t=this._speechBubble.getBoundingClientRect();this._dragOffset.x=s.clientX-t.left,this._dragOffset.y=s.clientY-t.top,e.preventDefault()}),document.addEventListener("touchmove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=e.touches[0],t=document.querySelector(".canvas-container");if(!t)return;const n=t.getBoundingClientRect();let o=s.clientX-n.left-this._dragOffset.x,i=s.clientY-n.top-this._dragOffset.y;const l=this._speechBubble.offsetWidth,a=this._speechBubble.offsetHeight;o=Math.max(10,Math.min(o,n.width-l-10)),i=Math.max(10,Math.min(i,n.height-a-10)),this._speechBubble.style.left=`${o}px`,this._speechBubble.style.top=`${i}px`,this._speechBubble.style.right="",this._speechBubble.style.bottom=""}),document.addEventListener("touchend",()=>{this._isDragging&&this._speechBubble&&(this._isDragging=!1,this._speechBubble.classList.remove("dragging"))}))}setBubblePosition(e){if(this._speechBubble)switch(this._bubblePosition=e,this._speechBubble.classList.remove("top","bottom","left","right"),this._speechBubble.style.left="",this._speechBubble.style.top="",this._speechBubble.style.right="",this._speechBubble.style.bottom="",e){case"bottom-left":this._speechBubble.style.left="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom");break;case"bottom-right":this._speechBubble.style.right="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom","right");break;case"top-left":this._speechBubble.style.left="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top");break;case"top-right":this._speechBubble.style.right="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top","right");break}}testLipSync(){const e=this._live2DManager.getCurrentModel();if(!e){this.updateStatus("请先选择一个模型");return}this._bubbleText&&(this._bubbleText.textContent="正在测试口型同步功能...",this.showSpeechBubble());try{const s="../../Resources/测试.wav";e._lipsync=!0;const t=new Audio(s);t.volume=.8;const n=e._wavFileHandler;n?(Promise.all([t.play(),Promise.resolve(n.start(s))]).then(()=>{this.updateStatus("开始播放测试音频，观察口型同步效果"),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="口型同步测试中，请观察嘴部动画！")},1e3)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("口型同步测试完成"),this._bubbleText&&(this._bubbleText.textContent="口型同步测试完成！")})):this.updateStatus("音频处理器未初始化")}catch(s){console.error("测试口型同步失败:",s),this.updateStatus("测试口型同步失败，请检查音频文件")}}changeModel(e){e>=0&&e<p?(this._live2DManager.switchToModel(e),this.updateStatus(`切换到模型 ${e+1}`)):this.updateStatus(`无效的模型索引: ${e}`)}playMotion(e,s=0){this._live2DManager.getCurrentModel()?(this._live2DManager.playMotion(e,s),this.updateStatus(`播放动作: ${e} ${s+1}`),this.showMotionBubble(e,s)):this.updateStatus("请先选择一个模型")}playExpression(e){const s=this._live2DManager.getExpressionNames();if(e>=0&&e<s.length){const t=s[e];this._live2DManager.setExpression(t),this.updateStatus(`播放表情: ${t}`)}else this.updateStatus(`无效的表情索引: ${e}`)}playRandomExpression(){this._live2DManager.setRandomExpression(),this.updateStatus("播放随机表情")}showBubble(e,s=!0){if(this._bubbleText){this._bubbleText.textContent=e;const t=document.getElementById("bubbleTextInput");t&&(t.value=e)}this.showSpeechBubble(),s||this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null)}hideBubble(){this.hideSpeechBubble()}playAudioWithLipSync(e){const s=this._live2DManager.getCurrentModel();if(!s){this.updateStatus("请先选择一个模型");return}try{s._lipsync=!0;const t=new Audio(`../../Resources/${e}`);t.volume=.8;const n=s._wavFileHandler;n?(Promise.all([t.play(),Promise.resolve(n.start(`../../Resources/${e}`))]).then(()=>{this.updateStatus(`播放音频: ${e}`)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("音频播放完成")})):this.updateStatus("音频处理器未初始化")}catch(t){console.error("播放音频失败:",t),this.updateStatus("播放音频失败，请检查音频文件")}}getCurrentModelInfo(){return this._live2DManager.getCurrentModel()?{modelName:"Current Model",hasModel:!0,motionGroups:this._live2DManager.getMotionGroups(),expressions:this._live2DManager.getExpressionNames()}:{modelName:"No Model",hasModel:!1,motionGroups:[],expressions:[]}}updateStatus(e){const s=document.getElementById("statusText");s&&(s.textContent=e),m.printMessage(`[Control Panel] ${e}`)}initializeModelUpload(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectFileBtn"),t=document.getElementById("uploadBtn"),n=document.getElementById("selectedFileName");s.addEventListener("click",()=>{e.click()}),e.addEventListener("change",o=>{var a;const i=o.target,l=(a=i.files)==null?void 0:a[0];l&&(l.type==="application/zip"||l.name.endsWith(".zip")?(n.textContent=`已选择: ${l.name}`,n.style.display="block",t.style.display="block",this.updateStatus(`已选择文件: ${l.name}`)):(alert("请选择ZIP格式的文件"),i.value=""))}),t.addEventListener("click",()=>{var i;const o=(i=e.files)==null?void 0:i[0];o&&this.uploadModel(o)})}async uploadModel(e){const s=document.getElementById("uploadStatus"),t=document.getElementById("uploadStatusText"),n=document.getElementById("progressBar"),o=document.getElementById("uploadBtn");try{s.style.display="block",s.className="upload-status",t.textContent="正在上传...",n.style.width="0%",o.disabled=!0;const i=new FormData;i.append("modelZip",e);const l=await fetch("http://localhost:3001/api/upload-model",{method:"POST",body:i});if(l.ok){const a=await l.json();if(s.className="upload-status success",t.textContent=`上传成功! 模型 ${a.modelName} 已添加`,n.style.width="100%",a.analysis){const u=[];a.analysis.hasExpressions&&u.push(`表情: ${a.analysis.expressions.length}个`),a.analysis.hasMotions&&u.push(`动作组: ${a.analysis.motionGroups.length}个`),a.analysis.issues&&a.analysis.issues.length>0&&u.push(`问题: ${a.analysis.issues.length}个`),u.length>0&&(t.textContent+=` (${u.join(", ")})`)}await this.refreshModelList(),this.clearFileSelection(),this.updateStatus(`模型 ${a.modelName} 上传成功`)}else{const a=await l.json();throw new Error(a.error||"上传失败")}}catch(i){const l=i instanceof Error?i.message:"未知错误";s.className="upload-status error",t.textContent=`上传失败: ${l}`,this.updateStatus(`上传失败: ${l}`)}finally{o.disabled=!1}}async refreshModelList(){try{await this.initializeModelSelector(),this.updateStatus("模型列表已更新")}catch(e){console.error("刷新模型列表失败:",e),this.updateStatus("刷新模型列表失败")}}clearFileSelection(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectedFileName"),t=document.getElementById("uploadBtn");e.value="",s.style.display="none",t.style.display="none"}async deleteModel(e){if(confirm(`确定要删除模型 "${e}" 吗？此操作不可撤销。`))try{this.updateStatus(`正在删除模型: ${e}...`);const s=await fetch(`http://localhost:3001/api/models/${e}`,{method:"DELETE"});if(s.ok){await s.json(),this.updateStatus(`模型 ${e} 删除成功`),await this.refreshModelList();const t=document.getElementById("deleteModelBtn");t.style.display="none"}else{const t=await s.json();throw new Error(t.error||"删除失败")}}catch(s){const t=s instanceof Error?s.message:"未知错误";this.updateStatus(`删除失败: ${t}`),alert(`删除模型失败: ${t}`)}}release(){h.releaseInstance()}}let r;window.addEventListener("DOMContentLoaded",async()=>{r=new g,await r.initialize(),window.Live2DControlPanel={changeModel:d=>r.changeModel(d),playMotion:(d,e=0)=>r.playMotion(d,e),playExpression:d=>r.playExpression(d),playRandomExpression:()=>r.playRandomExpression(),showBubble:(d,e=!0)=>r.showBubble(d,e),hideBubble:()=>r.hideBubble(),playAudio:d=>r.playAudioWithLipSync(d),uploadModel:d=>r.uploadModel(d),refreshModelList:()=>r.refreshModelList(),deleteModel:d=>r.deleteModel(d),getModelInfo:()=>r.getCurrentModelInfo(),_app:r}});window.addEventListener("beforeunload",()=>{r&&r.release()});
//# sourceMappingURL=control-panel-CvMg_SQb.js.map
