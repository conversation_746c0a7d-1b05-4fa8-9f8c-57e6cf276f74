import{L as M,M as v,a as w,b as E}from"./lappdelegate-hFREyyt4.js";const f=class f{static getAudioContext(){return this.audioContext||(this.audioContext=new(window.AudioContext||window.webkitAudioContext)),this.audioContext}static detectAudioFormat(e){try{const t=e.replace(/^data:audio\/[^;]+;base64,/,""),s=atob(t),n=new Uint8Array(Math.min(12,s.length));for(let o=0;o<n.length;o++)n[o]=s.charCodeAt(o);if(n.length>=12){const o=String.fromCharCode(n[0],n[1],n[2],n[3]),i=String.fromCharCode(n[8],n[9],n[10],n[11]);if(o==="RIFF"&&i==="WAVE")return"wav"}return n.length>=3&&(n[0]===73&&n[1]===68&&n[2]===51||n[0]===255&&(n[1]&224)===224)?"mp3":"unknown"}catch(t){return console.error("检测音频格式失败:",t),"unknown"}}static base64ToArrayBuffer(e){const t=e.replace(/^data:audio\/[^;]+;base64,/,""),s=atob(t),n=new Uint8Array(s.length);for(let o=0;o<s.length;o++)n[o]=s.charCodeAt(o);return n.buffer}static async convertMp3ToWav(e){try{const s=await this.getAudioContext().decodeAudioData(e.slice(0));return this.audioBufferToWav(s)}catch(t){console.error("MP3转WAV失败:",t);const s=t instanceof Error?t.message:String(t);throw new Error(`MP3转WAV失败: ${s}`)}}static audioBufferToWav(e){const t=e.numberOfChannels,s=e.sampleRate,n=e.length,o=16,i=o/8,a=n*t*i,l=44+a,u=new ArrayBuffer(l),c=new DataView(u);let d=0;this.writeString(c,d,"RIFF"),d+=4,c.setUint32(d,l-8,!0),d+=4,this.writeString(c,d,"WAVE"),d+=4,this.writeString(c,d,"fmt "),d+=4,c.setUint32(d,16,!0),d+=4,c.setUint16(d,1,!0),d+=2,c.setUint16(d,t,!0),d+=2,c.setUint32(d,s,!0),d+=4,c.setUint32(d,s*t*i,!0),d+=4,c.setUint16(d,t*i,!0),d+=2,c.setUint16(d,o,!0),d+=2,this.writeString(c,d,"data"),d+=4,c.setUint32(d,a,!0),d+=4;for(let p=0;p<t;p++){const B=e.getChannelData(p);let y=44+p*i;for(let b=0;b<n;b++){const m=Math.max(-1,Math.min(1,B[b])),_=m<0?m*32768:m*32767;c.setInt16(y,_,!0),y+=t*i}}return u}static writeString(e,t,s){for(let n=0;n<s.length;n++)e.setUint8(t+n,s.charCodeAt(n))}static async processBase64Audio(e){const t=this.detectAudioFormat(e),s=this.base64ToArrayBuffer(e);switch(t){case"wav":return console.log("检测到WAV格式，直接使用"),s;case"mp3":return console.log("检测到MP3格式，转换为WAV"),await this.convertMp3ToWav(s);default:console.warn("未知音频格式，尝试作为MP3处理");try{return await this.convertMp3ToWav(s)}catch{return console.error("作为MP3处理失败，尝试作为WAV处理"),s}}}};f.audioContext=null;let g=f;class x{constructor(){this._currentModelIndex=0,this._speechBubble=null,this._bubbleText=null,this._bubblePosition="center",this._bubbleTimeout=null,this._isDragging=!1,this._dragOffset={x:0,y:0},this._bubbleManuallyPositioned=!1}async initialize(){this._delegate=M.getInstance(),this._delegate.initialize(),this._live2DManager=this._delegate.getLive2DManager(),await this.initializeUI(),this._delegate.run()}async initializeUI(){await this.initializeModelSelector(),this.initializeControlButtons(),this.initializeSpeechBubble(),this.initializeModelUpload(),this.setupModelLoadListener()}async initializeModelSelector(){const e=document.getElementById("modelSelector"),t=document.getElementById("deleteModelBtn"),s=await this._live2DManager.detectAvailableModels();e.innerHTML='<option value="">选择模型...</option>',s.forEach((n,o)=>{const i=document.createElement("option");i.value=o.toString(),i.textContent=`${n.name} ${n.status==="active"?"✓":"⚠️"}`,i.setAttribute("data-model-name",n.name),i.setAttribute("data-has-expressions",n.hasExpressions.toString()),i.setAttribute("data-has-motions",n.hasMotions.toString()),e.appendChild(i)}),e.addEventListener("change",async n=>{const o=n.target,i=parseInt(o.value);if(!isNaN(i)&&s[i]){const a=s[i];await this.switchModelByName(a.name),t.style.display="block",t.setAttribute("data-model-name",a.name),this.displayModelInfo(a)}else t.style.display="none",this.hideModelInfo()}),t.addEventListener("click",()=>{const n=t.getAttribute("data-model-name");n&&this.deleteModel(n)}),s.length>0&&(e.value="0",await this.switchModelByName(s[0].name),t.style.display="block",t.setAttribute("data-model-name",s[0].name),this.displayModelInfo(s[0]))}initializeControlButtons(){const e=document.getElementById("panelToggleBtn");e==null||e.addEventListener("click",()=>{this.toggleControlPanel()});const t=document.getElementById("randomMotionBtn");t==null||t.addEventListener("click",()=>{this._live2DManager.playRandomMotion(v),this.updateStatus("播放随机动作")});const s=document.getElementById("stopMotionBtn");s==null||s.addEventListener("click",()=>{this._live2DManager.stopAllMotions(),this.updateStatus("停止所有动作")});const n=document.getElementById("randomExpressionBtn");n==null||n.addEventListener("click",()=>{this._live2DManager.setRandomExpression(),this.updateStatus("设置随机表情")});const o=document.getElementById("resetExpressionBtn");o==null||o.addEventListener("click",()=>{const a=this._live2DManager.getCurrentModel();a&&(a.getExpressionManager().stopAllMotions(),this.updateStatus("重置表情"))});const i=document.getElementById("testLipSyncBtn");i==null||i.addEventListener("click",()=>{this.testLipSync()})}initializeSpeechBubble(){this._speechBubble=document.getElementById("speechBubble"),this._bubbleText=document.getElementById("bubbleText");const e=document.getElementById("showBubbleBtn");e==null||e.addEventListener("click",()=>{this.showSpeechBubble()});const t=document.getElementById("hideBubbleBtn");t==null||t.addEventListener("click",()=>{this.hideSpeechBubble()});const s=document.getElementById("bubbleTextInput");s==null||s.addEventListener("input",o=>{const i=o.target;this._bubbleText&&(this._bubbleText.textContent=i.value)});const n=document.querySelectorAll(".position-button");n.forEach(o=>{o.addEventListener("click",i=>{const a=i.target,l=a.getAttribute("data-position");l&&(n.forEach(u=>u.classList.remove("active")),a.classList.add("active"),this.setBubblePosition(l))})}),this.setBubblePosition(this._bubblePosition),this.setupBubbleDragging(),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="欢迎使用Live2D控制面板！",this.showSpeechBubble())},3e3)}setupModelLoadListener(){const e=async()=>{this._live2DManager.isModelReady()?(this.updateModelInfo(),await this.updateMotionButtons(),await this.updateExpressionButtons(),this.updateStatus("模型加载完成")):setTimeout(e,100)};e()}switchModel(e){this._currentModelIndex=e,this._live2DManager.switchToModel(e),this.updateStatus("正在加载模型..."),this.clearButtons(),this.setupModelLoadListener()}async switchModelByName(e){this._live2DManager.loadModelByName(e),this.updateStatus(`正在加载模型: ${e}...`),this.clearButtons(),this.setupModelLoadListener();try{const t=await this._live2DManager.validateModel(e);t&&!t.isValid&&this.updateStatus(`模型 ${e} 存在问题: ${t.analysis.issues.join(", ")}`)}catch(t){console.warn("模型验证失败:",t)}}displayModelInfo(e){const t=document.getElementById("modelInfo"),s=document.getElementById("motionCount"),n=document.getElementById("expressionCount");t&&s&&n&&(t.style.display="block",s.textContent=e.motionGroups?e.motionGroups.length.toString():"0",n.textContent=e.expressions?e.expressions.length.toString():"0")}hideModelInfo(){const e=document.getElementById("modelInfo");e&&(e.style.display="none")}getCurrentModelName(){const e=document.getElementById("modelSelector");return e&&e.selectedIndex>0?e.options[e.selectedIndex].getAttribute("data-model-name"):null}updateModelInfo(){const e=this._live2DManager.getCurrentModel();if(!e||!e.getModelSetting())return;const t=this._live2DManager.getMotionGroups(),s=this._live2DManager.getExpressionNames();let n=0;t.forEach(l=>{n+=this._live2DManager.getMotionCount(l)});const o=document.getElementById("motionCount"),i=document.getElementById("expressionCount"),a=document.getElementById("modelInfo");o&&(o.textContent=n.toString()),i&&(i.textContent=s.length.toString()),a&&(a.style.display="grid")}async updateMotionButtons(){const e=document.getElementById("motionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载动作...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelMotionsFromServer(n);e.innerHTML="";for(const i in o)o.hasOwnProperty(i)&&o[i].forEach((l,u)=>{if(l.exists){const c=document.createElement("button");c.className="control-button motion",c.textContent=`${i} ${u+1}`,c.addEventListener("click",()=>{this._live2DManager.playMotion(i,u),this.updateStatus(`播放动作: ${i} ${u+1}`),this.showMotionBubble(i,u)}),e.appendChild(c)}});e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>');return}}}catch(s){console.warn("从服务器获取动作信息失败，使用本地方法:",s)}e.innerHTML="",this._live2DManager.getMotionGroups().forEach(s=>{const n=this._live2DManager.getMotionCount(s);for(let o=0;o<n;o++){const i=document.createElement("button");i.className="control-button motion",i.textContent=`${s} ${o+1}`,i.addEventListener("click",()=>{this._live2DManager.playMotion(s,o),this.updateStatus(`播放动作: ${s} ${o+1}`),this.showMotionBubble(s,o)}),e.appendChild(i)}}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>')}async updateExpressionButtons(){const e=document.getElementById("expressionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载表情...</div>';try{if(this._live2DManager.getCurrentModel()){const n=this.getCurrentModelName();if(n){const o=await this._live2DManager.getModelExpressionsFromServer(n);e.innerHTML="",o.forEach(i=>{const a=document.createElement("button");a.className="control-button expression",a.textContent=i,a.addEventListener("click",()=>{this._live2DManager.setExpression(i),this.updateStatus(`设置表情: ${i}`)}),e.appendChild(a)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>');return}}}catch(s){console.warn("从服务器获取表情信息失败，使用本地方法:",s)}e.innerHTML="",this._live2DManager.getExpressionNames().forEach(s=>{const n=document.createElement("button");n.className="control-button expression",n.textContent=s,n.addEventListener("click",()=>{this._live2DManager.setExpression(s),this.updateStatus(`设置表情: ${s}`)}),e.appendChild(n)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>')}clearButtons(){const e=document.getElementById("motionButtons"),t=document.getElementById("expressionButtons"),s=document.getElementById("modelInfo");e&&(e.innerHTML='<div class="loading">加载中...</div>'),t&&(t.innerHTML='<div class="loading">加载中...</div>'),s&&(s.style.display="none")}showSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.add("show"),this.updateStatus("显示文字气泡"),this._bubbleTimeout=window.setTimeout(()=>{this.hideSpeechBubble()},5e3))}hideSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.remove("show"),this.updateStatus("隐藏文字气泡"))}showMotionBubble(e,t){if(!this._bubbleText)return;const n={Idle:["我在这里等你哦~","今天天气真不错呢！","你想和我聊什么呢？","我正在想你呢~","有什么我可以帮助你的吗？"],TapBody:["哎呀，你在摸我呢！","好痒啊~","嘻嘻，你真调皮！","不要乱摸啦~","你的手好温暖呢！"]}[e]||["正在播放动作..."];let o;t<n.length?o=n[t]:o=n[Math.floor(Math.random()*n.length)],this._bubbleText.textContent=o;const i=document.getElementById("bubbleTextInput");i&&(i.value=o),this.showSpeechBubble()}setupBubbleDragging(){this._speechBubble&&(this._speechBubble.addEventListener("mousedown",e=>{this._isDragging=!0,this._speechBubble.classList.add("dragging");const t=this._speechBubble.getBoundingClientRect();this._dragOffset.x=e.clientX-t.left,this._dragOffset.y=e.clientY-t.top,e.preventDefault()}),document.addEventListener("mousemove",e=>{if(!this._isDragging||!this._speechBubble)return;const t=document.querySelector(".canvas-container");if(!t)return;const s=t.getBoundingClientRect();let n=e.clientX-s.left-this._dragOffset.x,o=e.clientY-s.top-this._dragOffset.y;const i=this._speechBubble.offsetWidth,a=this._speechBubble.offsetHeight;n=Math.max(10,Math.min(n,s.width-i-10)),o=Math.max(10,Math.min(o,s.height-a-10)),this._speechBubble.style.left=`${n}px`,this._speechBubble.style.top=`${o}px`,this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform=""}),document.addEventListener("mouseup",()=>{this._isDragging&&this._speechBubble&&(this._isDragging=!1,this._speechBubble.classList.remove("dragging"),this._bubbleManuallyPositioned=!0)}),this._speechBubble.addEventListener("touchstart",e=>{this._isDragging=!0,this._speechBubble.classList.add("dragging");const t=e.touches[0],s=this._speechBubble.getBoundingClientRect();this._dragOffset.x=t.clientX-s.left,this._dragOffset.y=t.clientY-s.top,e.preventDefault()}),document.addEventListener("touchmove",e=>{if(!this._isDragging||!this._speechBubble)return;const t=e.touches[0],s=document.querySelector(".canvas-container");if(!s)return;const n=s.getBoundingClientRect();let o=t.clientX-n.left-this._dragOffset.x,i=t.clientY-n.top-this._dragOffset.y;const a=this._speechBubble.offsetWidth,l=this._speechBubble.offsetHeight;o=Math.max(10,Math.min(o,n.width-a-10)),i=Math.max(10,Math.min(i,n.height-l-10)),this._speechBubble.style.left=`${o}px`,this._speechBubble.style.top=`${i}px`,this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform=""}),document.addEventListener("touchend",()=>{this._isDragging&&this._speechBubble&&(this._isDragging=!1,this._speechBubble.classList.remove("dragging"),this._bubbleManuallyPositioned=!0)}))}setBubblePosition(e){if(this._speechBubble&&!this._bubbleManuallyPositioned)switch(this._bubblePosition=e,this._speechBubble.classList.remove("top","bottom","left","right"),this._speechBubble.style.left="",this._speechBubble.style.top="",this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform="",e){case"bottom-left":this._speechBubble.style.left="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom");break;case"bottom-right":this._speechBubble.style.right="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom","right");break;case"top-left":this._speechBubble.style.left="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top");break;case"top-right":this._speechBubble.style.right="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top","right");break;case"center":default:this._speechBubble.style.left="50%",this._speechBubble.style.top="50%",this._speechBubble.style.transform="translate(-50%, -50%)";break}}testLipSync(){const e=this._live2DManager.getCurrentModel();if(!e){this.updateStatus("请先选择一个模型");return}this._bubbleText&&(this._bubbleText.textContent="正在测试口型同步功能...",this.showSpeechBubble());try{const t="../../Resources/测试.wav";e._lipsync=!0;const s=new Audio(t);s.volume=.8;const n=e._wavFileHandler;n?(Promise.all([s.play(),Promise.resolve(n.start(t))]).then(()=>{this.updateStatus("开始播放测试音频，观察口型同步效果"),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="口型同步测试中，请观察嘴部动画！")},1e3)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),s.addEventListener("ended",()=>{this.updateStatus("口型同步测试完成"),this._bubbleText&&(this._bubbleText.textContent="口型同步测试完成！")})):this.updateStatus("音频处理器未初始化")}catch(t){console.error("测试口型同步失败:",t),this.updateStatus("测试口型同步失败，请检查音频文件")}}changeModel(e){e>=0&&e<w?(this._live2DManager.switchToModel(e),this.updateStatus(`切换到模型 ${e+1}`)):this.updateStatus(`无效的模型索引: ${e}`)}playMotion(e,t=0){this._live2DManager.getCurrentModel()?(this._live2DManager.playMotion(e,t),this.updateStatus(`播放动作: ${e} ${t+1}`),this.showMotionBubble(e,t)):this.updateStatus("请先选择一个模型")}playExpression(e){const t=this._live2DManager.getExpressionNames();if(e>=0&&e<t.length){const s=t[e];this._live2DManager.setExpression(s),this.updateStatus(`播放表情: ${s}`)}else this.updateStatus(`无效的表情索引: ${e}`)}playRandomExpression(){this._live2DManager.setRandomExpression(),this.updateStatus("播放随机表情")}showBubble(e,t=!0){if(this._bubbleText){this._bubbleText.textContent=e;const s=document.getElementById("bubbleTextInput");s&&(s.value=e)}this.showSpeechBubble(),t||this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null)}hideBubble(){this.hideSpeechBubble()}playAudioWithLipSync(e){const t=this._live2DManager.getCurrentModel();if(!t){this.updateStatus("请先选择一个模型");return}try{t._lipsync=!0;const s=new Audio(`../../Resources/${e}`);s.volume=.8;const n=t._wavFileHandler;n?(Promise.all([s.play(),Promise.resolve(n.start(`../../Resources/${e}`))]).then(()=>{this.updateStatus(`播放音频: ${e}`)}).catch(o=>{console.error("播放音频失败:",o),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),s.addEventListener("ended",()=>{this.updateStatus("音频播放完成")})):this.updateStatus("音频处理器未初始化")}catch(s){console.error("播放音频失败:",s),this.updateStatus("播放音频失败，请检查音频文件")}}async playAudioFromBase64(e){const t=this._live2DManager.getCurrentModel();if(!t){this.updateStatus("请先选择一个模型");return}try{this.updateStatus("正在处理音频数据...");const s=await g.processBase64Audio(e);t._lipsync=!0;const n=new Blob([s],{type:"audio/wav"}),o=URL.createObjectURL(n),i=new Audio(o);i.volume=.8;const a=t._wavFileHandler;a?(Promise.all([i.play(),Promise.resolve(a.startFromArrayBuffer(s))]).then(()=>{this.updateStatus("播放base64音频成功")}).catch(l=>{console.error("播放base64音频失败:",l),this.updateStatus("播放base64音频失败，请检查音频数据和浏览器权限")}),i.addEventListener("ended",()=>{this.updateStatus("base64音频播放完成"),URL.revokeObjectURL(o)}),i.addEventListener("error",l=>{console.error("音频播放错误:",l),this.updateStatus("音频播放出错"),URL.revokeObjectURL(o)})):this.updateStatus("音频处理器未初始化")}catch(s){console.error("处理base64音频失败:",s);const n=s instanceof Error?s.message:String(s);this.updateStatus(`处理base64音频失败: ${n}`)}}getCurrentModelInfo(){return this._live2DManager.getCurrentModel()?{modelName:"Current Model",hasModel:!0,motionGroups:this._live2DManager.getMotionGroups(),expressions:this._live2DManager.getExpressionNames()}:{modelName:"No Model",hasModel:!1,motionGroups:[],expressions:[]}}toggleControlPanel(){const e=document.querySelector(".control-panel");e&&(e.classList.toggle("hidden"),console.log("控制面板显示状态已切换"))}updateStatus(e){const t=document.getElementById("statusText");t&&(t.textContent=e),E.printMessage(`[Control Panel] ${e}`)}initializeModelUpload(){const e=document.getElementById("modelFileInput"),t=document.getElementById("selectFileBtn"),s=document.getElementById("uploadBtn"),n=document.getElementById("selectedFileName");t.addEventListener("click",()=>{e.click()}),e.addEventListener("change",o=>{var l;const i=o.target,a=(l=i.files)==null?void 0:l[0];a&&(a.type==="application/zip"||a.name.endsWith(".zip")?(n.textContent=`已选择: ${a.name}`,n.style.display="block",s.style.display="block",this.updateStatus(`已选择文件: ${a.name}`)):(alert("请选择ZIP格式的文件"),i.value=""))}),s.addEventListener("click",()=>{var i;const o=(i=e.files)==null?void 0:i[0];o&&this.uploadModel(o)})}async uploadModel(e){const t=document.getElementById("uploadStatus"),s=document.getElementById("uploadStatusText"),n=document.getElementById("progressBar"),o=document.getElementById("uploadBtn");try{t.style.display="block",t.className="upload-status",s.textContent="正在上传...",n.style.width="0%",o.disabled=!0;const i=new FormData;i.append("modelZip",e);const a=await fetch("http://localhost:3001/api/upload-model",{method:"POST",body:i});if(a.ok){const l=await a.json();if(t.className="upload-status success",s.textContent=`上传成功! 模型 ${l.modelName} 已添加`,n.style.width="100%",l.analysis){const u=[];l.analysis.hasExpressions&&u.push(`表情: ${l.analysis.expressions.length}个`),l.analysis.hasMotions&&u.push(`动作组: ${l.analysis.motionGroups.length}个`),l.analysis.issues&&l.analysis.issues.length>0&&u.push(`问题: ${l.analysis.issues.length}个`),u.length>0&&(s.textContent+=` (${u.join(", ")})`)}await this.refreshModelList(),this.clearFileSelection(),this.notifyMainAppModelUploaded(l.modelName),this.updateStatus(`模型 ${l.modelName} 上传成功`)}else{const l=await a.json();throw new Error(l.error||"上传失败")}}catch(i){const a=i instanceof Error?i.message:"未知错误";t.className="upload-status error",s.textContent=`上传失败: ${a}`,this.updateStatus(`上传失败: ${a}`)}finally{o.disabled=!1}}async refreshModelList(){try{await this.initializeModelSelector(),this.updateStatus("模型列表已更新")}catch(e){console.error("刷新模型列表失败:",e),this.updateStatus("刷新模型列表失败")}}clearFileSelection(){const e=document.getElementById("modelFileInput"),t=document.getElementById("selectedFileName"),s=document.getElementById("uploadBtn");e.value="",t.style.display="none",s.style.display="none"}async deleteModel(e){if(confirm(`确定要删除模型 "${e}" 吗？此操作不可撤销。`))try{this.updateStatus(`正在删除模型: ${e}...`);const t=await fetch(`http://localhost:3001/api/models/${e}`,{method:"DELETE"});if(t.ok){await t.json(),this.updateStatus(`模型 ${e} 删除成功`),await this.refreshModelList();const s=document.getElementById("deleteModelBtn");s.style.display="none",this.notifyMainAppModelDeleted(e)}else{const s=await t.json();throw new Error(s.error||"删除失败")}}catch(t){const s=t instanceof Error?t.message:"未知错误";this.updateStatus(`删除失败: ${s}`),alert(`删除模型失败: ${s}`)}}notifyMainAppModelUploaded(e){try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"live2d_model_uploaded",modelName:e},"*"),window.embeddedLive2DManager&&typeof window.embeddedLive2DManager.refreshEmbeddedData=="function"&&window.embeddedLive2DManager.refreshEmbeddedData()}catch(t){console.warn("通知主应用模型上传失败:",t)}}notifyMainAppModelDeleted(e){try{window.parent&&window.parent!==window&&window.parent.postMessage({type:"live2d_model_deleted",modelName:e},"*"),window.embeddedLive2DManager&&typeof window.embeddedLive2DManager.refreshEmbeddedData=="function"&&window.embeddedLive2DManager.refreshEmbeddedData()}catch(t){console.warn("通知主应用模型删除失败:",t)}}release(){M.releaseInstance()}}let h;window.addEventListener("DOMContentLoaded",async()=>{h=new x,await h.initialize(),window.Live2DControlPanel={changeModel:r=>h.changeModel(r),playMotion:(r,e=0)=>h.playMotion(r,e),playExpression:r=>h.playExpression(r),playRandomExpression:()=>h.playRandomExpression(),showBubble:(r,e=!0)=>h.showBubble(r,e),hideBubble:()=>h.hideBubble(),playAudio:r=>h.playAudioWithLipSync(r),playAudioFromBase64:r=>h.playAudioFromBase64(r),uploadModel:r=>h.uploadModel(r),refreshModelList:()=>h.refreshModelList(),deleteModel:r=>h.deleteModel(r),getModelInfo:()=>h.getCurrentModelInfo(),_app:h},window.addEventListener("message",r=>{var e,t,s;if(r.data&&r.data.type==="toggle_panel"){const n=document.querySelector(".control-panel");n&&(n.classList.toggle("hidden"),console.log("控制面板显示状态已切换"));return}if(r.data&&r.data.type==="live2d_api_call"){const{messageId:n,method:o,args:i}=r.data;try{const a=window.Live2DControlPanel;if(a&&typeof a[o]=="function"){const l=a[o](...i);l&&typeof l.then=="function"?l.then(u=>{var c;(c=r.source)==null||c.postMessage({type:"live2d_api_response",messageId:n,success:!0,result:u},"*")}).catch(u=>{var c;(c=r.source)==null||c.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:u.message||"调用失败"},"*")}):(e=r.source)==null||e.postMessage({type:"live2d_api_response",messageId:n,success:!0,result:l},"*")}else(t=r.source)==null||t.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:`方法 ${o} 不存在`},"*")}catch(a){(s=r.source)==null||s.postMessage({type:"live2d_api_response",messageId:n,success:!1,error:a.message||"调用失败"},"*")}}})});window.addEventListener("beforeunload",()=>{h&&h.release()});
//# sourceMappingURL=control-panel-CZSjFbo9.js.map
