#!/usr/bin/env node

/**
 * 测试Live2D服务器持久性
 * 验证服务器是否能持续运行而不会意外退出
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

console.log('🔄 测试Live2D服务器持久性...\n');

let serverProcess = null;
let testRunning = true;

// 启动Live2D服务器
function startServer() {
  return new Promise((resolve, reject) => {
    const live2dServerPath = path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js');
    const live2dWorkingDir = path.join(__dirname, 'app', 'live2d');

    // 根据平台选择正确的Node.js路径
    let nodePath;
    if (process.platform === 'win32') {
      nodePath = path.join(__dirname, 'node-v23.6.1-windows-x64', 'node.exe');
    } else {
      nodePath = path.join(__dirname, 'node-v23.6.1-darwin-arm64', 'bin', 'node');
    }
    
    console.log('🚀 启动Live2D服务器...');
    
    const env = {
      ...process.env,
      NODE_PATH: path.join(live2dWorkingDir, 'node_modules')
    };
    
    serverProcess = spawn(nodePath, [live2dServerPath], {
      cwd: live2dWorkingDir,
      stdio: 'pipe',
      env: env,
      detached: false
    });
    
    console.log(`  进程PID: ${serverProcess.pid}`);
    
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      console.log(`[Server] ${output}`);
      
      if (output.includes('Live2D模型管理服务器运行在 http://localhost:3001')) {
        console.log('✅ 服务器启动成功');
        resolve();
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (!output.includes('express:') && !output.includes('router:')) {
        console.log(`[Server Error] ${output}`);
      }
    });
    
    serverProcess.on('exit', (code, signal) => {
      console.log(`❌ 服务器进程退出: 退出码=${code}, 信号=${signal}`);
      if (testRunning && code !== 0) {
        console.log('🔄 尝试重启服务器...');
        setTimeout(() => {
          startServer().catch(err => {
            console.error('重启失败:', err.message);
          });
        }, 3000);
      }
    });
    
    serverProcess.on('error', (err) => {
      console.error('❌ 服务器启动失败:', err.message);
      reject(err);
    });
    
    // 超时处理
    setTimeout(() => {
      if (serverProcess && !serverProcess.killed) {
        console.log('⏰ 启动超时，但进程仍在运行');
        resolve();
      }
    }, 10000);
  });
}

// 健康检查
function healthCheck() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3001/api/health', { timeout: 5000 }, (res) => {
      if (res.statusCode === 200) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
  });
}

// 检查进程是否运行
function isProcessRunning(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch (e) {
    return false;
  }
}

// 主测试循环
async function runPersistenceTest() {
  try {
    // 启动服务器
    await startServer();
    
    console.log('\n🔍 开始持久性测试 (运行60秒)...');
    
    let checkCount = 0;
    const testInterval = setInterval(async () => {
      checkCount++;
      
      if (!testRunning) {
        clearInterval(testInterval);
        return;
      }
      
      console.log(`\n[检查 ${checkCount}] ${new Date().toLocaleTimeString()}`);
      
      // 检查进程状态
      const processRunning = serverProcess && isProcessRunning(serverProcess.pid);
      console.log(`  进程状态: ${processRunning ? '运行中' : '已停止'}`);
      
      if (!processRunning) {
        console.log('  ❌ 进程已停止，这是一个问题！');
        return;
      }
      
      // 健康检查
      const healthy = await healthCheck();
      console.log(`  健康状态: ${healthy ? '正常' : '异常'}`);
      
      if (!healthy) {
        console.log('  ⚠️  健康检查失败，服务器可能有问题');
      }
      
      // 60秒后结束测试
      if (checkCount >= 12) { // 每5秒检查一次，12次 = 60秒
        console.log('\n🎉 持久性测试完成！');
        console.log('  结果: Live2D服务器成功运行了60秒');
        
        testRunning = false;
        clearInterval(testInterval);
        
        // 清理
        if (serverProcess && !serverProcess.killed) {
          console.log('\n🛑 停止测试服务器...');
          serverProcess.kill('SIGTERM');
        }
        
        setTimeout(() => {
          process.exit(0);
        }, 2000);
      }
      
    }, 5000); // 每5秒检查一次
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n🛑 测试被中断');
  testRunning = false;
  
  if (serverProcess && !serverProcess.killed) {
    serverProcess.kill('SIGTERM');
  }
  
  setTimeout(() => {
    process.exit(1);
  }, 2000);
});

// 运行测试
runPersistenceTest();
