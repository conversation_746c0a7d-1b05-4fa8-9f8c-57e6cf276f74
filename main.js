const { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain } = require('electron');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const { shell, spawn } = require('electron');
const childProcess = require('child_process');

// 定义为全局变量
let mainWindow;
let secretKey;
let loadingWindow06012;
let agentProcess06012;
let live2dServerProcess; // Live2D模型管理服务器进程
let tempNpxPath; // 存储临时 npx 脚本路径

// 运行 npx 命令的辅助函数
function runNpx(args, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      // 获取Node和npx路径
      const nodePath = getBundledNodePath();
      const npxPath = getBundledNpxPath();
      const usesBundledNode = nodePath !== 'node';
      
      console.log('准备执行npx命令:', args);
      
      let command, commandArgs;
      const spawnOptions = {
        ...options,
        stdio: options.stdio || 'pipe',
        shell: process.platform === 'win32', // Windows下使用shell执行
      };
      
      if (usesBundledNode && app.isPackaged) {
        // 在打包应用中，如果有临时npx脚本，使用它
        if (process.platform === 'win32' && tempNpxPath && fs.existsSync(tempNpxPath)) {
          command = tempNpxPath;
          commandArgs = args;
        } else if (fs.existsSync(npxPath)) {
          // 直接使用捆绑的npx
          command = npxPath;
          commandArgs = args;
        } else {
          // 通过node执行npm的npx-cli.js
          command = nodePath;
          const npxCliPath = path.join(process.resourcesPath, 'bundled_node', 'node_modules', 'npm', 'bin', 'npx-cli.js');
          commandArgs = [npxCliPath, ...args];
        }
        
        // 设置环境变量
        if (!spawnOptions.env) {
          spawnOptions.env = {...process.env};
        }
        
        // 添加Node.js相关环境变量
        const bundledNodeDir = path.dirname(nodePath);
        spawnOptions.env.PATH = `${bundledNodeDir}${path.delimiter}${spawnOptions.env.PATH || ''}`;
        spawnOptions.env.NODE_PATH = path.join(process.resourcesPath, 'bundled_node', 'node_modules');
        spawnOptions.env.NPM_CONFIG_PREFIX = path.join(process.resourcesPath, 'bundled_node');
        
        // Windows特殊处理
        if (process.platform === 'win32') {
          spawnOptions.env.Path = spawnOptions.env.PATH;
        }
      } else {
        // 开发环境下直接使用系统npx
        command = 'npx';
        commandArgs = args;
      }
      
      console.log(`执行: ${command} ${commandArgs.join(' ')}`);
      
      const npxProcess = childProcess.spawn(command, commandArgs, spawnOptions);
      
      let stdout = '';
      let stderr = '';
      
      if (npxProcess.stdout) {
        npxProcess.stdout.on('data', (data) => {
          const output = data.toString();
          stdout += output;
          console.log(`npx stdout: ${output}`);
        });
      }
      
      if (npxProcess.stderr) {
        npxProcess.stderr.on('data', (data) => {
          const output = data.toString();
          stderr += output;
          console.error(`npx stderr: ${output}`);
        });
      }
      
      npxProcess.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, stdout, stderr });
        } else {
          reject(new Error(`npx命令执行失败，退出码: ${code}\nstdout: ${stdout}\nstderr: ${stderr}`));
        }
      });
      
      npxProcess.on('error', (err) => {
        reject(new Error(`启动npx命令失败: ${err.message}`));
      });
    } catch (error) {
      reject(new Error(`执行npx时发生异常: ${error.message}`));
    }
  });
}

// 测试bundled Node环境是否正常工作
async function testBundledNodeEnv() {
  try {
    console.log('测试bundled Node环境...');
    
    // 检查node是否可用
    const nodePath = getBundledNodePath();
    if (!fs.existsSync(nodePath) && app.isPackaged) {
      console.warn('找不到bundled node路径:', nodePath);
      return false;
    }
    
    // 检查npx是否可用
    try {
      const result = await runNpx(['--version']);
      console.log('npx版本:', result.stdout.trim());
      console.log('bundled Node环境测试成功!');
      return true;
    } catch (npxError) {
      console.error('npx测试失败:', npxError.message);
      return false;
    }
  } catch (error) {
    console.error('测试bundled Node环境失败:', error);
    return false;
  }
}

// 初始化捆绑的Node.js和NPM环境
function initBundledNodeEnvironment() {
  return new Promise(async (resolve, reject) => {
    if (!app.isPackaged) {
      // 在开发环境中，无需初始化
      return resolve();
    }
    
    try {
      const nodePath = getBundledNodePath();
      
      if (!fs.existsSync(nodePath)) {
        console.warn('未找到捆绑的Node.js:', nodePath);
        return resolve(); // 即使找不到也继续执行
      }
      
      // 设置基本路径
      const bundledNodeDir = path.dirname(nodePath);
      const npmDir = path.join(bundledNodeDir, 'node_modules', 'npm');
      
      // 检查npm是否已经安装
      if (!fs.existsSync(npmDir)) {
        console.log('npm目录不存在，无需初始化');
        return resolve();
      }
      
      // 创建npx脚本
      if (process.platform === 'win32') {
        // Windows上创建npx.cmd
        tempNpxPath = path.join(app.getPath('temp'), 'npx.cmd');
        const npxContent = `@echo off\r\n"${nodePath}" "${path.join(bundledNodeDir, 'node_modules', 'npm', 'bin', 'npx-cli.js')}" %*`;
        fs.writeFileSync(tempNpxPath, npxContent);
        console.log('创建临时npx批处理文件:', tempNpxPath);
      } else {
        // Unix系统上创建npx可执行脚本
        tempNpxPath = path.join(app.getPath('temp'), 'npx');
        const npxContent = `#!/bin/bash\n"${nodePath}" "${path.join(bundledNodeDir, 'node_modules', 'npm', 'bin', 'npx-cli.js')}" "$@"`;
        fs.writeFileSync(tempNpxPath, npxContent);
        fs.chmodSync(tempNpxPath, 0o755);
        console.log('创建临时npx脚本文件:', tempNpxPath);
      }
      
      resolve();
    } catch (error) {
      console.error('初始化捆绑Node环境失败:', error);
      resolve(); // 即使失败也继续执行应用程序
    }
  });
}

function createWindow() {
  // 设置自定义窗口样式
  mainWindow = new BrowserWindow({
    width: 1800,
    height: 1000,
    title: '兴河AI助手',
    frame: false, // 移除默认窗口框架
    transparent: false, // 不透明
    backgroundColor: '#f5f5f5', // 设置背景色
    titleBarStyle: 'hiddenInset', // 在macOS上使用hiddenInset而不是hidden
    trafficLightPosition: { x: 10, y: 10 }, // 调整macOS交通灯按钮位置
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      // 添加访问媒体设备的权限
      webSecurity: true,
      allowRunningInsecureContent: false,
      devTools: true, // 启用开发者工具，方便调试
      // 添加这一行，禁用后台限制，允许程序在最小化时继续运行
      backgroundThrottling: false
    }
  });

  // 添加处理外部链接的代码，使用默认浏览器打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // 打开默认浏览器处理URL
    shell.openExternal(url);
    return { action: 'deny' }; // 阻止在Electron中创建新窗口
  });
  
  // 处理页面内的链接点击，防止页面导航到外部链接
  mainWindow.webContents.on('will-navigate', (event, url) => {
    // 如果不是应用内的本地链接，则阻止导航并用默认浏览器打开
    if (!url.startsWith('file://')) {
      event.preventDefault();
      shell.openExternal(url);
    }
  });

  // 请求摄像头和麦克风权限
  mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
    const allowedPermissions = ['media', 'mediaKeySystem', 'geolocation', 'notifications', 'midi', 'midiSysex'];
    if (allowedPermissions.includes(permission)) {
      callback(true); // 授权通过
    } else {
      callback(false); // 拒绝权限
    }
  });

  // 移除默认菜单
  Menu.setApplicationMenu(null);

  // // 添加键盘快捷键，允许使用F12或Ctrl+Shift+I打开开发者工具
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'F12' || (input.control && input.shift && input.key === 'i')) {
      mainWindow.webContents.openDevTools();
      event.preventDefault();
    }
  });

  // 设置IPC通信处理窗口控制
  ipcMain.on('minimize-window', () => {
    mainWindow.minimize();
  });

  ipcMain.on('maximize-window', () => {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  });

  ipcMain.on('close-window', () => {
    mainWindow.close();
  });

  ipcMain.on('is-maximized', (event) => {
    event.returnValue = mainWindow.isMaximized();
  });

  // 监听最大化和非最大化事件
  mainWindow.on('maximize', () => {
    mainWindow.webContents.send('window-maximized');
  });

  mainWindow.on('unmaximize', () => {
    mainWindow.webContents.send('window-unmaximized');
  });

  // 添加窗口最小化和恢复事件的监听
  mainWindow.on('minimize', () => {
    mainWindow.webContents.send('window-minimized');
  });

  mainWindow.on('restore', () => {
    mainWindow.webContents.send('window-restored');
  });

  // 处理窗口获得/失去焦点事件
  mainWindow.on('focus', () => {
    mainWindow.webContents.send('window-focused');
  });

  mainWindow.on('blur', () => {
    mainWindow.webContents.send('window-blurred');
  });

  // 在窗口关闭前先清理进程
  mainWindow.on('close', () => {
    // 主窗口即将关闭时确保Live2DAgentServer进程被终止
    cleanupProcesses06012();
  });

  // 添加窗口关闭事件，清理IPC监听器
  mainWindow.on('closed', () => {
    // 清理所有与窗口相关的IPC监听器
    ipcMain.removeAllListeners('minimize-window');
    ipcMain.removeAllListeners('maximize-window');
    ipcMain.removeAllListeners('close-window');
    ipcMain.removeAllListeners('is-maximized');
    // 移除secret-key处理程序
    ipcMain.removeHandler('get-secret-key');
    // 移除打开用户目录处理程序
    ipcMain.removeAllListeners('open-user-data-directory06011');
    
    mainWindow = null;
  });

  mainWindow.loadFile('index.html');
  
  // 在页面加载完成后设置密钥
  mainWindow.webContents.on('did-finish-load', () => {
    // 确保已获取到密钥
    if (!secretKey) {
      secretKey = handleSecretKey();
    }
    
    // 将密钥设置到HTML中的input元素
    mainWindow.webContents.executeJavaScript(`
      document.getElementById('secret_key').value = "${secretKey}";
    `);
  });
  
  // 在应用启动时自动打开开发者工具
  mainWindow.webContents.openDevTools();
}

// 检查或创建密钥文件
function handleSecretKey() {
  const userDataPath = app.getPath('userData');
  const secretKeyPath = path.join(userDataPath, 'secret_key.txt');
  
  // 检查文件是否存在
  if (fs.existsSync(secretKeyPath)) {
    // 如果存在，读取密钥
    secretKey = fs.readFileSync(secretKeyPath, 'utf8').trim();
  } else {
    // 如果不存在，生成32位随机数并保存
    secretKey = crypto.randomBytes(16).toString('hex'); // 生成32个十六进制字符 (16字节)
    fs.writeFileSync(secretKeyPath, secretKey);
  }
  
  // 返回密钥值以便后续使用
  return secretKey;
}

// 创建加载窗口
function createLoadingWindow06012() {
  loadingWindow06012 = new BrowserWindow({
    width: 500,
    height: 300,
    frame: false,
    transparent: false,
    resizable: false,
    center: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      devTools: true // 启用开发者工具
    }
  });
  
  loadingWindow06012.loadFile('app/loading06012.html');
  
  // 添加键盘快捷键以打开开发者工具
  loadingWindow06012.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'F12' || (input.control && input.shift && input.key === 'i')) {
      loadingWindow06012.webContents.openDevTools();
      event.preventDefault();
    }
  });
  
  // 防止加载窗口被关闭
  loadingWindow06012.on('close', (event) => {
    // 如果主窗口还没有创建，阻止关闭
    if (!mainWindow) {
      event.preventDefault();
    }
  });
}

// 获取打包后的Node.js路径
function getBundledNodePath() {
  if (app.isPackaged) {
    console.log('查找内嵌Node.js路径...');
    console.log('process.resourcesPath:', process.resourcesPath);
    console.log('__dirname:', __dirname);
    console.log('process.execPath:', process.execPath);

    // 可能的Node.js路径列表
    const possiblePaths = [];

    if (process.platform === 'win32') {
      possiblePaths.push(
        path.join(process.resourcesPath, 'bundled_node', 'node.exe'),
        path.join(process.resourcesPath, 'app.asar.unpacked', 'node_modules', 'bundled_node', 'node.exe'),
        path.join(__dirname, 'node_modules', 'bundled_node', 'node.exe'),
        path.join(path.dirname(process.execPath), 'bundled_node', 'node.exe'),
        // Windows 特定的内嵌Node.js路径
        path.join(__dirname, 'node-v23.6.1-windows-x64', 'node.exe'),
        path.join(process.resourcesPath, 'node-v23.6.1-windows-x64', 'node.exe'),
        path.join(process.resourcesPath, 'app', 'assets', 'bin', 'node.exe'),
        path.join(__dirname, 'app', 'assets', 'bin', 'node.exe')
      );
    } else {
      possiblePaths.push(
        path.join(process.resourcesPath, 'bundled_node', 'node'),
        path.join(process.resourcesPath, 'app.asar.unpacked', 'node_modules', 'bundled_node', 'node'),
        path.join(__dirname, 'node_modules', 'bundled_node', 'node'),
        path.join(path.dirname(process.execPath), 'bundled_node', 'node'),
        // macOS特殊路径
        path.join(process.resourcesPath, 'app', 'node_modules', 'bundled_node', 'node'),
        path.join(process.resourcesPath, '..', 'MacOS', 'bundled_node', 'node'),
        // 实际的内嵌Node.js路径
        path.join(__dirname, 'node-v23.6.1-darwin-arm64', 'bin', 'node'),
        path.join(process.resourcesPath, 'node-v23.6.1-darwin-arm64', 'bin', 'node'),
        path.join(process.resourcesPath, 'app', 'assets', 'bin', 'node'),
        path.join(__dirname, 'app', 'assets', 'bin', 'node')
      );
    }

    // 检查每个可能的路径
    for (const nodePath of possiblePaths) {
      console.log('检查Node路径:', nodePath);
      if (fs.existsSync(nodePath)) {
        console.log('✅ 找到内嵌Node.js:', nodePath);
        return nodePath;
      }
    }

    console.error('❌ 未找到内嵌Node.js，尝试的路径:');
    possiblePaths.forEach(p => console.error('  -', p));

    // 如果都找不到，返回第一个默认路径（会导致错误，但至少有明确的错误信息）
    return possiblePaths[0];
  }
  return 'node'; // 开发环境使用系统Node
}

// 获取打包后的npx路径
function getBundledNpxPath() {
  if (app.isPackaged) {
    // 根据平台返回不同的npx路径
    if (process.platform === 'win32') {
      // 尝试多个可能的npx路径
      const possibleNpxPaths = [
        path.join(process.resourcesPath, 'bundled_node', 'npx.cmd'),
        path.join(process.resourcesPath, 'node-v23.6.1-windows-x64', 'npx.cmd'),
        path.join(__dirname, 'node-v23.6.1-windows-x64', 'npx.cmd')
      ];

      for (const npxPath of possibleNpxPaths) {
        if (fs.existsSync(npxPath)) {
          return npxPath;
        }
      }

      return path.join(process.resourcesPath, 'bundled_node', 'npx.cmd');
    } else {
      return path.join(process.resourcesPath, 'bundled_node', 'npx');
    }
  }
  return 'npx'; // 开发环境使用系统npx
}

// 启动Live2D模型管理服务器
function startLive2DModelServer() {
  return new Promise((resolve, reject) => {
    try {
      console.log('启动Live2D模型管理服务器...');

      // 确定Live2D服务器路径
      let live2dServerPath;
      let live2dWorkingDir;

      if (app.isPackaged) {
        // 在打包的应用中
        live2dServerPath = path.join(process.resourcesPath, 'app', 'live2d', 'start-live2d-server.js');
        live2dWorkingDir = path.join(process.resourcesPath, 'app', 'live2d');

        // 检查路径是否存在
        if (!fs.existsSync(live2dServerPath)) {
          // 尝试其他可能的路径
          const alternatePaths = [
            path.join(process.resourcesPath, 'live2d', 'start-live2d-server.js'),
            path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js')
          ];

          for (const altPath of alternatePaths) {
            if (fs.existsSync(altPath)) {
              live2dServerPath = altPath;
              live2dWorkingDir = path.dirname(altPath);
              break;
            }
          }
        }
      } else {
        // 在开发环境中
        live2dServerPath = path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js');
        live2dWorkingDir = path.join(__dirname, 'app', 'live2d');
      }

      if (!fs.existsSync(live2dServerPath)) {
        console.error('Live2D服务器启动脚本不存在:', live2dServerPath);
        return reject(new Error(`Live2D服务器启动脚本不存在: ${live2dServerPath}`));
      }

      console.log('Live2D服务器路径:', live2dServerPath);
      console.log('Live2D工作目录:', live2dWorkingDir);

      // 获取Node.js路径
      const nodePath = getBundledNodePath();
      const usesBundledNode = nodePath !== 'node';

      console.log('Live2D服务器使用Node路径:', nodePath);
      console.log('是否使用内嵌Node:', usesBundledNode);

      // 设置环境变量
      const env = {
        ...process.env
      };

      // 如果使用捆绑的Node，设置相关环境变量
      if (app.isPackaged && usesBundledNode) {
        const bundledNodeDir = path.dirname(nodePath);
        const bundledNodeModules = path.join(process.resourcesPath, 'bundled_node', 'node_modules');

        // 设置NODE_PATH，包含内嵌的node_modules和Live2D的node_modules
        const live2dNodeModules = path.join(live2dWorkingDir, 'node_modules');
        const nodePaths = [bundledNodeModules];

        // 如果Live2D有自己的node_modules，也加入PATH
        if (fs.existsSync(live2dNodeModules)) {
          nodePaths.push(live2dNodeModules);
          console.log('找到Live2D node_modules:', live2dNodeModules);
        }

        env.NODE_PATH = nodePaths.join(path.delimiter);
        env.PATH = `${bundledNodeDir}${path.delimiter}${env.PATH || ''}`;
        env.NPM_CONFIG_PREFIX = path.join(process.resourcesPath, 'bundled_node');

        console.log('设置NODE_PATH:', env.NODE_PATH);
        console.log('设置PATH:', env.PATH);

        if (process.platform === 'win32') {
          env.Path = env.PATH;
        }
      }

      // 启动Live2D服务器
      console.log('启动Live2D服务器...');
      console.log('  命令:', nodePath);
      console.log('  参数:', [live2dServerPath]);
      console.log('  工作目录:', live2dWorkingDir);

      live2dServerProcess = childProcess.spawn(nodePath, [live2dServerPath], {
        cwd: live2dWorkingDir,
        stdio: 'pipe',
        env: env,
        detached: false,
        shell: false
      });

      // 设置编码
      live2dServerProcess.stdout.setEncoding('utf8');
      live2dServerProcess.stderr.setEncoding('utf8');

      // 监听输出
      live2dServerProcess.stdout.on('data', (data) => {
        console.log(`[Live2D Server] ${data.toString().trim()}`);

        // 检查启动成功标志
        if (data.includes('服务地址: http://localhost:3001') ||
            data.includes('Live2D服务器启动成功') ||
            data.includes('Server running on port 3001') ||
            data.includes('Live2D模型管理服务器运行在 http://localhost:3001')) {
          console.log('Live2D模型管理服务器启动成功');
          resolve();
        }
      });

      live2dServerProcess.stderr.on('data', (data) => {
        const output = data.toString().trim();
        console.error(`[Live2D Server Error] ${output}`);

        // 某些启动信息可能在stderr中
        if (output.includes('服务地址: http://localhost:3001') ||
            output.includes('Live2D服务器启动成功') ||
            output.includes('Server running on port 3001') ||
            output.includes('Live2D模型管理服务器运行在 http://localhost:3001')) {
          console.log('Live2D模型管理服务器启动成功');
          resolve();
        }
      });

      // 进程退出处理
      live2dServerProcess.on('exit', (code, signal) => {
        console.log(`Live2D服务器进程退出，退出码: ${code}, 信号: ${signal}`);
        if (code !== 0 && code !== null) {
          console.error(`Live2D服务器异常退出: ${code}`);
          // 在打包环境中，尝试重启服务器
          if (app.isPackaged) {
            console.log('尝试重启Live2D服务器...');
            setTimeout(() => {
              startLive2DModelServer().catch(err => {
                console.error('重启Live2D服务器失败:', err.message);
              });
            }, 5000);
          }
        }
      });

      // 进程错误处理
      live2dServerProcess.on('error', (err) => {
        console.error('启动Live2D服务器失败:', err);
        reject(new Error(`启动Live2D服务器失败: ${err.message}`));
      });

      // 设置超时
      setTimeout(() => {
        if (live2dServerProcess && !live2dServerProcess.killed) {
          console.log('Live2D服务器启动超时，但进程仍在运行，假设启动成功');
          resolve();
        }
      }, 10000); // 10秒超时

      // 启动健康检查
      startLive2DHealthCheck();

    } catch (error) {
      console.error('启动Live2D服务器异常:', error);
      reject(new Error(`启动Live2D服务器异常: ${error.message}`));
    }
  });
}

// Live2D服务器健康检查
function startLive2DHealthCheck() {
  if (!app.isPackaged) {
    return; // 开发环境不需要健康检查
  }

  console.log('启动Live2D服务器健康检查...');

  const checkInterval = setInterval(() => {
    // 检查进程是否还在运行
    if (!live2dServerProcess || live2dServerProcess.killed) {
      console.log('检测到Live2D服务器进程已停止，尝试重启...');
      clearInterval(checkInterval);

      // 重启服务器
      startLive2DModelServer().then(() => {
        console.log('Live2D服务器重启成功');
        startLive2DHealthCheck(); // 重新开始健康检查
      }).catch(err => {
        console.error('Live2D服务器重启失败:', err.message);
        // 5分钟后再次尝试
        setTimeout(() => {
          startLive2DHealthCheck();
        }, 300000);
      });
      return;
    }

    // 检查端口是否还在监听
    const http = require('http');
    const req = http.get('http://localhost:3001/api/health', { timeout: 5000 }, (res) => {
      if (res.statusCode === 200) {
        console.log('Live2D服务器健康检查通过');
      } else {
        console.warn(`Live2D服务器响应异常，状态码: ${res.statusCode}`);
      }
    });

    req.on('error', (err) => {
      console.error('Live2D服务器健康检查失败:', err.message);
      console.log('尝试重启Live2D服务器...');

      // 终止当前进程
      if (live2dServerProcess && !live2dServerProcess.killed) {
        live2dServerProcess.kill('SIGTERM');
      }

      clearInterval(checkInterval);

      // 重启服务器
      setTimeout(() => {
        startLive2DModelServer().then(() => {
          console.log('Live2D服务器重启成功');
          startLive2DHealthCheck(); // 重新开始健康检查
        }).catch(err => {
          console.error('Live2D服务器重启失败:', err.message);
        });
      }, 5000);
    });

    req.on('timeout', () => {
      console.warn('Live2D服务器健康检查超时');
      req.destroy();
    });

  }, 30000); // 每30秒检查一次
}

// 启动Live2DAgentServer
function startLive2DAgentServer06012(secretKey, userDataPath) {
  return new Promise((resolve, reject) => {
    try {
      // 检查是否使用捆绑的Node.js
      const nodePath = getBundledNodePath();
      const npxPath = getBundledNpxPath();
      const usesBundledNode = nodePath !== 'node';
      console.log(`使用Node路径: ${nodePath}`);
      console.log(`使用NPX路径: ${npxPath}`);

      // 设置可执行文件路径，区分开发环境和生产环境
      let agentPath;
      let binDir; // 保存bin目录路径
      
      if (app.isPackaged) {
        // 在打包的应用中，检查各种可能的路径
        const possiblePaths = [
          path.join(process.resourcesPath, 'app/assets/bin/Live2DAgentServer'),
          path.join(process.resourcesPath, 'assets/bin/Live2DAgentServer'),
          path.join(process.resourcesPath, 'bin/Live2DAgentServer'),
          // 添加.js版本的路径，用于Node.js执行
          path.join(process.resourcesPath, 'app/assets/bin/Live2DAgentServer.js'),
          path.join(process.resourcesPath, 'assets/bin/Live2DAgentServer.js'),
          path.join(process.resourcesPath, 'bin/Live2DAgentServer.js')
        ];
        
        // 添加平台特定后缀
        if (process.platform === 'win32') {
          possiblePaths.push(path.join(process.resourcesPath, 'app/assets/bin/Live2DAgentServer.exe'));
          possiblePaths.push(path.join(process.resourcesPath, 'app/assets/bin/Live2DAgentServer.bat'));
        }
        
        // 检查哪个路径存在
        let pathFound = false;
        for (const testPath of possiblePaths) {
          console.log('检查路径:', testPath);
          if (fs.existsSync(testPath)) {
            agentPath = testPath;
            pathFound = true;
            binDir = path.dirname(testPath); // 保存bin目录路径
            console.log('找到Live2DAgentServer路径:', agentPath);
            break;
          }
        }
        
        if (!pathFound) {
          console.error('未找到Live2DAgentServer可执行文件，尝试的路径:', possiblePaths);
          return reject('未找到Live2DAgentServer可执行文件，请检查安装是否完整');
        }
      } else {
        // 在开发环境中
        agentPath = path.join(__dirname, 'app/assets/bin/Live2DAgentServer');
        binDir = path.join(__dirname, 'app/assets/bin'); // 保存bin目录路径
        if (process.platform === 'win32') {
          // 检查.js或.exe版本
          if (fs.existsSync(`${agentPath}.js`)) {
            agentPath = `${agentPath}.js`;
          } else if (fs.existsSync(`${agentPath}.exe`)) {
            agentPath = `${agentPath}.exe`;
          }
        }
      }
      
      if (!fs.existsSync(agentPath)) {
        return reject(`Live2DAgentServer可执行文件不存在: ${agentPath}`);
      }
      
      // 检查uv和uvx文件是否存在
      const uvPath = path.join(binDir, 'uv');
      const uvxPath = path.join(binDir, 'uvx');
      
      // 在Windows平台添加.exe后缀
      const uvExePath = process.platform === 'win32' ? `${uvPath}.exe` : uvPath;
      const uvxExePath = process.platform === 'win32' ? `${uvxPath}.exe` : uvxPath;
      
      // 检查文件存在性并设置执行权限
      const uvExists = fs.existsSync(uvExePath);
      const uvxExists = fs.existsSync(uvxExePath);
      
      console.log(`uv路径: ${uvExePath}, 存在: ${uvExists}`);
      console.log(`uvx路径: ${uvxExePath}, 存在: ${uvxExists}`);
      
      // 如果在非Windows平台上，确保文件有执行权限
      if (process.platform !== 'win32') {
        if (uvExists) {
          try {
            fs.chmodSync(uvExePath, 0o755);
          } catch (err) {
            console.error('设置uv可执行权限失败:', err);
          }
        }
        if (uvxExists) {
          try {
            fs.chmodSync(uvxExePath, 0o755);
          } catch (err) {
            console.error('设置uvx可执行权限失败:', err);
          }
        }
      }
      
      // 确定是否需要使用Node来执行服务器
      const isNodeScript = agentPath.endsWith('.js');
      console.log('是否为Node.js脚本:', isNodeScript);
      
      // 添加工作目录
      const workingDir = path.dirname(agentPath);
      
      // 设置环境变量
      const env = {
        ...process.env,
        USER_DATA_PATH: userDataPath,
        LIVE2D_DEBUG: 'true',
        // 添加uv和uvx的路径
        UV_PATH: uvExists ? uvExePath : '',
        UVX_PATH: uvxExists ? uvxExePath : '',
        // 添加bin目录到PATH，确保Live2DAgentServer可以直接调用uv和uvx
        PATH: `${binDir}${path.delimiter}${process.env.PATH || ''}`
      };
      
      // 如果有捆绑的Node，添加到PATH
      if (usesBundledNode) {
        const bundledNodeDir = path.dirname(nodePath);
        const originalPath = env.PATH || env.Path || '';
        
        // 确保PATH中包含Node.js、npx路径和bin目录
        env.PATH = `${bundledNodeDir}${path.delimiter}${binDir}${path.delimiter}${originalPath}`;
        
        // 为Node.js脚本添加额外的环境变量
        env.NODE_PATH = path.join(process.resourcesPath, 'bundled_node', 'node_modules');
        
        // 添加NPM相关环境变量
        env.NPM_CONFIG_PREFIX = path.join(process.resourcesPath, 'bundled_node');
        
        // 在Windows上特别处理路径
        if (process.platform === 'win32') {
          env.Path = env.PATH;  // Windows使用Path而不是PATH
          
          // 创建临时npx批处理文件以确保正确执行
          try {
            const tempNpxPath = path.join(app.getPath('temp'), 'npx.cmd');
            const npxContent = `@echo off\r\n"${nodePath}" "${path.join(bundledNodeDir, 'node_modules', 'npm', 'bin', 'npx-cli.js')}" %*`;
            fs.writeFileSync(tempNpxPath, npxContent);
            console.log('创建临时npx批处理文件:', tempNpxPath);
            env.add_timeout = tempNpxPath;
          } catch (err) {
            console.error('创建临时npx批处理文件失败:', err);
          }
        }
        
        console.log('更新后的PATH:', env.PATH);
        console.log('NODE_PATH:', env.NODE_PATH);
        console.log('NPM_CONFIG_PREFIX:', env.NPM_CONFIG_PREFIX);
      }

      console.log('工作目录:', workingDir);
      console.log('UV_PATH:', env.UV_PATH);
      console.log('UVX_PATH:', env.UVX_PATH);
      
      // 设置启动选项
      let spawnOptions = {
        detached: false,
        stdio: 'pipe',
        cwd: workingDir,
        env: env,
        windowsHide: true,
        shell: process.platform === 'win32' // Windows下使用shell以确保批处理文件可以执行
      };
      
      // 命令和参数
      let command, args;
      
      if (isNodeScript) {
        command = nodePath; // 使用捆绑的Node或系统Node
        args = [agentPath, secretKey, userDataPath];
        console.log('以Node脚本方式启动:', command, args);
      } else {
        command = agentPath;
        args = [secretKey, userDataPath];
        console.log('以可执行文件方式启动:', command, args);
      }

      // 启动进程
      console.log('启动命令:', command, '参数:', args);
      agentProcess06012 = childProcess.spawn(command, args, spawnOptions);
      
      // 设置编码以正确读取输出
      agentProcess06012.stdout.setEncoding('utf8');
      agentProcess06012.stderr.setEncoding('utf8');
      
      // 监听stdout输出
      agentProcess06012.stdout.on('data', (data) => {
        console.log(`Live2DAgentServer stdout: ${data}`);
        // 检查是否包含启动成功的标志
        if (data.includes('启动成功') || 
            data.includes('http://0.0.0.0:60086') || 
            data.includes('Application startup complete') || 
            data.includes('Uvicorn running') || 
            data.includes('ready')) {
          console.log('在stdout中检测到启动成功信息');
          if (loadingWindow06012) {
            loadingWindow06012.webContents.send('update-status06012', '服务启动成功');
          }
          resolve('服务启动成功');
          return;
        }
        if (loadingWindow06012) {
          loadingWindow06012.webContents.send('update-status06012', `服务输出: ${data.trim()}`);
        }
      });
      
      // 监听stderr输出
      agentProcess06012.stderr.on('data', (data) => {
        console.error(`Live2DAgentServer stderr: ${data}`);
        
        // 检查是否包含启动成功信息
        if (data.includes('Application startup complete') || 
            data.includes('http://0.0.0.0:60086') || 
            data.includes('Uvicorn running')) {
          console.log('在stderr中检测到启动成功信息');
          loadingWindow06012.webContents.send('update-status06012', `服务启动成功: ${data.trim()}`);
          resolve('服务启动成功');
          return;
        }
        
        // 不是成功信息才标记为错误
        if (loadingWindow06012) {
          loadingWindow06012.webContents.send('update-status06012', `服务输出: ${data.trim()}`);
        }
      });
      
      // 进程退出处理
      agentProcess06012.on('close', (code, signal) => {
        console.log(`Live2DAgentServer进程退出，退出码: ${code}, 信号: ${signal}`);
        
        if (code !== 0) {
          // 获取日志文件位置
          const logFilePath = path.join(userDataPath, 'live2d-agent-error.log');
          
          // 记录错误信息
          try {
            fs.writeFileSync(logFilePath, 
              `退出时间: ${new Date().toISOString()}\n` +
              `退出码: ${code}\n` +
              `信号: ${signal}\n` +
              `命令路径: ${command}\n` +
              `参数: ${args.join(' ')}\n` +
              `工作目录: ${workingDir}\n` +
              `平台: ${process.platform}\n` +
              `Node版本: ${process.version}\n` +
              `Electron版本: ${process.versions.electron}\n` +
              `使用捆绑Node: ${usesBundledNode}\n`
            );
            console.log('错误日志已写入:', logFilePath);
          } catch (err) {
            console.error('写入错误日志失败:', err);
          }
          
          reject(`Live2DAgentServer异常退出，退出码: ${code}, 信号: ${signal}。已记录详细日志。`);
        }
      });
      
      // 进程错误处理
      agentProcess06012.on('error', (err) => {
        console.error('启动Live2DAgentServer失败:', err);
        
        // 获取日志文件位置
        const logFilePath = path.join(userDataPath, 'live2d-agent-error.log');
        
        // 记录错误信息
        try {
          fs.writeFileSync(logFilePath, 
            `错误时间: ${new Date().toISOString()}\n` +
            `错误信息: ${err.message}\n` +
            `错误堆栈: ${err.stack}\n` +
            `命令路径: ${command}\n` +
            `参数: ${args.join(' ')}\n` +
            `工作目录: ${workingDir}\n` +
            `平台: ${process.platform}\n` +
            `Node版本: ${process.version}\n` +
            `Electron版本: ${process.versions.electron}\n` +
            `使用捆绑Node: ${usesBundledNode}\n`
          );
          console.log('错误日志已写入:', logFilePath);
        } catch (logErr) {
          console.error('写入错误日志失败:', logErr);
        }
        
        reject(`启动Live2DAgentServer失败: ${err.message}。已记录详细日志。`);
      });
      
      // 设置超时
      setTimeout(() => {
        if (!agentProcess06012.killed) {
          reject('启动Live2DAgentServer超时，请检查程序是否正常');
        }
      }, 60000);
      
    } catch (error) {
      console.error('启动Live2DAgentServer异常:', error);
      reject(`启动Live2DAgentServer异常: ${error.message}`);
    }
  });
}

// 应用退出时清理进程和临时文件
function cleanupProcesses06012() {
  // 清理Live2DAgentServer进程
  if (agentProcess06012 && !agentProcess06012.killed) {
    try {
      // 在Windows中使用taskkill强制终止进程树
      if (process.platform === 'win32') {
        try {
          // 使用taskkill命令强制终止进程及其子进程
          childProcess.execSync(`taskkill /pid ${agentProcess06012.pid} /T /F`);
          console.log(`Live2DAgentServer进程(PID:${agentProcess06012.pid})已强制终止`);
        } catch (taskkillError) {
          console.error('使用taskkill终止进程失败:', taskkillError);
          // 如果taskkill失败，回退到普通kill方法
          agentProcess06012.kill();
        }
      } else {
        // 非Windows平台使用SIGTERM信号终止
        agentProcess06012.kill('SIGTERM');
      }
      console.log('Live2DAgentServer进程已终止');
    } catch (error) {
      console.error('终止Live2DAgentServer进程失败:', error);
    }
  }

  // 清理Live2D模型管理服务器进程
  if (live2dServerProcess && !live2dServerProcess.killed) {
    try {
      // 在Windows中使用taskkill强制终止进程树
      if (process.platform === 'win32') {
        try {
          // 使用taskkill命令强制终止进程及其子进程
          childProcess.execSync(`taskkill /pid ${live2dServerProcess.pid} /T /F`);
          console.log(`Live2D服务器进程(PID:${live2dServerProcess.pid})已强制终止`);
        } catch (taskkillError) {
          console.error('使用taskkill终止Live2D服务器进程失败:', taskkillError);
          // 如果taskkill失败，回退到普通kill方法
          live2dServerProcess.kill();
        }
      } else {
        // 非Windows平台使用SIGTERM信号终止
        live2dServerProcess.kill('SIGTERM');
      }
      console.log('Live2D服务器进程已终止');
    } catch (error) {
      console.error('终止Live2D服务器进程失败:', error);
    }
  }

  // 清理临时文件
  if (tempNpxPath && fs.existsSync(tempNpxPath)) {
    try {
      fs.unlinkSync(tempNpxPath);
      console.log('已删除临时npx脚本文件:', tempNpxPath);
    } catch (error) {
      console.error('删除临时npx脚本文件失败:', error);
    }
  }
}

app.whenReady().then(async () => {
  // 设置应用名称
  app.name = '兴河 AI Assistant';
  
  // 初始化Node.js和NPM环境
  await initBundledNodeEnvironment().catch(err => {
    console.error('初始化Node环境失败:', err);
  });
  
  // 测试Node环境
  const envOk = await testBundledNodeEnv();
  console.log('Node环境准备状态:', envOk ? '正常' : '异常');
  
  // 先获取密钥
  secretKey = handleSecretKey();
  
  // 创建加载窗口
  createLoadingWindow06012();
  
  // 启动Live2D服务
  loadingWindow06012.webContents.on('did-finish-load', () => {
    // 显示正在启动Live2D模型管理服务器的状态
    loadingWindow06012.webContents.send('update-status06012', '正在启动Live2D模型管理服务器...');
    loadingWindow06012.webContents.send('update-progress06012', 25);

    // 先启动Live2D模型管理服务器
    startLive2DModelServer()
      .then(() => {
        // Live2D模型管理服务器启动成功，继续启动Agent服务器
        loadingWindow06012.webContents.send('update-status06012', 'Live2D模型管理服务器启动成功，正在启动Agent服务...');
        loadingWindow06012.webContents.send('update-progress06012', 50);

        // 启动Live2DAgentServer
        return startLive2DAgentServer06012(secretKey, app.getPath('userData'));
      })
      .then(() => {
        // 所有服务启动成功
        loadingWindow06012.webContents.send('update-status06012', '所有Live2D服务启动成功，正在加载主界面...');
        loadingWindow06012.webContents.send('update-progress06012', 100);

        // 延迟一会再创建主窗口，让用户看到100%的进度
        setTimeout(() => {
          createWindow();

          // 在主窗口完全加载后，关闭加载窗口
          mainWindow.webContents.on('did-finish-load', () => {
            if (loadingWindow06012) {
              loadingWindow06012.close();
              loadingWindow06012 = null;
            }
          });
        }, 1000);
      })
      .catch((error) => {
        console.error('启动Live2D服务失败:', error);
        loadingWindow06012.webContents.send('update-status06012', `启动失败: ${error.message || error}`);

        // 启动失败也创建主窗口，让用户可以使用其他功能
        setTimeout(() => {
          createWindow();

          // 在主窗口完全加载后，关闭加载窗口
          mainWindow.webContents.on('did-finish-load', () => {
            if (loadingWindow06012) {
              loadingWindow06012.close();
              loadingWindow06012 = null;
            }
          });
        }, 3000);
      });
  });

  // 添加IPC处理程序，允许渲染进程获取密钥
  ipcMain.handle('get-secret-key', () => {
    return secretKey;
  });

  // 添加IPC处理程序，允许渲染进程获取Live2DAgentServer的状态
  ipcMain.handle('get-live2d-agent-status06012', () => {
    if (agentProcess06012 && !agentProcess06012.killed) {
      return { running: true };
    } else {
      return { running: false };
    }
  });

  // 添加IPC处理程序，用于重启Live2DAgentServer
  ipcMain.handle('restart-live2d-agent06012', async () => {
    try {
      // 先清理现有进程
      if (agentProcess06012 && !agentProcess06012.killed) {
        cleanupProcesses06012();
        
        // 等待进程完全退出
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // 重新启动服务
      await startLive2DAgentServer06012(secretKey, app.getPath('userData'));
      
      return { success: true };
    } catch (error) {
      console.error('重启Live2DAgentServer失败:', error);
      return { 
        success: false, 
        error: error.message || '未知错误' 
      };
    }
  });

  // 添加IPC处理程序，打开用户数据目录
  ipcMain.on('open-user-data-directory06011', () => {
    const userDataPath06011 = app.getPath('userData');
    shell.openPath(userDataPath06011)
      .then(result => {
        if (result !== '') {
          console.log(`打开用户数据目录失败: ${result}`);
        }
      })
      .catch(err => {
        console.error('打开用户数据目录时出错:', err);
      });
  });

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// 应用退出时清理资源
app.on('will-quit', cleanupProcesses06012);

// 添加额外的退出事件监听器
app.on('before-quit', cleanupProcesses06012);

// 确保主窗口关闭时也清理进程
app.on('window-all-closed', () => {
  // 先清理进程
  cleanupProcesses06012();
  
  // 在Windows上延迟退出一小段时间，确保进程有时间完全终止
  if (process.platform === 'win32') {
    setTimeout(() => {
      app.quit();
    }, 500);  // 500ms的延迟通常足够进程完全退出
  } else {
    // 非Windows平台直接退出
    app.quit();
  }
});

// 修改initApp函数，添加提示词选择器初始化
function initApp() {
    // 初始化调整大小的功能
    initResizers();
    
    // 初始化代码块格式化
    setupCodeBlockObserver();
    
    // 初始化工具调用历史记录
    initToolHistory();
    
    // 初始化连接状态指示器
    initConnectionStatus();
    
    // 初始化主题设置
    initTheme();
    
    // 初始化工具调用开关
    initToolsToggle();
    
    // 初始化联网搜索开关
    initOnlineSearchToggle();
    
    // 初始化RAG开关
    initRAGToggle();
    
    // 初始化模型选择器
    initModelSearch();
    
    // 初始化新版模型管理界面
    if (typeof initModelManager05303 === 'function') {
        initModelManager05303();
    }
    
    // 初始化提示词选择器
    if (typeof window.initPromptSelector === 'function') {
        window.initPromptSelector();
    }
    
    // 初始化Agent管理
    if (typeof window.agentManager !== 'undefined' && typeof window.agentManager.initAgentSelector === 'function') {
        window.agentManager.initAgentSelector();
    }
    
    // 初始化形象系统
    if (typeof window.avatarSystemManager !== 'undefined' && typeof window.avatarSystemManager.initAvatarSystem === 'function') {
        window.avatarSystemManager.initAvatarSystem();
    }
    
    // 初始化工具调用监视器
    setupToolCallingObserver();
    
    // 设置服务器和应用程序的状态
    updateServerState();
}

function loadChat(chatId) {
    console.log('加载会话:', chatId);
    
    // 设置当前活动会话ID
    activeChatId = chatId;
    
    // 清空消息容器
    const messagesContainer = document.querySelector('.messages-container');
    messagesContainer.innerHTML = '';
    
    // 更新活动状态
    document.querySelectorAll('.chat-item').forEach(i => i.classList.remove('active'));
    document.querySelector(`.chat-item[data-chat-id="${chatId}"]`).classList.add('active');
    
    // 更新发送按钮状态
    updateSendButtonStatus(chatId);
    
    // 如果有历史记录，则显示历史消息
    if (chatHistory.has(chatId)) {
        const history = chatHistory.get(chatId);
        
        // 检查会话是否有正在进行的流式响应
        const hasActiveStream = activeStreams.has(chatId) && !activeStreams.get(chatId).complete;
        
        // 如果会话有正在进行的响应，排除最后一个AI响应，以便单独处理
        const messagesToRender = hasActiveStream ? 
            history.filter((msg, idx, arr) => {
                // 如果是最后一个AI响应，不在这里渲染
                return !(idx === arr.length - 1 && msg.role === 'assistant');
            }) : history;
        
        // 使用消息渲染器渲染历史消息（除了可能正在流式生成的最后一条）
        if (window.messageRenderer && typeof window.messageRenderer.renderMessages === 'function') {
            window.messageRenderer.renderMessages(messagesToRender, messagesContainer);
        } else {
            // 如果消息渲染器不可用，则添加警告
            console.warn('消息渲染器不可用，无法渲染历史消息');
        }
        
        // 如果有正在进行的流式响应，单独处理
        if (hasActiveStream) {
            const streamData = activeStreams.get(chatId);
            
            // 创建一个临时的助手消息对象用于流式响应
            const tempAssistantMsg = {
                role: 'assistant',
                content: streamData.content || '',
                timestamp: streamData.time // 使用保存的时间戳
            };
            
            // 使用消息渲染器创建流式响应元素
            let streamingElement;
            if (window.messageRenderer && typeof window.messageRenderer.createMessageElement === 'function') {
                streamingElement = window.messageRenderer.createMessageElement(tempAssistantMsg, streamData.time);
                
                // 添加streaming类
                const contentElement = streamingElement.querySelector('.message-content');
                if (contentElement) {
                    contentElement.classList.add('streaming');
                    
                    // 确保有.ai-response元素
                    if (!contentElement.querySelector('.ai-response')) {
                        const responseElement = document.createElement('p');
                        responseElement.className = 'ai-response';
                        responseElement.innerHTML = streamData.content ? marked.parse(streamData.content) : '';
                        contentElement.insertBefore(responseElement, contentElement.firstChild);
                    }
                }
                
                // 添加到消息容器
                messagesContainer.appendChild(streamingElement);
                
                // 获取响应内容元素
                const responseElement = streamingElement.querySelector('.ai-response');
                
                // 如果没有内容则显示加载指示器
                if (!streamData.content) {
                    const loadingHtml = `
                        <div class="loading-indicator">
                            <div class="loading-spinner"></div>
                            <span>AI思考中</span>
                        </div>
                    `;
                    responseElement.innerHTML = loadingHtml;
                }
                
                // 更新流数据中的responseElement引用
                streamData.responseElement = responseElement;
            } else {
                // 降级处理：使用原始方法创建消息元素
                console.warn('消息渲染器不可用，使用降级方式处理流式响应');
                
                // 创建AI回复消息元素
                const aiMessageElement = document.createElement('div');
                aiMessageElement.className = 'message ai-message';
                
                // 创建AI头像
                const aiAvatar = document.createElement('div');
                aiAvatar.className = 'message-avatar';
                aiAvatar.innerHTML = '<img src="app/assets/robot-icon.svg" alt="AI">';
                aiMessageElement.appendChild(aiAvatar);
                
                // 创建消息内容
                const contentElement = document.createElement('div');
                contentElement.className = 'message-content';
                // 如果流仍在活动中，添加streaming类
                if (!streamData.complete) {
                    contentElement.classList.add('streaming');
                }
                contentElement.innerHTML = `
                    <p class="ai-response"></p>
                    <div class="message-time">${streamData.time}</div>
                `;
                aiMessageElement.appendChild(contentElement);
                
                messagesContainer.appendChild(aiMessageElement);
                
                // 获取响应内容元素
                const responseElement = contentElement.querySelector('.ai-response');
                
                // 显示正在进行的流式响应或加载指示器
                if (streamData.content) {
                    try {
                        responseElement.innerHTML = marked.parse(streamData.content);
                    } catch (e) {
                        responseElement.textContent = streamData.content;
                    }
                } else {
                    const loadingHtml = `
                        <div class="loading-indicator">
                            <div class="loading-spinner"></div>
                            <span>AI思考中</span>
                        </div>
                    `;
                    responseElement.innerHTML = loadingHtml;
                }
                
                // 更新流数据中的responseElement引用
                streamData.responseElement = responseElement;
            }
        }
    }
    
    // 滚动到底部
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // 添加系统日志
    addSystemLog(`加载会话: ${chatId}`, 'info');
}

function sendMessage() {
    const input = document.getElementById('varArea');
    const messagesContainer = document.querySelector('.messages-container');
    
    // 获取用户输入
    let userMessage = input.value.trim();
    if (!userMessage) return;
    
    // 获取图片附件
    const imageAttachments = getImageAttachments();
    if (imageAttachments.length > 0) {
        userMessage = `${userMessage}\n${imageAttachments.map(img => `![image](${img.src})`).join('\n')}`;
    }
    
    // 获取文件附件（待实现）
    
    // 清空输入框
    input.value = '';
    
    // 重置输入框高度
    input.style.height = 'auto';
    
    // 清空附件预览
    clearImageAttachments();
    
    // 获取当前时间
    const currentTime = getCurrentTime();
    
    // 创建用户消息对象
    const userMsg = {
        role: 'user',
        content: userMessage,
        timestamp: currentTime  // 添加时间戳
    };
    
    // 创建并添加用户消息元素到UI
    let userMessageElement;
    
    // 使用消息渲染器创建用户消息元素
    if (window.messageRenderer && typeof window.messageRenderer.createMessageElement === 'function') {
        userMessageElement = window.messageRenderer.createMessageElement(userMsg, currentTime);
    } else {
        // 降级处理：使用原始方法创建消息元素
        userMessageElement = document.createElement('div');
        userMessageElement.className = 'message user-message';
        
        // 添加头像
        const avatarElement = document.createElement('div');
        avatarElement.className = 'message-avatar';
        avatarElement.innerHTML = '<img src="app/assets/user-icon.svg" alt="用户">';
        userMessageElement.appendChild(avatarElement);
        
        // 添加消息内容
        const contentElement = document.createElement('div');
        contentElement.className = 'message-content';
        contentElement.innerHTML = `
            <p>${userMessage}</p>
            <div class="message-time">${currentTime}</div>
        `;
        userMessageElement.appendChild(contentElement);
    }
    
    // 添加到消息容器
    messagesContainer.appendChild(userMessageElement);
    
    // 滚动到底部
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // 更新聊天历史（在内存中）
    let history = [];
    if (chatHistory.has(activeChatId)) {
        history = chatHistory.get(activeChatId);
    }
    history.push(userMsg);
    
    // 创建AI响应消息元素
    let aiMessageElement;
    
    // 使用消息渲染器创建AI响应消息元素的基本结构
    const assistantMsg = {
        role: 'assistant',
        content: '', // 初始为空
        timestamp: currentTime  // 添加时间戳
    };
    
    if (window.messageRenderer && typeof window.messageRenderer.createMessageElement === 'function') {
        aiMessageElement = window.messageRenderer.createMessageElement(assistantMsg, currentTime);
        
        // 为流式响应添加streaming类
        const contentElement = aiMessageElement.querySelector('.message-content');
        if (contentElement) {
            contentElement.classList.add('streaming');
            // 创建AI响应占位符
            if (!contentElement.querySelector('.ai-response')) {
                const responseElement = document.createElement('p');
                responseElement.className = 'ai-response';
                contentElement.insertBefore(responseElement, contentElement.firstChild);
            }
        }
    } else {
        // 降级处理：使用原始方法创建消息元素
        aiMessageElement = document.createElement('div');
        aiMessageElement.className = 'message ai-message';
        
        // 添加头像
        const aiAvatar = document.createElement('div');
        aiAvatar.className = 'message-avatar';
        aiAvatar.innerHTML = '<img src="app/assets/robot-icon.svg" alt="AI">';
        aiMessageElement.appendChild(aiAvatar);
        
        // 添加消息内容
        const contentElement = document.createElement('div');
        contentElement.className = 'message-content streaming';
        contentElement.innerHTML = `
            <p class="ai-response"></p>
            <div class="message-time">${currentTime}</div>
        `;
        aiMessageElement.appendChild(contentElement);
    }
    
    // 添加到消息容器
    messagesContainer.appendChild(aiMessageElement);
    
    // 获取响应内容元素
    const responseElement = aiMessageElement.querySelector('.ai-response');
    
    // 添加加载动画
    const loadingHtml = `
        <div class="loading-indicator">
            <div class="loading-spinner"></div>
            <span>AI思考中</span>
        </div>
    `;
    responseElement.innerHTML = loadingHtml;
    
    // 滚动到底部
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // 禁用发送按钮，避免重复发送
    const sendButton = document.getElementById('send-btn');
    sendButton.disabled = true;
    
    // 记录当前的模型选择
    const modelSelect = document.getElementById('model-select');
    const selectedModel = modelSelect ? modelSelect.value : '';
    
    // 获取服务器地址
    const serverUrl = localStorage.getItem('serverUrl');
    if (!serverUrl) {
        responseElement.innerHTML = '<p class="error-message">错误: 请先配置服务器地址</p>';
        sendButton.disabled = false;
        return;
    }
    
    // 创建流式响应跟踪对象
    const streamData = {
        content: '',
        time: currentTime,
        responseElement: responseElement,
        complete: false
    };
    activeStreams.set(activeChatId, streamData);
    
    // 创建要发送的消息数组
    const messages = buildPromptWithHistory(history);
    
    // 发送请求并处理响应
    streamResponse(serverUrl, messages, selectedModel, responseElement, history, activeChatId);
}

function clearMessages() {
    if (!confirm('确定要清除当前会话的所有消息吗？')) {
        return;
    }
    
    // 清除消息容器
    const messagesContainer = document.querySelector('.messages-container');
    messagesContainer.innerHTML = '';
    
    // 清除活动会话的消息历史
    if (activeChatId && chatHistory.has(activeChatId)) {
        // 保留系统消息，删除其他所有消息
        const systemMessages = chatHistory.get(activeChatId).filter(msg => msg.role === 'system');
        chatHistory.set(activeChatId, systemMessages);
        
        // 保存更新后的聊天历史
        if (window.chatStorage && typeof window.chatStorage.saveChatHistory === 'function') {
            window.chatStorage.saveChatHistory(chatHistory);
        }
    }
    
    // 清除正在进行的流式响应
    if (activeChatId && activeStreams.has(activeChatId)) {
        activeStreams.delete(activeChatId);
    }
    
    // 添加系统日志
    addSystemLog('已清除当前会话的所有消息', 'info');
    
    // 显示通知
    showNotification('消息已清除');
}

// 获取工具调用结果
function getToolCallResult(callId) {
    const serverUrl = localStorage.getItem('serverUrl');
    
    if (!serverUrl) {
        addSystemLog('获取工具执行结果失败：未连接到服务器', 'error');
        showNotification('未连接到服务器，无法获取工具执行结果', true);
        return;
    }
    
    // 显示加载提示
    showNotification('正在获取工具执行结果...', false);
    
    const resultUrl = serverUrl + "/getCallToolResult";
    
    // 构造请求参数
    const params = {
        call_id: callId
    };
    
    // 发送POST请求
    fetch(resultUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('serverToken'),
            'hashid': localStorage.getItem('hashid')
        },
        body: JSON.stringify(params)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`服务器返回错误: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === "success") {
            // 显示工具执行结果弹窗
            showToolResultModal(data.content.exec_result);
            addSystemLog(`成功获取工具执行结果，ID: ${callId}`, 'info');
            
            // 使用工具响应处理器将结果添加到消息中
            if (window.toolResponseHandler && typeof window.toolResponseHandler.processToolCallResult === 'function') {
                const result = data.content.exec_result;
                const success = window.toolResponseHandler.processToolCallResult(callId, result, activeChatId);
                console.log(`工具响应处理${success ? '成功' : '失败'}: ${callId}`);
            } else {
                console.warn('工具响应处理器不可用，无法添加工具响应到消息中');
            }
        } else {
            throw new Error(`获取工具执行结果失败: ${data.errorMessages || '未知错误'}`);
        }
    })
    .catch(error => {
        const errorMsg = error.message.includes('调用工具还未执行完毕') ? 
            '工具正在执行中，请稍后再试' : 
            `获取工具执行结果失败: ${error.message}`;
            
        addSystemLog(errorMsg, 'error');
        showNotification(errorMsg, true);
        console.error("获取工具执行结果失败:", error);
    });
}

// 处理工具调用数据
function processToolCallsData(toolCalls) {
    // 如果没有初始化已处理ID集合，则初始化
    if (!window.processedCallIds) {
        window.processedCallIds = new Set();
    }
    
    // 处理每个工具调用
    toolCalls.forEach(toolCall => {
        const callId = toolCall.call_id;
        
        // 如果此调用ID已处理过，则跳过
        if (window.processedCallIds.has(callId)) {
            return;
        }
        
        // 标记为已处理
        window.processedCallIds.add(callId);
        
        // 处理参数字符串，将其转换为对象
        let params;
        try {
            // 尝试解析参数字符串
            params = JSON.parse(toolCall.params);
        } catch (e) {
            // 如果解析失败，则使用原始字符串
            params = toolCall.params;
        }
        
        // 添加到工具调用历史记录
        addToolHistoryRecord(
            toolCall.tool_name,
            toolCall.tool_description,
            params,
            callId,  // 传入调用ID
            toolCall.create_time,
            toolCall.control  // 传入创建时间
        );
        
        // 如果工具调用已经有结果，添加到消息列表中
        if (toolCall.exec_result && window.toolResponseHandler && typeof window.toolResponseHandler.processToolCallResult === 'function') {
            const success = window.toolResponseHandler.processToolCallResult(callId, toolCall.exec_result, activeChatId);
            console.log(`工具调用结果自动处理${success ? '成功' : '失败'}: ${callId}`);
            
            // 添加系统日志
            if (typeof addSystemLog === 'function') {
                addSystemLog(`工具调用 ${toolCall.tool_name} 结果已处理`, 'info');
            }
        }
    });
} 