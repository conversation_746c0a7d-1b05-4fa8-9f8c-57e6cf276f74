#!/usr/bin/env node

/**
 * 调试Live2D服务器启动问题
 * 详细记录启动过程和可能的错误
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const net = require('net');

console.log('🔍 调试Live2D服务器启动问题...\n');

// 检查端口是否被占用
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve(true); // 端口可用
      });
      server.close();
    });
    
    server.on('error', () => {
      resolve(false); // 端口被占用
    });
  });
}

// 检查进程是否还在运行
function checkProcessRunning(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch (e) {
    return false;
  }
}

async function debugStartup() {
  console.log('📍 环境信息:');
  console.log('  当前目录:', process.cwd());
  console.log('  Node.js版本:', process.version);
  console.log('  平台:', process.platform);
  
  // 检查文件路径
  const live2dServerPath = path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js');
  const live2dWorkingDir = path.join(__dirname, 'app', 'live2d');

  // 根据平台选择正确的Node.js路径
  let nodePath;
  if (process.platform === 'win32') {
    nodePath = path.join(__dirname, 'node-v23.6.1-windows-x64', 'node.exe');
  } else {
    nodePath = path.join(__dirname, 'node-v23.6.1-darwin-arm64', 'bin', 'node');
  }
  
  console.log('\n📁 文件检查:');
  console.log('  Live2D服务器脚本:', live2dServerPath, fs.existsSync(live2dServerPath) ? '✅' : '❌');
  console.log('  工作目录:', live2dWorkingDir, fs.existsSync(live2dWorkingDir) ? '✅' : '❌');
  console.log('  内嵌Node.js:', nodePath, fs.existsSync(nodePath) ? '✅' : '❌');
  
  // 检查依赖
  const nodeModulesPath = path.join(live2dWorkingDir, 'node_modules');
  console.log('  Live2D依赖:', nodeModulesPath, fs.existsSync(nodeModulesPath) ? '✅' : '❌');
  
  if (fs.existsSync(nodeModulesPath)) {
    const deps = fs.readdirSync(nodeModulesPath);
    console.log('    依赖列表:', deps.join(', '));
  }
  
  // 检查端口
  console.log('\n🔌 端口检查:');
  const portAvailable = await checkPort(3001);
  console.log('  端口3001可用:', portAvailable ? '✅' : '❌');
  
  if (!portAvailable) {
    console.log('  ⚠️  端口3001被占用，可能有其他Live2D服务器在运行');
  }
  
  // 测试内嵌Node.js
  console.log('\n🧪 测试内嵌Node.js:');
  try {
    const testProcess = spawn(nodePath, ['--version'], { stdio: 'pipe' });
    
    testProcess.stdout.on('data', (data) => {
      console.log('  ✅ Node.js版本:', data.toString().trim());
    });
    
    testProcess.on('error', (err) => {
      console.log('  ❌ Node.js测试失败:', err.message);
    });
    
    await new Promise((resolve) => {
      testProcess.on('exit', (code) => {
        console.log('  📤 Node.js测试退出码:', code);
        resolve();
      });
    });
  } catch (error) {
    console.log('  ❌ Node.js测试异常:', error.message);
  }
  
  // 启动Live2D服务器并详细监控
  console.log('\n🚀 启动Live2D服务器 (详细模式):');
  
  const env = {
    ...process.env,
    NODE_PATH: path.join(live2dWorkingDir, 'node_modules'),
    DEBUG: '*' // 启用调试输出
  };
  
  console.log('  环境变量 NODE_PATH:', env.NODE_PATH);
  console.log('  命令:', nodePath, [live2dServerPath]);
  console.log('  工作目录:', live2dWorkingDir);
  
  const serverProcess = spawn(nodePath, [live2dServerPath], {
    cwd: live2dWorkingDir,
    stdio: 'pipe',
    env: env,
    detached: false
  });
  
  console.log('  进程PID:', serverProcess.pid);
  
  let outputReceived = false;
  let errorReceived = false;
  
  // 监听所有输出
  serverProcess.stdout.on('data', (data) => {
    outputReceived = true;
    const output = data.toString();
    console.log(`[STDOUT] ${output.trim()}`);
  });
  
  serverProcess.stderr.on('data', (data) => {
    errorReceived = true;
    const output = data.toString();
    console.log(`[STDERR] ${output.trim()}`);
  });
  
  serverProcess.on('spawn', () => {
    console.log('  ✅ 进程已启动');
  });
  
  serverProcess.on('error', (err) => {
    console.log(`  ❌ 进程启动失败: ${err.message}`);
  });
  
  serverProcess.on('exit', (code, signal) => {
    console.log(`  📤 进程退出: 退出码=${code}, 信号=${signal}`);
    
    if (code === 0) {
      console.log('  ✅ 正常退出');
    } else if (code !== null) {
      console.log(`  ❌ 异常退出，退出码: ${code}`);
    }
    
    if (signal) {
      console.log(`  🔔 收到信号: ${signal}`);
    }
  });
  
  serverProcess.on('close', (code, signal) => {
    console.log(`  🔒 进程关闭: 退出码=${code}, 信号=${signal}`);
  });
  
  // 监控进程状态
  let checkCount = 0;
  const statusInterval = setInterval(() => {
    checkCount++;
    const isRunning = checkProcessRunning(serverProcess.pid);
    console.log(`  [${checkCount}s] 进程状态: ${isRunning ? '运行中' : '已停止'}`);
    
    if (!isRunning || checkCount >= 15) {
      clearInterval(statusInterval);
      
      // 最终检查端口
      setTimeout(async () => {
        console.log('\n🔍 最终检查:');
        const finalPortCheck = await checkPort(3001);
        console.log('  端口3001状态:', finalPortCheck ? '未监听' : '正在监听');
        
        if (!outputReceived && !errorReceived) {
          console.log('  ⚠️  没有收到任何输出，可能是启动脚本问题');
        }
        
        console.log('\n💡 调试建议:');
        if (!finalPortCheck) {
          console.log('  ✅ 端口3001正在监听，服务器可能启动成功');
        } else {
          console.log('  ❌ 端口3001未监听，可能的原因:');
          console.log('    1. 进程启动后立即退出');
          console.log('    2. 依赖模块缺失或版本不兼容');
          console.log('    3. 权限问题');
          console.log('    4. 端口绑定失败');
        }
        
        // 清理
        if (checkProcessRunning(serverProcess.pid)) {
          console.log('\n🛑 停止测试进程...');
          serverProcess.kill('SIGTERM');
        }
        
        process.exit(0);
      }, 1000);
    }
  }, 1000);
  
  // 安全退出
  process.on('SIGINT', () => {
    console.log('\n🛑 调试被中断');
    if (checkProcessRunning(serverProcess.pid)) {
      serverProcess.kill('SIGTERM');
    }
    process.exit(1);
  });
}

debugStartup().catch((error) => {
  console.error('❌ 调试失败:', error.message);
  process.exit(1);
});
