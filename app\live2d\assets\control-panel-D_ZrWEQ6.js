import{L as h,M as p,a as g}from"./lappdelegate-CVQV028K.js";class b{constructor(){this._currentModelIndex=0,this._speechBubble=null,this._bubbleText=null,this._bubblePosition="center",this._bubbleTimeout=null,this._isDragging=!1,this._dragOffset={x:0,y:0},this._isDraggingModel=!1,this._isScalingModel=!1,this._lastMouseX=0,this._lastMouseY=0,this._modelScale=1,this._modelOffsetX=0,this._modelOffsetY=0,this._modelDragEnabled=!0}async initialize(){this._delegate=h.getInstance(),this._delegate.initialize(),this._live2DManager=this._delegate.getLive2DManager(),await this.initializeUI(),this._delegate.run()}async initializeUI(){await this.initializeModelSelector(),this.initializeControlButtons(),this.initializeSpeechBubble(),this.initializeModelUpload(),this.initializeModelDragAndScale(),this.initializePanelToggle(),this.setupModelLoadListener()}async initializeModelSelector(){const e=document.getElementById("modelSelector"),s=document.getElementById("deleteModelBtn"),t=await this._live2DManager.detectAvailableModels();e.innerHTML='<option value="">选择模型...</option>',t.forEach((o,i)=>{const n=document.createElement("option");n.value=i.toString(),n.textContent=`${o.name} ${o.status==="active"?"✓":"⚠️"}`,n.setAttribute("data-model-name",o.name),n.setAttribute("data-has-expressions",o.hasExpressions.toString()),n.setAttribute("data-has-motions",o.hasMotions.toString()),e.appendChild(n)}),e.addEventListener("change",async o=>{const i=o.target,n=parseInt(i.value);if(!isNaN(n)&&t[n]){const l=t[n];await this.switchModelByName(l.name),s.style.display="block",s.setAttribute("data-model-name",l.name),this.displayModelInfo(l)}else s.style.display="none",this.hideModelInfo()}),s.addEventListener("click",()=>{const o=s.getAttribute("data-model-name");o&&this.deleteModel(o)}),t.length>0&&(e.value="0",await this.switchModelByName(t[0].name),s.style.display="block",s.setAttribute("data-model-name",t[0].name),this.displayModelInfo(t[0]))}initializeControlButtons(){const e=document.getElementById("randomMotionBtn");e==null||e.addEventListener("click",()=>{this._live2DManager.playRandomMotion(p),this.updateStatus("播放随机动作")});const s=document.getElementById("stopMotionBtn");s==null||s.addEventListener("click",()=>{this._live2DManager.stopAllMotions(),this.updateStatus("停止所有动作")});const t=document.getElementById("randomExpressionBtn");t==null||t.addEventListener("click",()=>{this._live2DManager.setRandomExpression(),this.updateStatus("设置随机表情")});const o=document.getElementById("resetExpressionBtn");o==null||o.addEventListener("click",()=>{const n=this._live2DManager.getCurrentModel();n&&(n.getExpressionManager().stopAllMotions(),this.updateStatus("重置表情"))});const i=document.getElementById("testLipSyncBtn");i==null||i.addEventListener("click",()=>{this.testLipSync()})}initializeSpeechBubble(){this._speechBubble=document.getElementById("speechBubble"),this._bubbleText=document.getElementById("bubbleText");const e=document.getElementById("showBubbleBtn");e==null||e.addEventListener("click",()=>{this.showSpeechBubble()});const s=document.getElementById("hideBubbleBtn");s==null||s.addEventListener("click",()=>{this.hideSpeechBubble()});const t=document.getElementById("bubbleTextInput");t==null||t.addEventListener("input",i=>{const n=i.target;this._bubbleText&&(this._bubbleText.textContent=n.value)});const o=document.querySelectorAll(".position-button");o.forEach(i=>{i.addEventListener("click",n=>{const l=n.target,a=l.getAttribute("data-position");a&&(o.forEach(r=>r.classList.remove("active")),l.classList.add("active"),this.setBubblePosition(a))})}),this.setBubblePosition(this._bubblePosition),this.setupBubbleDragging(),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="欢迎使用Live2D控制面板！",this.showSpeechBubble())},3e3)}setupModelLoadListener(){const e=async()=>{this._live2DManager.isModelReady()?(this.updateModelInfo(),await this.updateMotionButtons(),await this.updateExpressionButtons(),this.updateStatus("模型加载完成")):setTimeout(e,100)};e()}switchModel(e){this._currentModelIndex=e,this._live2DManager.switchToModel(e),this.updateStatus("正在加载模型..."),this.clearButtons(),this.setupModelLoadListener()}async switchModelByName(e){this._live2DManager.loadModelByName(e),this.updateStatus(`正在加载模型: ${e}...`),this.clearButtons(),this.setupModelLoadListener();try{const s=await this._live2DManager.validateModel(e);s&&!s.isValid&&this.updateStatus(`模型 ${e} 存在问题: ${s.analysis.issues.join(", ")}`)}catch(s){console.warn("模型验证失败:",s)}}displayModelInfo(e){const s=document.getElementById("modelInfo"),t=document.getElementById("motionCount"),o=document.getElementById("expressionCount");s&&t&&o&&(s.style.display="block",t.textContent=e.motionGroups?e.motionGroups.length.toString():"0",o.textContent=e.expressions?e.expressions.length.toString():"0")}hideModelInfo(){const e=document.getElementById("modelInfo");e&&(e.style.display="none")}getCurrentModelName(){const e=document.getElementById("modelSelector");return e&&e.selectedIndex>0?e.options[e.selectedIndex].getAttribute("data-model-name"):null}updateModelInfo(){const e=this._live2DManager.getCurrentModel();if(!e||!e.getModelSetting())return;const s=this._live2DManager.getMotionGroups(),t=this._live2DManager.getExpressionNames();let o=0;s.forEach(a=>{o+=this._live2DManager.getMotionCount(a)});const i=document.getElementById("motionCount"),n=document.getElementById("expressionCount"),l=document.getElementById("modelInfo");i&&(i.textContent=o.toString()),n&&(n.textContent=t.length.toString()),l&&(l.style.display="grid")}async updateMotionButtons(){const e=document.getElementById("motionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载动作...</div>';try{if(this._live2DManager.getCurrentModel()){const o=this.getCurrentModelName();if(o){const i=await this._live2DManager.getModelMotionsFromServer(o);e.innerHTML="";for(const n in i)i.hasOwnProperty(n)&&i[n].forEach((a,r)=>{if(a.exists){const u=document.createElement("button");u.className="control-button motion",u.textContent=`${n} ${r+1}`,u.addEventListener("click",()=>{this._live2DManager.playMotion(n,r),this.updateStatus(`播放动作: ${n} ${r+1}`),this.showMotionBubble(n,r)}),e.appendChild(u)}});e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>');return}}}catch(t){console.warn("从服务器获取动作信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getMotionGroups().forEach(t=>{const o=this._live2DManager.getMotionCount(t);for(let i=0;i<o;i++){const n=document.createElement("button");n.className="control-button motion",n.textContent=`${t} ${i+1}`,n.addEventListener("click",()=>{this._live2DManager.playMotion(t,i),this.updateStatus(`播放动作: ${t} ${i+1}`),this.showMotionBubble(t,i)}),e.appendChild(n)}}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用动作</div>')}async updateExpressionButtons(){const e=document.getElementById("expressionButtons");if(!e)return;e.innerHTML='<div class="loading">正在加载表情...</div>';try{if(this._live2DManager.getCurrentModel()){const o=this.getCurrentModelName();if(o){const i=await this._live2DManager.getModelExpressionsFromServer(o);e.innerHTML="",i.forEach(n=>{const l=document.createElement("button");l.className="control-button expression",l.textContent=n,l.addEventListener("click",()=>{this._live2DManager.setExpression(n),this.updateStatus(`设置表情: ${n}`)}),e.appendChild(l)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>');return}}}catch(t){console.warn("从服务器获取表情信息失败，使用本地方法:",t)}e.innerHTML="",this._live2DManager.getExpressionNames().forEach(t=>{const o=document.createElement("button");o.className="control-button expression",o.textContent=t,o.addEventListener("click",()=>{this._live2DManager.setExpression(t),this.updateStatus(`设置表情: ${t}`)}),e.appendChild(o)}),e.children.length===0&&(e.innerHTML='<div class="loading">暂无可用表情</div>')}clearButtons(){const e=document.getElementById("motionButtons"),s=document.getElementById("expressionButtons"),t=document.getElementById("modelInfo");e&&(e.innerHTML='<div class="loading">加载中...</div>'),s&&(s.innerHTML='<div class="loading">加载中...</div>'),t&&(t.style.display="none")}showSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.add("show"),this.updateStatus("显示文字气泡"),this._bubbleTimeout=window.setTimeout(()=>{this.hideSpeechBubble()},5e3))}hideSpeechBubble(){this._speechBubble&&(this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null),this._speechBubble.classList.remove("show"),this.updateStatus("隐藏文字气泡"))}showMotionBubble(e,s){if(!this._bubbleText)return;const o={Idle:["我在这里等你哦~","今天天气真不错呢！","你想和我聊什么呢？","我正在想你呢~","有什么我可以帮助你的吗？"],TapBody:["哎呀，你在摸我呢！","好痒啊~","嘻嘻，你真调皮！","不要乱摸啦~","你的手好温暖呢！"]}[e]||["正在播放动作..."];let i;s<o.length?i=o[s]:i=o[Math.floor(Math.random()*o.length)],this._bubbleText.textContent=i;const n=document.getElementById("bubbleTextInput");n&&(n.value=i),this.showSpeechBubble()}setupBubbleDragging(){this._speechBubble&&(this._speechBubble.addEventListener("mousedown",e=>{this._isDragging=!0,this._speechBubble.classList.add("dragging");const s=this._speechBubble.getBoundingClientRect();this._dragOffset.x=e.clientX-s.left,this._dragOffset.y=e.clientY-s.top,e.preventDefault()}),document.addEventListener("mousemove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=document.querySelector(".canvas-container");if(!s)return;const t=s.getBoundingClientRect();let o=e.clientX-t.left-this._dragOffset.x,i=e.clientY-t.top-this._dragOffset.y;const n=this._speechBubble.offsetWidth,l=this._speechBubble.offsetHeight;o=Math.max(10,Math.min(o,t.width-n-10)),i=Math.max(10,Math.min(i,t.height-l-10)),this._speechBubble.style.left=`${o}px`,this._speechBubble.style.top=`${i}px`,this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform="scale(1)"}),document.addEventListener("mouseup",()=>{this._isDragging&&this._speechBubble&&(this._isDragging=!1,this._speechBubble.classList.remove("dragging"))}),this._speechBubble.addEventListener("touchstart",e=>{this._isDragging=!0,this._speechBubble.classList.add("dragging");const s=e.touches[0],t=this._speechBubble.getBoundingClientRect();this._dragOffset.x=s.clientX-t.left,this._dragOffset.y=s.clientY-t.top,e.preventDefault()}),document.addEventListener("touchmove",e=>{if(!this._isDragging||!this._speechBubble)return;const s=e.touches[0],t=document.querySelector(".canvas-container");if(!t)return;const o=t.getBoundingClientRect();let i=s.clientX-o.left-this._dragOffset.x,n=s.clientY-o.top-this._dragOffset.y;const l=this._speechBubble.offsetWidth,a=this._speechBubble.offsetHeight;i=Math.max(10,Math.min(i,o.width-l-10)),n=Math.max(10,Math.min(n,o.height-a-10)),this._speechBubble.style.left=`${i}px`,this._speechBubble.style.top=`${n}px`,this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform="scale(1)"}),document.addEventListener("touchend",()=>{this._isDragging&&this._speechBubble&&(this._isDragging=!1,this._speechBubble.classList.remove("dragging"))}))}setBubblePosition(e){if(this._speechBubble)switch(this._bubblePosition=e,this._speechBubble.classList.remove("top","bottom","left","right"),this._speechBubble.style.left="",this._speechBubble.style.top="",this._speechBubble.style.right="",this._speechBubble.style.bottom="",this._speechBubble.style.transform="",e){case"bottom-left":this._speechBubble.style.left="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom");break;case"bottom-right":this._speechBubble.style.right="50px",this._speechBubble.style.bottom="50px",this._speechBubble.classList.add("bottom","right");break;case"top-left":this._speechBubble.style.left="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top");break;case"top-right":this._speechBubble.style.right="50px",this._speechBubble.style.top="50px",this._speechBubble.classList.add("top","right");break;case"center":default:this._speechBubble.style.left="50%",this._speechBubble.style.top="50%",this._speechBubble.style.transform="translate(-50%, -50%)";break}}testLipSync(){const e=this._live2DManager.getCurrentModel();if(!e){this.updateStatus("请先选择一个模型");return}this._bubbleText&&(this._bubbleText.textContent="正在测试口型同步功能...",this.showSpeechBubble());try{const s="../../Resources/测试.wav";e._lipsync=!0;const t=new Audio(s);t.volume=.8;const o=e._wavFileHandler;o?(Promise.all([t.play(),Promise.resolve(o.start(s))]).then(()=>{this.updateStatus("开始播放测试音频，观察口型同步效果"),setTimeout(()=>{this._bubbleText&&(this._bubbleText.textContent="口型同步测试中，请观察嘴部动画！")},1e3)}).catch(i=>{console.error("播放音频失败:",i),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("口型同步测试完成"),this._bubbleText&&(this._bubbleText.textContent="口型同步测试完成！")})):this.updateStatus("音频处理器未初始化")}catch(s){console.error("测试口型同步失败:",s),this.updateStatus("测试口型同步失败，请检查音频文件")}}changeModel(e){typeof e=="string"?(this.switchModelByName(e),this.updateStatus(`切换到模型: ${e}`)):typeof e=="number"?this.changeModelByDynamicIndex(e):this.updateStatus(`无效的模型参数: ${e}`)}async changeModelByDynamicIndex(e){try{const s=await this._live2DManager.detectAvailableModels();if(e>=0&&e<s.length){const t=s[e];await this.switchModelByName(t.name),this.updateStatus(`切换到模型: ${t.name} (索引: ${e})`);const o=document.getElementById("modelSelector");o&&(o.value=e.toString())}else this.updateStatus(`无效的模型索引: ${e}，可用模型数量: ${s.length}`)}catch(s){this.updateStatus(`切换模型失败: ${s instanceof Error?s.message:String(s)}`)}}playMotion(e,s=0){this._live2DManager.getCurrentModel()?(this._live2DManager.playMotion(e,s),this.updateStatus(`播放动作: ${e} ${s+1}`),this.showMotionBubble(e,s)):this.updateStatus("请先选择一个模型")}playExpression(e){const s=this._live2DManager.getExpressionNames();if(e>=0&&e<s.length){const t=s[e];this._live2DManager.setExpression(t),this.updateStatus(`播放表情: ${t}`)}else this.updateStatus(`无效的表情索引: ${e}`)}playRandomExpression(){this._live2DManager.setRandomExpression(),this.updateStatus("播放随机表情")}showBubble(e,s=!0){if(this._bubbleText){this._bubbleText.textContent=e;const t=document.getElementById("bubbleTextInput");t&&(t.value=e)}this.showSpeechBubble(),s||this._bubbleTimeout&&(clearTimeout(this._bubbleTimeout),this._bubbleTimeout=null)}hideBubble(){this.hideSpeechBubble()}playAudioWithLipSync(e){const s=this._live2DManager.getCurrentModel();if(!s){this.updateStatus("请先选择一个模型");return}try{s._lipsync=!0;const t=new Audio(`../../Resources/${e}`);t.volume=.8;const o=s._wavFileHandler;o?(Promise.all([t.play(),Promise.resolve(o.start(`../../Resources/${e}`))]).then(()=>{this.updateStatus(`播放音频: ${e}`)}).catch(i=>{console.error("播放音频失败:",i),this.updateStatus("播放音频失败，请检查音频文件和浏览器权限")}),t.addEventListener("ended",()=>{this.updateStatus("音频播放完成")})):this.updateStatus("音频处理器未初始化")}catch(t){console.error("播放音频失败:",t),this.updateStatus("播放音频失败，请检查音频文件")}}getCurrentModelInfo(){return this._live2DManager.getCurrentModel()?{modelName:"Current Model",hasModel:!0,motionGroups:this._live2DManager.getMotionGroups(),expressions:this._live2DManager.getExpressionNames()}:{modelName:"No Model",hasModel:!1,motionGroups:[],expressions:[]}}async getAvailableModels(){try{return await this._live2DManager.detectAvailableModels()}catch(e){return console.error("获取模型列表失败:",e),[]}}updateStatus(e){const s=document.getElementById("statusText");s&&(s.textContent=e),g.printMessage(`[Control Panel] ${e}`)}initializeModelUpload(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectFileBtn"),t=document.getElementById("uploadBtn"),o=document.getElementById("selectedFileName");s.addEventListener("click",()=>{e.click()}),e.addEventListener("change",i=>{var a;const n=i.target,l=(a=n.files)==null?void 0:a[0];l&&(l.type==="application/zip"||l.name.endsWith(".zip")?(o.textContent=`已选择: ${l.name}`,o.style.display="block",t.style.display="block",this.updateStatus(`已选择文件: ${l.name}`)):(alert("请选择ZIP格式的文件"),n.value=""))}),t.addEventListener("click",()=>{var n;const i=(n=e.files)==null?void 0:n[0];i&&this.uploadModel(i)})}async uploadModel(e){const s=document.getElementById("uploadStatus"),t=document.getElementById("uploadStatusText"),o=document.getElementById("progressBar"),i=document.getElementById("uploadBtn");try{s.style.display="block",s.className="upload-status",t.textContent="正在上传...",o.style.width="0%",i.disabled=!0;const n=new FormData;n.append("modelZip",e);const l=await fetch("http://localhost:3001/api/upload-model",{method:"POST",body:n});if(l.ok){const a=await l.json();if(s.className="upload-status success",t.textContent=`上传成功! 模型 ${a.modelName} 已添加`,o.style.width="100%",a.analysis){const r=[];a.analysis.hasExpressions&&r.push(`表情: ${a.analysis.expressions.length}个`),a.analysis.hasMotions&&r.push(`动作组: ${a.analysis.motionGroups.length}个`),a.analysis.issues&&a.analysis.issues.length>0&&r.push(`问题: ${a.analysis.issues.length}个`),r.length>0&&(t.textContent+=` (${r.join(", ")})`)}await this.refreshModelList(),this.clearFileSelection(),this.updateStatus(`模型 ${a.modelName} 上传成功`)}else{const a=await l.json();throw new Error(a.error||"上传失败")}}catch(n){const l=n instanceof Error?n.message:"未知错误";s.className="upload-status error",t.textContent=`上传失败: ${l}`,this.updateStatus(`上传失败: ${l}`)}finally{i.disabled=!1}}async refreshModelList(){try{await this.initializeModelSelector(),this.updateStatus("模型列表已更新")}catch(e){console.error("刷新模型列表失败:",e),this.updateStatus("刷新模型列表失败")}}clearFileSelection(){const e=document.getElementById("modelFileInput"),s=document.getElementById("selectedFileName"),t=document.getElementById("uploadBtn");e.value="",s.style.display="none",t.style.display="none"}async deleteModel(e){if(confirm(`确定要删除模型 "${e}" 吗？此操作不可撤销。`))try{this.updateStatus(`正在删除模型: ${e}...`);const s=await fetch(`http://localhost:3001/api/models/${e}`,{method:"DELETE"});if(s.ok){await s.json(),this.updateStatus(`模型 ${e} 删除成功`),await this.refreshModelList();const t=document.getElementById("deleteModelBtn");t.style.display="none"}else{const t=await s.json();throw new Error(t.error||"删除失败")}}catch(s){const t=s instanceof Error?s.message:"未知错误";this.updateStatus(`删除失败: ${t}`),alert(`删除模型失败: ${t}`)}}initializeModelDragAndScale(){this.waitForCanvasAndBindEvents()}waitForCanvasAndBindEvents(){const e=()=>{const s=document.querySelector(".canvas-container"),t=s==null?void 0:s.querySelector("canvas");t?(console.log("Canvas found, binding drag and scale events"),this.bindDragAndScaleEvents(t)):setTimeout(e,100)};e()}bindDragAndScaleEvents(e){console.log("Binding mouse events to canvas"),e.addEventListener("mousedown",t=>{console.log("Mouse down event:",t.ctrlKey,t.shiftKey),t.ctrlKey?(this._isScalingModel=!0,this._lastMouseY=t.clientY,console.log("Scaling mode enabled")):t.shiftKey&&(this._isDraggingModel=!0,this._lastMouseX=t.clientX,this._lastMouseY=t.clientY,console.log("Dragging mode enabled")),t.preventDefault()}),e.addEventListener("mousemove",t=>{if(this._isDraggingModel){const o=t.clientX-this._lastMouseX,i=t.clientY-this._lastMouseY;this._modelOffsetX+=o*.002,this._modelOffsetY-=i*.002,console.log("Dragging model:",o,i,"New offset:",this._modelOffsetX,this._modelOffsetY),this.applyModelTransform(),this._lastMouseX=t.clientX,this._lastMouseY=t.clientY}else if(this._isScalingModel){const i=1+(t.clientY-this._lastMouseY)*.003;this._modelScale*=i,this._modelScale=Math.max(.1,Math.min(5,this._modelScale)),console.log("Scaling model:",i,"New scale:",this._modelScale),this.applyModelTransform(),this._lastMouseY=t.clientY}}),e.addEventListener("mouseup",()=>{console.log("Mouse up - stopping drag/scale"),this._isDraggingModel=!1,this._isScalingModel=!1}),e.addEventListener("touchstart",t=>{if(t.touches.length===1)this._isDraggingModel=!0,this._lastMouseX=t.touches[0].clientX,this._lastMouseY=t.touches[0].clientY;else if(t.touches.length===2){this._isScalingModel=!0;const o=t.touches[0],i=t.touches[1],n=Math.sqrt(Math.pow(i.clientX-o.clientX,2)+Math.pow(i.clientY-o.clientY,2));this._lastMouseY=n}t.preventDefault()}),e.addEventListener("touchmove",t=>{if(this._isDraggingModel&&t.touches.length===1){const o=t.touches[0].clientX-this._lastMouseX,i=t.touches[0].clientY-this._lastMouseY;this._modelOffsetX+=o*.002,this._modelOffsetY-=i*.002,this.applyModelTransform(),this._lastMouseX=t.touches[0].clientX,this._lastMouseY=t.touches[0].clientY}else if(this._isScalingModel&&t.touches.length===2){const o=t.touches[0],i=t.touches[1],n=Math.sqrt(Math.pow(i.clientX-o.clientX,2)+Math.pow(i.clientY-o.clientY,2)),l=1+(n-this._lastMouseY)*.001;this._modelScale*=l,this._modelScale=Math.max(.1,Math.min(5,this._modelScale)),this.applyModelTransform(),this._lastMouseY=n}t.preventDefault()}),e.addEventListener("touchend",()=>{this._isDraggingModel=!1,this._isScalingModel=!1}),e.addEventListener("wheel",t=>{if(t.ctrlKey){t.preventDefault();const o=t.deltaY>0?.95:1.05;this._modelScale*=o,this._modelScale=Math.max(.1,Math.min(5,this._modelScale)),this.applyModelTransform()}});const s=document.getElementById("resetTransformBtn");s&&s.addEventListener("click",()=>{this.resetModelTransform()})}applyModelTransform(){console.log("Applying model transform:",this._modelScale,this._modelOffsetX,this._modelOffsetY);const e=this._delegate._subdelegates;if(console.log("Subdelegates:",e),e&&e.getSize()>0){const s=e.at(0);console.log("Subdelegate:",s);const t=s._view;if(console.log("View:",t),t){const o=t._viewMatrix;console.log("ViewMatrix:",o),o?(o.loadIdentity(),o.scale(1,1),o.scale(this._modelScale,this._modelScale),o.translateX(this._modelOffsetX),o.translateY(this._modelOffsetY),console.log("Transform applied successfully"),this.updateStatus(`模型变换 - 缩放: ${this._modelScale.toFixed(2)}, 位置: (${this._modelOffsetX.toFixed(2)}, ${this._modelOffsetY.toFixed(2)})`)):console.log("ViewMatrix not found")}else console.log("View not found")}else console.log("Subdelegates not found or empty")}resetModelTransform(){this._modelScale=1,this._modelOffsetX=0,this._modelOffsetY=0,this.applyModelTransform(),this.updateStatus("模型变换已重置")}initializePanelToggle(){const e=document.getElementById("panelToggleBtn"),s=document.getElementById("controlPanel");if(e&&s){const t=()=>{s.classList.contains("hidden")?(s.classList.remove("hidden"),console.log("Control panel shown"),this.updateStatus("控制面板已显示")):(s.classList.add("hidden"),console.log("Control panel hidden"),this.updateStatus("控制面板已隐藏"))};e.addEventListener("click",t),window.addEventListener("keydown",o=>{o.key==="F1"&&!o.ctrlKey&&!o.shiftKey&&!o.altKey&&(o.preventDefault(),o.stopPropagation(),console.log("F1 key pressed, toggling panel"),t())},!0),window.addEventListener("message",o=>{o.data&&o.data.type==="toggle_panel"&&(console.log("Received toggle panel message from parent"),t())})}}release(){h.releaseInstance()}}let c;window.addEventListener("DOMContentLoaded",async()=>{c=new b,await c.initialize(),window.Live2DControlPanel={changeModel:d=>c.changeModel(d),playMotion:(d,e=0)=>c.playMotion(d,e),playExpression:d=>c.playExpression(d),playRandomExpression:()=>c.playRandomExpression(),showBubble:(d,e=!0)=>c.showBubble(d,e),hideBubble:()=>c.hideBubble(),playAudio:d=>c.playAudioWithLipSync(d),uploadModel:d=>c.uploadModel(d),refreshModelList:()=>c.refreshModelList(),deleteModel:d=>c.deleteModel(d),getModelInfo:()=>c.getCurrentModelInfo(),getAvailableModels:()=>c.getAvailableModels(),_app:c},window.addEventListener("message",d=>{var e,s,t;if(d.data&&d.data.type==="live2d_api_call"){const{messageId:o,method:i,args:n}=d.data;try{const l=window.Live2DControlPanel;if(l&&typeof l[i]=="function"){const a=l[i](...n);a&&typeof a.then=="function"?a.then(r=>{var u;(u=d.source)==null||u.postMessage({type:"live2d_api_response",messageId:o,success:!0,result:r},"*")}).catch(r=>{var u;(u=d.source)==null||u.postMessage({type:"live2d_api_response",messageId:o,success:!1,error:r.message||"调用失败"},"*")}):(e=d.source)==null||e.postMessage({type:"live2d_api_response",messageId:o,success:!0,result:a},"*")}else(s=d.source)==null||s.postMessage({type:"live2d_api_response",messageId:o,success:!1,error:`方法 ${i} 不存在`},"*")}catch(l){(t=d.source)==null||t.postMessage({type:"live2d_api_response",messageId:o,success:!1,error:l.message||"调用失败"},"*")}}})});window.addEventListener("beforeunload",()=>{c&&c.release()});
//# sourceMappingURL=control-panel-D_ZrWEQ6.js.map
