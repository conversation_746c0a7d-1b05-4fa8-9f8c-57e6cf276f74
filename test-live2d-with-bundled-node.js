#!/usr/bin/env node

/**
 * 测试使用内嵌Node.js启动Live2D服务器
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 测试使用内嵌Node.js启动Live2D服务器...\n');

// 内嵌Node.js路径 (根据平台选择)
let bundledNodePath;
if (process.platform === 'win32') {
  bundledNodePath = path.join(__dirname, 'node-v23.6.1-windows-x64', 'node.exe');
} else {
  bundledNodePath = path.join(__dirname, 'node-v23.6.1-darwin-arm64', 'bin', 'node');
}
const live2dServerPath = path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js');
const live2dWorkingDir = path.join(__dirname, 'app', 'live2d');

console.log('📍 路径信息:');
console.log('  内嵌Node.js:', bundledNodePath);
console.log('  Live2D服务器脚本:', live2dServerPath);
console.log('  工作目录:', live2dWorkingDir);

// 检查文件是否存在
if (!fs.existsSync(bundledNodePath)) {
  console.error('❌ 内嵌Node.js不存在:', bundledNodePath);
  process.exit(1);
}

if (!fs.existsSync(live2dServerPath)) {
  console.error('❌ Live2D服务器脚本不存在:', live2dServerPath);
  process.exit(1);
}

console.log('✅ 所有文件都存在\n');

// 设置环境变量
const env = {
  ...process.env,
  NODE_PATH: path.join(__dirname, 'app', 'live2d', 'node_modules')
};

console.log('🚀 启动Live2D服务器...');
console.log('  命令:', bundledNodePath);
console.log('  参数:', [live2dServerPath]);
console.log('  环境变量 NODE_PATH:', env.NODE_PATH);

// 启动Live2D服务器
const serverProcess = spawn(bundledNodePath, [live2dServerPath], {
  cwd: live2dWorkingDir,
  stdio: 'pipe',
  env: env
});

let serverStarted = false;
let testTimeout;

// 监听输出
serverProcess.stdout.on('data', (data) => {
  const output = data.toString().trim();
  console.log(`[Live2D Server] ${output}`);
  
  // 检查启动成功标志
  if (output.includes('服务地址: http://localhost:3001') || 
      output.includes('Live2D服务器启动成功') ||
      output.includes('Server running on port 3001')) {
    console.log('\n✅ Live2D服务器启动成功！');
    serverStarted = true;
    
    // 测试HTTP连接
    setTimeout(testConnection, 2000);
  }
});

serverProcess.stderr.on('data', (data) => {
  const output = data.toString().trim();
  console.error(`[Live2D Server Error] ${output}`);
  
  // 检查是否是依赖错误
  if (output.includes('Cannot find module') || output.includes('MODULE_NOT_FOUND')) {
    console.error('\n❌ 依赖模块缺失');
    cleanup();
  }
  
  // 某些启动信息可能在stderr中
  if (output.includes('服务地址: http://localhost:3001') || 
      output.includes('Live2D服务器启动成功') ||
      output.includes('Server running on port 3001')) {
    console.log('\n✅ Live2D服务器启动成功！');
    serverStarted = true;
    
    // 测试HTTP连接
    setTimeout(testConnection, 2000);
  }
});

serverProcess.on('error', (err) => {
  console.error('\n❌ 启动Live2D服务器失败:', err.message);
  cleanup();
});

serverProcess.on('exit', (code) => {
  if (code !== 0) {
    console.error(`\n❌ Live2D服务器异常退出，退出码: ${code}`);
  }
  cleanup();
});

// 测试HTTP连接
function testConnection() {
  console.log('\n🔗 测试HTTP连接...');
  
  const http = require('http');
  const req = http.get('http://localhost:3001', (res) => {
    console.log(`✅ HTTP连接成功，状态码: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      if (data.includes('Live2D') || data.includes('html')) {
        console.log('✅ 服务器响应正常');
      } else {
        console.log('⚠️  服务器响应异常');
      }
      
      console.log('\n🎉 测试完成！Live2D服务器使用内嵌Node.js启动成功');
      cleanup();
    });
  });
  
  req.on('error', (err) => {
    console.error('❌ HTTP连接失败:', err.message);
    cleanup();
  });
  
  req.setTimeout(5000, () => {
    console.error('❌ HTTP连接超时');
    req.destroy();
    cleanup();
  });
}

// 清理资源
function cleanup() {
  if (testTimeout) {
    clearTimeout(testTimeout);
  }
  
  if (serverProcess && !serverProcess.killed) {
    console.log('\n🛑 停止Live2D服务器...');
    serverProcess.kill('SIGTERM');
    
    setTimeout(() => {
      if (!serverProcess.killed) {
        console.log('🔥 强制终止Live2D服务器...');
        serverProcess.kill('SIGKILL');
      }
      process.exit(serverStarted ? 0 : 1);
    }, 2000);
  } else {
    process.exit(serverStarted ? 0 : 1);
  }
}

// 设置超时
testTimeout = setTimeout(() => {
  if (!serverStarted) {
    console.error('\n❌ Live2D服务器启动超时');
    cleanup();
  }
}, 15000);

// 处理进程信号
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

console.log('⏳ 等待Live2D服务器启动...');
