{"version": 3, "file": "control-panel-DwPu2h1P.js", "sources": ["../../src/control-panel.ts"], "sourcesContent": ["/**\n * Copyright(c) Live2D Inc. All rights reserved.\n *\n * Use of this source code is governed by the Live2D Open Software license\n * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.\n */\n\nimport { LAppDelegate } from './lappdelegate';\nimport { LAppLive2DManager } from './lapplive2dmanager';\nimport { LAppPal } from './lapppal';\nimport * as LAppDefine from './lappdefine';\n\n/**\n * 控制面板应用程序类\n * 管理Live2D模型的控制界面\n */\nclass ControlPanelApp {\n  private _delegate: LAppDelegate;\n  private _live2DManager: LAppLive2DManager;\n  private _currentModelIndex: number = 0;\n  private _speechBubble: HTMLElement | null = null;\n  private _bubbleText: HTMLElement | null = null;\n  private _bubblePosition: string = 'center';\n  private _bubbleTimeout: number | null = null;\n  private _isDragging: boolean = false;\n  private _dragOffset: { x: number; y: number } = { x: 0, y: 0 };\n\n  // 模型拖拽和缩放相关\n  private _isDraggingModel: boolean = false;\n  private _isScalingModel: boolean = false;\n  private _lastMouseX: number = 0;\n  private _lastMouseY: number = 0;\n  private _modelScale: number = 1.0;\n  private _modelOffsetX: number = 0;\n  private _modelOffsetY: number = 0;\n  private _modelDragEnabled: boolean = true;\n\n  /**\n   * 初始化应用程序\n   */\n  public async initialize(): Promise<void> {\n    // 获取应用程序委托实例\n    this._delegate = LAppDelegate.getInstance();\n    this._delegate.initialize();\n\n    // 获取Live2D管理器\n    this._live2DManager = this._delegate.getLive2DManager();\n\n    // 初始化UI\n    await this.initializeUI();\n\n    // 开始渲染循环\n    this._delegate.run();\n  }\n\n  /**\n   * 初始化用户界面\n   */\n  private async initializeUI(): Promise<void> {\n    // 初始化模型选择器\n    await this.initializeModelSelector();\n\n    // 初始化控制按钮\n    this.initializeControlButtons();\n\n    // 初始化文字气泡\n    this.initializeSpeechBubble();\n\n    // 初始化模型上传功能\n    this.initializeModelUpload();\n\n    // 初始化模型拖拽和缩放\n    this.initializeModelDragAndScale();\n\n    // 初始化面板切换功能\n    this.initializePanelToggle();\n\n    // 监听模型加载完成事件\n    this.setupModelLoadListener();\n  }\n\n  /**\n   * 初始化模型选择器\n   */\n  private async initializeModelSelector(): Promise<void> {\n    const modelSelector = document.getElementById('modelSelector') as HTMLSelectElement;\n    const deleteModelBtn = document.getElementById('deleteModelBtn') as HTMLButtonElement;\n\n    // 动态检测可用模型\n    const availableModels = await this._live2DManager.detectAvailableModels();\n\n    // 清空现有选项\n    modelSelector.innerHTML = '<option value=\"\">选择模型...</option>';\n\n    // 添加模型选项\n    availableModels.forEach((model, index) => {\n      const option = document.createElement('option');\n      option.value = index.toString();\n      option.textContent = `${model.name} ${model.status === 'active' ? '✓' : '⚠️'}`;\n      option.setAttribute('data-model-name', model.name);\n      option.setAttribute('data-has-expressions', model.hasExpressions.toString());\n      option.setAttribute('data-has-motions', model.hasMotions.toString());\n      modelSelector.appendChild(option);\n    });\n\n    // 监听选择变化\n    modelSelector.addEventListener('change', async (event) => {\n      const target = event.target as HTMLSelectElement;\n      const selectedIndex = parseInt(target.value);\n\n      if (!isNaN(selectedIndex) && availableModels[selectedIndex]) {\n        const selectedModel = availableModels[selectedIndex];\n        await this.switchModelByName(selectedModel.name);\n        deleteModelBtn.style.display = 'block';\n        deleteModelBtn.setAttribute('data-model-name', selectedModel.name);\n\n        // 显示模型信息\n        this.displayModelInfo(selectedModel);\n      } else {\n        deleteModelBtn.style.display = 'none';\n        this.hideModelInfo();\n      }\n    });\n\n    // 删除模型按钮事件\n    deleteModelBtn.addEventListener('click', () => {\n      const modelName = deleteModelBtn.getAttribute('data-model-name');\n      if (modelName) {\n        this.deleteModel(modelName);\n      }\n    });\n\n    // 默认选择第一个模型\n    if (availableModels.length > 0) {\n      modelSelector.value = '0';\n      await this.switchModelByName(availableModels[0].name);\n      deleteModelBtn.style.display = 'block';\n      deleteModelBtn.setAttribute('data-model-name', availableModels[0].name);\n\n      // 显示模型信息\n      this.displayModelInfo(availableModels[0]);\n    }\n  }\n\n  /**\n   * 初始化控制按钮\n   */\n  private initializeControlButtons(): void {\n    // 随机动作按钮\n    const randomMotionBtn = document.getElementById('randomMotionBtn');\n    randomMotionBtn?.addEventListener('click', () => {\n      this._live2DManager.playRandomMotion(LAppDefine.MotionGroupIdle);\n      this.updateStatus('播放随机动作');\n    });\n\n    // 停止动作按钮\n    const stopMotionBtn = document.getElementById('stopMotionBtn');\n    stopMotionBtn?.addEventListener('click', () => {\n      this._live2DManager.stopAllMotions();\n      this.updateStatus('停止所有动作');\n    });\n\n    // 随机表情按钮\n    const randomExpressionBtn = document.getElementById('randomExpressionBtn');\n    randomExpressionBtn?.addEventListener('click', () => {\n      this._live2DManager.setRandomExpression();\n      this.updateStatus('设置随机表情');\n    });\n\n    // 重置表情按钮\n    const resetExpressionBtn = document.getElementById('resetExpressionBtn');\n    resetExpressionBtn?.addEventListener('click', () => {\n      // 通过设置空表情来重置\n      const model = this._live2DManager.getCurrentModel();\n      if (model) {\n        model.getExpressionManager().stopAllMotions();\n        this.updateStatus('重置表情');\n      }\n    });\n\n    // 测试口型同步按钮\n    const testLipSyncBtn = document.getElementById('testLipSyncBtn');\n    testLipSyncBtn?.addEventListener('click', () => {\n      this.testLipSync();\n    });\n  }\n\n  /**\n   * 初始化文字气泡\n   */\n  private initializeSpeechBubble(): void {\n    // 获取气泡元素\n    this._speechBubble = document.getElementById('speechBubble');\n    this._bubbleText = document.getElementById('bubbleText');\n\n    // 显示气泡按钮\n    const showBubbleBtn = document.getElementById('showBubbleBtn');\n    showBubbleBtn?.addEventListener('click', () => {\n      this.showSpeechBubble();\n    });\n\n    // 隐藏气泡按钮\n    const hideBubbleBtn = document.getElementById('hideBubbleBtn');\n    hideBubbleBtn?.addEventListener('click', () => {\n      this.hideSpeechBubble();\n    });\n\n    // 文字输入框\n    const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n    bubbleTextInput?.addEventListener('input', (event) => {\n      const target = event.target as HTMLTextAreaElement;\n      if (this._bubbleText) {\n        this._bubbleText.textContent = target.value;\n      }\n    });\n\n    // 位置控制按钮\n    const positionButtons = document.querySelectorAll('.position-button');\n    positionButtons.forEach(button => {\n      button.addEventListener('click', (event) => {\n        const target = event.target as HTMLElement;\n        const position = target.getAttribute('data-position');\n\n        if (position) {\n          // 更新按钮状态\n          positionButtons.forEach(btn => btn.classList.remove('active'));\n          target.classList.add('active');\n\n          // 更新气泡位置\n          this.setBubblePosition(position);\n        }\n      });\n    });\n\n    // 设置初始位置\n    this.setBubblePosition(this._bubblePosition);\n\n    // 添加拖动事件\n    this.setupBubbleDragging();\n\n    // 页面加载后3秒显示欢迎气泡\n    setTimeout(() => {\n      if (this._bubbleText) {\n        this._bubbleText.textContent = '欢迎使用Live2D控制面板！';\n        this.showSpeechBubble();\n      }\n    }, 3000);\n  }\n\n  /**\n   * 设置模型加载监听器\n   */\n  private setupModelLoadListener(): void {\n    // 定期检查模型是否加载完成\n    const checkModelReady = async () => {\n      if (this._live2DManager.isModelReady()) {\n        this.updateModelInfo();\n        await this.updateMotionButtons();\n        await this.updateExpressionButtons();\n        this.updateStatus('模型加载完成');\n      } else {\n        setTimeout(checkModelReady, 100);\n      }\n    };\n\n    checkModelReady();\n  }\n\n  /**\n   * 切换模型\n   */\n  private switchModel(modelIndex: number): void {\n    this._currentModelIndex = modelIndex;\n    this._live2DManager.switchToModel(modelIndex);\n    this.updateStatus('正在加载模型...');\n\n    // 清空按钮\n    this.clearButtons();\n\n    // 等待模型加载完成\n    this.setupModelLoadListener();\n  }\n\n  /**\n   * 根据模型名称切换模型\n   */\n  private async switchModelByName(modelName: string): Promise<void> {\n    this._live2DManager.loadModelByName(modelName);\n    this.updateStatus(`正在加载模型: ${modelName}...`);\n\n    // 清空按钮\n    this.clearButtons();\n\n    // 等待模型加载完成\n    this.setupModelLoadListener();\n\n    // 验证模型\n    try {\n      const validation = await this._live2DManager.validateModel(modelName);\n      if (validation && !validation.isValid) {\n        this.updateStatus(`模型 ${modelName} 存在问题: ${validation.analysis.issues.join(', ')}`);\n      }\n    } catch (error) {\n      console.warn('模型验证失败:', error);\n    }\n  }\n\n  /**\n   * 显示模型信息\n   */\n  private displayModelInfo(model: any): void {\n    const modelInfo = document.getElementById('modelInfo');\n    const motionCount = document.getElementById('motionCount');\n    const expressionCount = document.getElementById('expressionCount');\n\n    if (modelInfo && motionCount && expressionCount) {\n      modelInfo.style.display = 'block';\n      motionCount.textContent = model.motionGroups ? model.motionGroups.length.toString() : '0';\n      expressionCount.textContent = model.expressions ? model.expressions.length.toString() : '0';\n    }\n  }\n\n  /**\n   * 隐藏模型信息\n   */\n  private hideModelInfo(): void {\n    const modelInfo = document.getElementById('modelInfo');\n    if (modelInfo) {\n      modelInfo.style.display = 'none';\n    }\n  }\n\n  /**\n   * 获取当前模型名称\n   */\n  private getCurrentModelName(): string | null {\n    const modelSelector = document.getElementById('modelSelector') as HTMLSelectElement;\n    if (modelSelector && modelSelector.selectedIndex > 0) {\n      const selectedOption = modelSelector.options[modelSelector.selectedIndex];\n      return selectedOption.getAttribute('data-model-name');\n    }\n    return null;\n  }\n\n  /**\n   * 更新模型信息\n   */\n  private updateModelInfo(): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model || !model.getModelSetting()) {\n      return;\n    }\n\n    const motionGroups = this._live2DManager.getMotionGroups();\n    const expressions = this._live2DManager.getExpressionNames();\n\n    // 计算总动作数\n    let totalMotions = 0;\n    motionGroups.forEach(group => {\n      totalMotions += this._live2DManager.getMotionCount(group);\n    });\n\n    // 更新显示\n    const motionCountElement = document.getElementById('motionCount');\n    const expressionCountElement = document.getElementById('expressionCount');\n    const modelInfoElement = document.getElementById('modelInfo');\n\n    if (motionCountElement) motionCountElement.textContent = totalMotions.toString();\n    if (expressionCountElement) expressionCountElement.textContent = expressions.length.toString();\n    if (modelInfoElement) modelInfoElement.style.display = 'grid';\n  }\n\n  /**\n   * 更新动作按钮\n   */\n  private async updateMotionButtons(): Promise<void> {\n    const motionButtonsContainer = document.getElementById('motionButtons');\n    if (!motionButtonsContainer) return;\n\n    motionButtonsContainer.innerHTML = '<div class=\"loading\">正在加载动作...</div>';\n\n    try {\n      // 尝试从服务器获取当前模型的动作信息\n      const currentModel = this._live2DManager.getCurrentModel();\n      if (currentModel) {\n        // 获取模型名称（这里需要一个方法来获取当前模型名称）\n        const modelName = this.getCurrentModelName();\n        if (modelName) {\n          const motionsData = await this._live2DManager.getModelMotionsFromServer(modelName);\n\n          motionButtonsContainer.innerHTML = '';\n\n          for (const groupName in motionsData) {\n            if (motionsData.hasOwnProperty(groupName)) {\n              const motionArray = motionsData[groupName] as any[];\n              motionArray.forEach((motion, index) => {\n                if (motion.exists) {\n                  const button = document.createElement('button');\n                  button.className = 'control-button motion';\n                  button.textContent = `${groupName} ${index + 1}`;\n                  button.addEventListener('click', () => {\n                    this._live2DManager.playMotion(groupName, index);\n                    this.updateStatus(`播放动作: ${groupName} ${index + 1}`);\n                    this.showMotionBubble(groupName, index);\n                  });\n                  motionButtonsContainer.appendChild(button);\n                }\n              });\n            }\n          }\n\n          if (motionButtonsContainer.children.length === 0) {\n            motionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用动作</div>';\n          }\n          return;\n        }\n      }\n    } catch (error) {\n      console.warn('从服务器获取动作信息失败，使用本地方法:', error);\n    }\n\n    // 回退到本地方法\n    motionButtonsContainer.innerHTML = '';\n    const motionGroups = this._live2DManager.getMotionGroups();\n\n    motionGroups.forEach(group => {\n      const motionCount = this._live2DManager.getMotionCount(group);\n\n      for (let i = 0; i < motionCount; i++) {\n        const button = document.createElement('button');\n        button.className = 'control-button motion';\n        button.textContent = `${group} ${i + 1}`;\n        button.addEventListener('click', () => {\n          this._live2DManager.playMotion(group, i);\n          this.updateStatus(`播放动作: ${group} ${i + 1}`);\n          this.showMotionBubble(group, i);\n        });\n        motionButtonsContainer.appendChild(button);\n      }\n    });\n\n    if (motionButtonsContainer.children.length === 0) {\n      motionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用动作</div>';\n    }\n  }\n\n  /**\n   * 更新表情按钮\n   */\n  private async updateExpressionButtons(): Promise<void> {\n    const expressionButtonsContainer = document.getElementById('expressionButtons');\n    if (!expressionButtonsContainer) return;\n\n    expressionButtonsContainer.innerHTML = '<div class=\"loading\">正在加载表情...</div>';\n\n    try {\n      // 尝试从服务器获取当前模型的表情信息\n      const currentModel = this._live2DManager.getCurrentModel();\n      if (currentModel) {\n        const modelName = this.getCurrentModelName();\n        if (modelName) {\n          const expressions = await this._live2DManager.getModelExpressionsFromServer(modelName);\n\n          expressionButtonsContainer.innerHTML = '';\n\n          expressions.forEach(expressionName => {\n            const button = document.createElement('button');\n            button.className = 'control-button expression';\n            button.textContent = expressionName;\n            button.addEventListener('click', () => {\n              this._live2DManager.setExpression(expressionName);\n              this.updateStatus(`设置表情: ${expressionName}`);\n            });\n            expressionButtonsContainer.appendChild(button);\n          });\n\n          if (expressionButtonsContainer.children.length === 0) {\n            expressionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用表情</div>';\n          }\n          return;\n        }\n      }\n    } catch (error) {\n      console.warn('从服务器获取表情信息失败，使用本地方法:', error);\n    }\n\n    // 回退到本地方法\n    expressionButtonsContainer.innerHTML = '';\n    const expressions = this._live2DManager.getExpressionNames();\n\n    expressions.forEach(expressionName => {\n      const button = document.createElement('button');\n      button.className = 'control-button expression';\n      button.textContent = expressionName;\n      button.addEventListener('click', () => {\n        this._live2DManager.setExpression(expressionName);\n        this.updateStatus(`设置表情: ${expressionName}`);\n      });\n      expressionButtonsContainer.appendChild(button);\n    });\n\n    if (expressionButtonsContainer.children.length === 0) {\n      expressionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用表情</div>';\n    }\n  }\n\n  /**\n   * 清空按钮\n   */\n  private clearButtons(): void {\n    const motionButtonsContainer = document.getElementById('motionButtons');\n    const expressionButtonsContainer = document.getElementById('expressionButtons');\n    const modelInfoElement = document.getElementById('modelInfo');\n\n    if (motionButtonsContainer) {\n      motionButtonsContainer.innerHTML = '<div class=\"loading\">加载中...</div>';\n    }\n    if (expressionButtonsContainer) {\n      expressionButtonsContainer.innerHTML = '<div class=\"loading\">加载中...</div>';\n    }\n    if (modelInfoElement) {\n      modelInfoElement.style.display = 'none';\n    }\n  }\n\n  /**\n   * 显示文字气泡\n   */\n  private showSpeechBubble(): void {\n    if (!this._speechBubble) return;\n\n    // 清除之前的自动隐藏定时器\n    if (this._bubbleTimeout) {\n      clearTimeout(this._bubbleTimeout);\n      this._bubbleTimeout = null;\n    }\n\n    // 显示气泡\n    this._speechBubble.classList.add('show');\n    this.updateStatus('显示文字气泡');\n\n    // 5秒后自动隐藏\n    this._bubbleTimeout = window.setTimeout(() => {\n      this.hideSpeechBubble();\n    }, 5000);\n  }\n\n  /**\n   * 隐藏文字气泡\n   */\n  private hideSpeechBubble(): void {\n    if (!this._speechBubble) return;\n\n    // 清除自动隐藏定时器\n    if (this._bubbleTimeout) {\n      clearTimeout(this._bubbleTimeout);\n      this._bubbleTimeout = null;\n    }\n\n    // 隐藏气泡\n    this._speechBubble.classList.remove('show');\n    this.updateStatus('隐藏文字气泡');\n  }\n\n  /**\n   * 显示与动作同步的文字气泡\n   */\n  private showMotionBubble(group: string, motionIndex: number): void {\n    if (!this._bubbleText) return;\n\n    // 根据动作类型显示不同的文字\n    const motionTexts: { [key: string]: string[] } = {\n      'Idle': [\n        '我在这里等你哦~',\n        '今天天气真不错呢！',\n        '你想和我聊什么呢？',\n        '我正在想你呢~',\n        '有什么我可以帮助你的吗？'\n      ],\n      'TapBody': [\n        '哎呀，你在摸我呢！',\n        '好痒啊~',\n        '嘻嘻，你真调皮！',\n        '不要乱摸啦~',\n        '你的手好温暖呢！'\n      ]\n    };\n\n    // 获取对应的文字数组\n    const texts = motionTexts[group] || ['正在播放动作...'];\n\n    // 选择文字（如果有多个动作，按索引选择，否则随机选择）\n    let selectedText: string;\n    if (motionIndex < texts.length) {\n      selectedText = texts[motionIndex];\n    } else {\n      selectedText = texts[Math.floor(Math.random() * texts.length)];\n    }\n\n    // 更新气泡文字\n    this._bubbleText.textContent = selectedText;\n\n    // 同时更新输入框\n    const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n    if (bubbleTextInput) {\n      bubbleTextInput.value = selectedText;\n    }\n\n    // 显示气泡\n    this.showSpeechBubble();\n  }\n\n  /**\n   * 设置气泡拖动功能\n   */\n  private setupBubbleDragging(): void {\n    if (!this._speechBubble) return;\n\n    // 鼠标按下事件\n    this._speechBubble.addEventListener('mousedown', (e: MouseEvent) => {\n      this._isDragging = true;\n      this._speechBubble!.classList.add('dragging');\n\n      const rect = this._speechBubble!.getBoundingClientRect();\n      this._dragOffset.x = e.clientX - rect.left;\n      this._dragOffset.y = e.clientY - rect.top;\n\n      e.preventDefault();\n    });\n\n    // 鼠标移动事件\n    document.addEventListener('mousemove', (e: MouseEvent) => {\n      if (!this._isDragging || !this._speechBubble) return;\n\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n\n      // 计算新位置（相对于canvas容器）\n      let newX = e.clientX - containerRect.left - this._dragOffset.x;\n      let newY = e.clientY - containerRect.top - this._dragOffset.y;\n\n      // 获取气泡的尺寸\n      const bubbleWidth = this._speechBubble.offsetWidth;\n      const bubbleHeight = this._speechBubble.offsetHeight;\n\n      // 限制在画布范围内\n      newX = Math.max(10, Math.min(newX, containerRect.width - bubbleWidth - 10));\n      newY = Math.max(10, Math.min(newY, containerRect.height - bubbleHeight - 10));\n\n      // 设置位置\n      this._speechBubble.style.left = `${newX}px`;\n      this._speechBubble.style.top = `${newY}px`;\n      this._speechBubble.style.right = '';\n      this._speechBubble.style.bottom = '';\n      this._speechBubble.style.transform = '';\n    });\n\n    // 鼠标释放事件\n    document.addEventListener('mouseup', () => {\n      if (this._isDragging && this._speechBubble) {\n        this._isDragging = false;\n        this._speechBubble.classList.remove('dragging');\n      }\n    });\n\n    // 触摸事件支持\n    this._speechBubble.addEventListener('touchstart', (e: TouchEvent) => {\n      this._isDragging = true;\n      this._speechBubble!.classList.add('dragging');\n\n      const touch = e.touches[0];\n      const rect = this._speechBubble!.getBoundingClientRect();\n      this._dragOffset.x = touch.clientX - rect.left;\n      this._dragOffset.y = touch.clientY - rect.top;\n\n      e.preventDefault();\n    });\n\n    document.addEventListener('touchmove', (e: TouchEvent) => {\n      if (!this._isDragging || !this._speechBubble) return;\n\n      const touch = e.touches[0];\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n\n      // 计算新位置（相对于canvas容器）\n      let newX = touch.clientX - containerRect.left - this._dragOffset.x;\n      let newY = touch.clientY - containerRect.top - this._dragOffset.y;\n\n      // 获取气泡的尺寸\n      const bubbleWidth = this._speechBubble.offsetWidth;\n      const bubbleHeight = this._speechBubble.offsetHeight;\n\n      // 限制在画布范围内\n      newX = Math.max(10, Math.min(newX, containerRect.width - bubbleWidth - 10));\n      newY = Math.max(10, Math.min(newY, containerRect.height - bubbleHeight - 10));\n\n      // 设置位置\n      this._speechBubble.style.left = `${newX}px`;\n      this._speechBubble.style.top = `${newY}px`;\n      this._speechBubble.style.right = '';\n      this._speechBubble.style.bottom = '';\n      this._speechBubble.style.transform = '';\n    });\n\n    document.addEventListener('touchend', () => {\n      if (this._isDragging && this._speechBubble) {\n        this._isDragging = false;\n        this._speechBubble.classList.remove('dragging');\n      }\n    });\n  }\n\n  /**\n   * 设置气泡位置\n   */\n  private setBubblePosition(position: string): void {\n    if (!this._speechBubble) return;\n\n    this._bubblePosition = position;\n\n    // 移除所有位置类和样式\n    this._speechBubble.classList.remove('top', 'bottom', 'left', 'right');\n    this._speechBubble.style.left = '';\n    this._speechBubble.style.top = '';\n    this._speechBubble.style.right = '';\n    this._speechBubble.style.bottom = '';\n    this._speechBubble.style.transform = '';\n\n    // 根据位置设置样式\n    switch (position) {\n      case 'bottom-left':\n        this._speechBubble.style.left = '50px';\n        this._speechBubble.style.bottom = '50px';\n        this._speechBubble.classList.add('bottom');\n        break;\n      case 'bottom-right':\n        this._speechBubble.style.right = '50px';\n        this._speechBubble.style.bottom = '50px';\n        this._speechBubble.classList.add('bottom', 'right');\n        break;\n      case 'top-left':\n        this._speechBubble.style.left = '50px';\n        this._speechBubble.style.top = '50px';\n        this._speechBubble.classList.add('top');\n        break;\n      case 'top-right':\n        this._speechBubble.style.right = '50px';\n        this._speechBubble.style.top = '50px';\n        this._speechBubble.classList.add('top', 'right');\n        break;\n      case 'center':\n      default:\n        // 默认居中显示\n        this._speechBubble.style.left = '50%';\n        this._speechBubble.style.top = '50%';\n        this._speechBubble.style.transform = 'translate(-50%, -50%)';\n        break;\n    }\n  }\n\n  /**\n   * 测试口型同步功能\n   */\n  private testLipSync(): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    // 显示测试气泡\n    if (this._bubbleText) {\n      this._bubbleText.textContent = '正在测试口型同步功能...';\n      this.showSpeechBubble();\n    }\n\n    // 使用模型自带的wav文件处理器和同时播放音频\n    try {\n      // 获取测试音频文件路径\n      const audioPath = '../../Resources/测试.wav';\n\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audio = new Audio(audioPath);\n      audio.volume = 0.8; // 设置音量\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        // 同时启动音频播放和口型同步\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.start(audioPath))\n        ]).then(() => {\n          this.updateStatus('开始播放测试音频，观察口型同步效果');\n\n          // 更新气泡文字\n          setTimeout(() => {\n            if (this._bubbleText) {\n              this._bubbleText.textContent = '口型同步测试中，请观察嘴部动画！';\n            }\n          }, 1000);\n        }).catch(error => {\n          console.error('播放音频失败:', error);\n          this.updateStatus('播放音频失败，请检查音频文件和浏览器权限');\n        });\n\n        // 音频结束时的处理\n        audio.addEventListener('ended', () => {\n          this.updateStatus('口型同步测试完成');\n          if (this._bubbleText) {\n            this._bubbleText.textContent = '口型同步测试完成！';\n          }\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('测试口型同步失败:', error);\n      this.updateStatus('测试口型同步失败，请检查音频文件');\n    }\n  }\n\n  // ==================== 公共API接口 ====================\n\n  /**\n   * 更换模型\n   * @param modelIndex 模型索引 (0: Haru, 1: Hiyori, 2: Mark, 3: Natori, 4: Rice)\n   */\n  public changeModel(modelIndex: number): void {\n    if (modelIndex >= 0 && modelIndex < LAppDefine.ModelDirSize) {\n      this._live2DManager.switchToModel(modelIndex);\n      this.updateStatus(`切换到模型 ${modelIndex + 1}`);\n    } else {\n      this.updateStatus(`无效的模型索引: ${modelIndex}`);\n    }\n  }\n\n  /**\n   * 播放动作\n   * @param group 动作组名 (如: \"Idle\", \"TapBody\")\n   * @param index 动作索引 (从0开始)\n   */\n  public playMotion(group: string, index: number = 0): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (model) {\n      this._live2DManager.playMotion(group, index);\n      this.updateStatus(`播放动作: ${group} ${index + 1}`);\n\n      // 播放动作时显示相应的文字气泡\n      this.showMotionBubble(group, index);\n    } else {\n      this.updateStatus('请先选择一个模型');\n    }\n  }\n\n  /**\n   * 播放表情\n   * @param index 表情索引 (从0开始)\n   */\n  public playExpression(index: number): void {\n    const expressionNames = this._live2DManager.getExpressionNames();\n    if (index >= 0 && index < expressionNames.length) {\n      const expressionName = expressionNames[index];\n      this._live2DManager.setExpression(expressionName);\n      this.updateStatus(`播放表情: ${expressionName}`);\n    } else {\n      this.updateStatus(`无效的表情索引: ${index}`);\n    }\n  }\n\n  /**\n   * 播放随机表情\n   */\n  public playRandomExpression(): void {\n    this._live2DManager.setRandomExpression();\n    this.updateStatus('播放随机表情');\n  }\n\n  /**\n   * 显示文字气泡\n   * @param text 要显示的文字\n   * @param autoHide 是否自动隐藏 (默认: true, 5秒后隐藏)\n   */\n  public showBubble(text: string, autoHide: boolean = true): void {\n    if (this._bubbleText) {\n      this._bubbleText.textContent = text;\n\n      // 同时更新输入框\n      const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n      if (bubbleTextInput) {\n        bubbleTextInput.value = text;\n      }\n    }\n\n    this.showSpeechBubble();\n\n    if (!autoHide) {\n      // 清除自动隐藏定时器\n      if (this._bubbleTimeout) {\n        clearTimeout(this._bubbleTimeout);\n        this._bubbleTimeout = null;\n      }\n    }\n  }\n\n  /**\n   * 隐藏文字气泡\n   */\n  public hideBubble(): void {\n    this.hideSpeechBubble();\n  }\n\n  /**\n   * 播放音频并进行口型同步\n   * @param audioPath 音频文件路径 (相对于Resources目录)\n   */\n  public playAudioWithLipSync(audioPath: string): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    try {\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audio = new Audio(`../../Resources/${audioPath}`);\n      audio.volume = 0.8;\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.start(`../../Resources/${audioPath}`))\n        ]).then(() => {\n          this.updateStatus(`播放音频: ${audioPath}`);\n        }).catch(error => {\n          console.error('播放音频失败:', error);\n          this.updateStatus('播放音频失败，请检查音频文件和浏览器权限');\n        });\n\n        audio.addEventListener('ended', () => {\n          this.updateStatus('音频播放完成');\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('播放音频失败:', error);\n      this.updateStatus('播放音频失败，请检查音频文件');\n    }\n  }\n\n  /**\n   * 获取当前模型信息\n   */\n  public getCurrentModelInfo(): any {\n    const model = this._live2DManager.getCurrentModel();\n    if (model) {\n      return {\n        modelName: 'Current Model',\n        hasModel: true,\n        motionGroups: this._live2DManager.getMotionGroups(),\n        expressions: this._live2DManager.getExpressionNames()\n      };\n    }\n    return {\n      modelName: 'No Model',\n      hasModel: false,\n      motionGroups: [],\n      expressions: []\n    };\n  }\n\n  // ==================== 私有方法 ====================\n\n  /**\n   * 更新状态信息\n   */\n  private updateStatus(message: string): void {\n    const statusElement = document.getElementById('statusText');\n    if (statusElement) {\n      statusElement.textContent = message;\n    }\n\n    // 在控制台也输出状态信息\n    LAppPal.printMessage(`[Control Panel] ${message}`);\n  }\n\n  /**\n   * 初始化模型上传功能\n   */\n  private initializeModelUpload(): void {\n    const fileInput = document.getElementById('modelFileInput') as HTMLInputElement;\n    const selectFileBtn = document.getElementById('selectFileBtn') as HTMLButtonElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n    const selectedFileName = document.getElementById('selectedFileName') as HTMLDivElement;\n\n    // 选择文件按钮点击事件\n    selectFileBtn.addEventListener('click', () => {\n      fileInput.click();\n    });\n\n    // 文件选择事件\n    fileInput.addEventListener('change', (event) => {\n      const target = event.target as HTMLInputElement;\n      const file = target.files?.[0];\n\n      if (file) {\n        if (file.type === 'application/zip' || file.name.endsWith('.zip')) {\n          selectedFileName.textContent = `已选择: ${file.name}`;\n          selectedFileName.style.display = 'block';\n          uploadBtn.style.display = 'block';\n          this.updateStatus(`已选择文件: ${file.name}`);\n        } else {\n          alert('请选择ZIP格式的文件');\n          target.value = '';\n        }\n      }\n    });\n\n    // 上传按钮点击事件\n    uploadBtn.addEventListener('click', () => {\n      const file = fileInput.files?.[0];\n      if (file) {\n        this.uploadModel(file);\n      }\n    });\n  }\n\n  /**\n   * 上传模型文件\n   */\n  public async uploadModel(file: File): Promise<void> {\n    const uploadStatus = document.getElementById('uploadStatus') as HTMLDivElement;\n    const uploadStatusText = document.getElementById('uploadStatusText') as HTMLParagraphElement;\n    const progressBar = document.getElementById('progressBar') as HTMLDivElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n\n    try {\n      // 显示上传状态\n      uploadStatus.style.display = 'block';\n      uploadStatus.className = 'upload-status';\n      uploadStatusText.textContent = '正在上传...';\n      progressBar.style.width = '0%';\n      uploadBtn.disabled = true;\n\n      // 创建FormData\n      const formData = new FormData();\n      formData.append('modelZip', file);\n\n      // 上传文件\n      const response = await fetch('http://localhost:3001/api/upload-model', {\n        method: 'POST',\n        body: formData\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n\n        // 上传成功\n        uploadStatus.className = 'upload-status success';\n        uploadStatusText.textContent = `上传成功! 模型 ${result.modelName} 已添加`;\n        progressBar.style.width = '100%';\n\n        // 显示分析结果\n        if (result.analysis) {\n          const analysisInfo: string[] = [];\n          if (result.analysis.hasExpressions) {\n            analysisInfo.push(`表情: ${result.analysis.expressions.length}个`);\n          }\n          if (result.analysis.hasMotions) {\n            analysisInfo.push(`动作组: ${result.analysis.motionGroups.length}个`);\n          }\n          if (result.analysis.issues && result.analysis.issues.length > 0) {\n            analysisInfo.push(`问题: ${result.analysis.issues.length}个`);\n          }\n\n          if (analysisInfo.length > 0) {\n            uploadStatusText.textContent += ` (${analysisInfo.join(', ')})`;\n          }\n        }\n\n        // 刷新模型列表\n        await this.refreshModelList();\n\n        // 清空文件选择\n        this.clearFileSelection();\n\n        this.updateStatus(`模型 ${result.modelName} 上传成功`);\n      } else {\n        const error = await response.json();\n        throw new Error(error.error || '上传失败');\n      }\n    } catch (error) {\n      // 上传失败\n      const errorMessage = error instanceof Error ? error.message : '未知错误';\n      uploadStatus.className = 'upload-status error';\n      uploadStatusText.textContent = `上传失败: ${errorMessage}`;\n      this.updateStatus(`上传失败: ${errorMessage}`);\n    } finally {\n      uploadBtn.disabled = false;\n    }\n  }\n\n  /**\n   * 刷新模型列表\n   */\n  public async refreshModelList(): Promise<void> {\n    try {\n      // 重新初始化模型选择器\n      await this.initializeModelSelector();\n      this.updateStatus('模型列表已更新');\n    } catch (error) {\n      console.error('刷新模型列表失败:', error);\n      this.updateStatus('刷新模型列表失败');\n    }\n  }\n\n  /**\n   * 清空文件选择\n   */\n  private clearFileSelection(): void {\n    const fileInput = document.getElementById('modelFileInput') as HTMLInputElement;\n    const selectedFileName = document.getElementById('selectedFileName') as HTMLDivElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n\n    fileInput.value = '';\n    selectedFileName.style.display = 'none';\n    uploadBtn.style.display = 'none';\n  }\n\n  /**\n   * 删除模型\n   */\n  public async deleteModel(modelName: string): Promise<void> {\n    if (!confirm(`确定要删除模型 \"${modelName}\" 吗？此操作不可撤销。`)) {\n      return;\n    }\n\n    try {\n      this.updateStatus(`正在删除模型: ${modelName}...`);\n\n      const response = await fetch(`http://localhost:3001/api/models/${modelName}`, {\n        method: 'DELETE'\n      });\n\n      if (response.ok) {\n        await response.json();\n        this.updateStatus(`模型 ${modelName} 删除成功`);\n\n        // 刷新模型列表\n        await this.refreshModelList();\n\n        // 隐藏删除按钮\n        const deleteModelBtn = document.getElementById('deleteModelBtn') as HTMLButtonElement;\n        deleteModelBtn.style.display = 'none';\n\n      } else {\n        const error = await response.json();\n        throw new Error(error.error || '删除失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '未知错误';\n      this.updateStatus(`删除失败: ${errorMessage}`);\n      alert(`删除模型失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 初始化模型拖拽和缩放功能\n   */\n  private initializeModelDragAndScale(): void {\n    // 等待canvas创建完成后再绑定事件\n    this.waitForCanvasAndBindEvents();\n  }\n\n  /**\n   * 等待canvas创建并绑定拖拽事件\n   */\n  private waitForCanvasAndBindEvents(): void {\n    const checkCanvas = () => {\n      const canvasContainer = document.querySelector('.canvas-container');\n      const canvas = canvasContainer?.querySelector('canvas') as HTMLCanvasElement;\n\n      if (canvas) {\n        console.log('Canvas found, binding drag and scale events');\n        this.bindDragAndScaleEvents(canvas);\n      } else {\n        // 如果canvas还没创建，100ms后再检查\n        setTimeout(checkCanvas, 100);\n      }\n    };\n\n    checkCanvas();\n  }\n\n  /**\n   * 绑定拖拽和缩放事件到canvas\n   */\n  private bindDragAndScaleEvents(canvas: HTMLCanvasElement): void {\n\n    console.log('Binding mouse events to canvas');\n\n    // 鼠标事件\n    canvas.addEventListener('mousedown', (e) => {\n      console.log('Mouse down event:', e.ctrlKey, e.shiftKey);\n      if (e.ctrlKey) {\n        // Ctrl+鼠标左键：缩放模式\n        this._isScalingModel = true;\n        this._lastMouseY = e.clientY;\n        console.log('Scaling mode enabled');\n      } else if (e.shiftKey) {\n        // Shift+鼠标左键：拖拽模式\n        this._isDraggingModel = true;\n        this._lastMouseX = e.clientX;\n        this._lastMouseY = e.clientY;\n        console.log('Dragging mode enabled');\n      }\n      e.preventDefault();\n    });\n\n    canvas.addEventListener('mousemove', (e) => {\n      if (this._isDraggingModel) {\n        const deltaX = e.clientX - this._lastMouseX;\n        const deltaY = e.clientY - this._lastMouseY;\n\n        this._modelOffsetX += deltaX * 0.01;\n        this._modelOffsetY -= deltaY * 0.01; // Y轴反向\n\n        console.log('Dragging model:', deltaX, deltaY, 'New offset:', this._modelOffsetX, this._modelOffsetY);\n        this.applyModelTransform();\n\n        this._lastMouseX = e.clientX;\n        this._lastMouseY = e.clientY;\n      } else if (this._isScalingModel) {\n        const deltaY = e.clientY - this._lastMouseY;\n        const scaleFactor = 1 + (deltaY * 0.01);\n\n        this._modelScale *= scaleFactor;\n        this._modelScale = Math.max(0.1, Math.min(5.0, this._modelScale)); // 限制缩放范围\n\n        console.log('Scaling model:', scaleFactor, 'New scale:', this._modelScale);\n        this.applyModelTransform();\n\n        this._lastMouseY = e.clientY;\n      }\n    });\n\n    canvas.addEventListener('mouseup', () => {\n      console.log('Mouse up - stopping drag/scale');\n      this._isDraggingModel = false;\n      this._isScalingModel = false;\n    });\n\n    // 触摸事件（移动设备支持）\n    canvas.addEventListener('touchstart', (e) => {\n      if (e.touches.length === 1) {\n        // 单指拖拽\n        this._isDraggingModel = true;\n        this._lastMouseX = e.touches[0].clientX;\n        this._lastMouseY = e.touches[0].clientY;\n      } else if (e.touches.length === 2) {\n        // 双指缩放\n        this._isScalingModel = true;\n        const touch1 = e.touches[0];\n        const touch2 = e.touches[1];\n        const distance = Math.sqrt(\n          Math.pow(touch2.clientX - touch1.clientX, 2) +\n          Math.pow(touch2.clientY - touch1.clientY, 2)\n        );\n        this._lastMouseY = distance;\n      }\n      e.preventDefault();\n    });\n\n    canvas.addEventListener('touchmove', (e) => {\n      if (this._isDraggingModel && e.touches.length === 1) {\n        const deltaX = e.touches[0].clientX - this._lastMouseX;\n        const deltaY = e.touches[0].clientY - this._lastMouseY;\n\n        this._modelOffsetX += deltaX * 0.01;\n        this._modelOffsetY -= deltaY * 0.01;\n\n        this.applyModelTransform();\n\n        this._lastMouseX = e.touches[0].clientX;\n        this._lastMouseY = e.touches[0].clientY;\n      } else if (this._isScalingModel && e.touches.length === 2) {\n        const touch1 = e.touches[0];\n        const touch2 = e.touches[1];\n        const distance = Math.sqrt(\n          Math.pow(touch2.clientX - touch1.clientX, 2) +\n          Math.pow(touch2.clientY - touch1.clientY, 2)\n        );\n\n        const scaleFactor = distance / this._lastMouseY;\n        this._modelScale *= scaleFactor;\n        this._modelScale = Math.max(0.1, Math.min(5.0, this._modelScale));\n\n        this.applyModelTransform();\n\n        this._lastMouseY = distance;\n      }\n      e.preventDefault();\n    });\n\n    canvas.addEventListener('touchend', () => {\n      this._isDraggingModel = false;\n      this._isScalingModel = false;\n    });\n\n    // 鼠标滚轮缩放\n    canvas.addEventListener('wheel', (e) => {\n      if (e.ctrlKey) {\n        e.preventDefault();\n        const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;\n        this._modelScale *= scaleFactor;\n        this._modelScale = Math.max(0.1, Math.min(5.0, this._modelScale));\n        this.applyModelTransform();\n      }\n    });\n\n    // 重置按钮\n    const resetTransformBtn = document.getElementById('resetTransformBtn');\n    if (resetTransformBtn) {\n      resetTransformBtn.addEventListener('click', () => {\n        this.resetModelTransform();\n      });\n    }\n  }\n\n  /**\n   * 应用模型变换\n   */\n  private applyModelTransform(): void {\n    console.log('Applying model transform:', this._modelScale, this._modelOffsetX, this._modelOffsetY);\n\n    // 通过delegate获取subdelegates数组\n    const subdelegates = (this._delegate as any)._subdelegates;\n    console.log('Subdelegates:', subdelegates);\n\n    if (subdelegates && subdelegates.getSize() > 0) {\n      const subdelegate = subdelegates.at(0);\n      console.log('Subdelegate:', subdelegate);\n\n      // 通过subdelegate获取view\n      const view = (subdelegate as any)._view;\n      console.log('View:', view);\n\n      if (view) {\n        // 获取视图矩阵并应用变换\n        const viewMatrix = view._viewMatrix;\n        console.log('ViewMatrix:', viewMatrix);\n\n        if (viewMatrix) {\n          // 重置矩阵\n          viewMatrix.loadIdentity();\n\n          // 应用缩放\n          viewMatrix.scale(this._modelScale, this._modelScale);\n\n          // 应用位移\n          viewMatrix.translateX(this._modelOffsetX);\n          viewMatrix.translateY(this._modelOffsetY);\n\n          console.log('Transform applied successfully');\n          this.updateStatus(`模型变换 - 缩放: ${this._modelScale.toFixed(2)}, 位置: (${this._modelOffsetX.toFixed(2)}, ${this._modelOffsetY.toFixed(2)})`);\n        } else {\n          console.log('ViewMatrix not found');\n        }\n      } else {\n        console.log('View not found');\n      }\n    } else {\n      console.log('Subdelegates not found or empty');\n    }\n  }\n\n  /**\n   * 重置模型变换\n   */\n  private resetModelTransform(): void {\n    this._modelScale = 1.0;\n    this._modelOffsetX = 0;\n    this._modelOffsetY = 0;\n    this.applyModelTransform();\n    this.updateStatus('模型变换已重置');\n  }\n\n  /**\n   * 初始化面板切换功能\n   */\n  private initializePanelToggle(): void {\n    const toggleBtn = document.getElementById('panelToggleBtn');\n    const controlPanel = document.getElementById('controlPanel');\n\n    if (toggleBtn && controlPanel) {\n      toggleBtn.addEventListener('click', () => {\n        const isHidden = controlPanel.classList.contains('hidden');\n\n        if (isHidden) {\n          // 显示面板\n          controlPanel.classList.remove('hidden');\n          this.updateStatus('控制面板已显示');\n        } else {\n          // 隐藏面板\n          controlPanel.classList.add('hidden');\n          this.updateStatus('控制面板已隐藏');\n        }\n      });\n\n      // 键盘快捷键 Tab 切换面板\n      document.addEventListener('keydown', (e) => {\n        if (e.key === 'Tab' && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n          e.preventDefault();\n          toggleBtn.click();\n        }\n      });\n    }\n  }\n\n  /**\n   * 释放资源\n   */\n  public release(): void {\n    LAppDelegate.releaseInstance();\n  }\n}\n\n// 全局应用程序实例\nlet app: ControlPanelApp;\n\n/**\n * 页面加载完成后初始化应用程序\n */\nwindow.addEventListener('DOMContentLoaded', async () => {\n  app = new ControlPanelApp();\n  await app.initialize();\n\n  // 将API暴露到全局对象，方便外部调用\n  (window as any).Live2DControlPanel = {\n    // 模型控制\n    changeModel: (index: number) => app.changeModel(index),\n\n    // 动作控制\n    playMotion: (group: string, index: number = 0) => app.playMotion(group, index),\n\n    // 表情控制\n    playExpression: (index: number) => app.playExpression(index),\n    playRandomExpression: () => app.playRandomExpression(),\n\n    // 气泡控制\n    showBubble: (text: string, autoHide: boolean = true) => app.showBubble(text, autoHide),\n    hideBubble: () => app.hideBubble(),\n\n    // 音频播放\n    playAudio: (audioPath: string) => app.playAudioWithLipSync(audioPath),\n\n    // 模型上传\n    uploadModel: (file: File) => app.uploadModel(file),\n    refreshModelList: () => app.refreshModelList(),\n    deleteModel: (modelName: string) => app.deleteModel(modelName),\n\n    // 信息获取\n    getModelInfo: () => app.getCurrentModelInfo(),\n\n    // 内部实例（高级用户使用）\n    _app: app\n  };\n\n  // 添加消息监听器，处理来自父窗口的API调用\n  window.addEventListener('message', (event) => {\n    // 验证消息来源（可选，根据需要调整）\n    if (event.data && event.data.type === 'live2d_api_call') {\n      const { messageId, method, args } = event.data;\n\n      try {\n        // 获取API方法\n        const api = (window as any).Live2DControlPanel;\n        if (api && typeof api[method] === 'function') {\n          // 调用API方法\n          const result = api[method](...args);\n\n          // 如果返回Promise，等待结果\n          if (result && typeof result.then === 'function') {\n            result.then((res: any) => {\n              // 发送成功响应\n              (event.source as any)?.postMessage({\n                type: 'live2d_api_response',\n                messageId: messageId,\n                success: true,\n                result: res\n              }, '*');\n            }).catch((error: any) => {\n              // 发送错误响应\n              (event.source as any)?.postMessage({\n                type: 'live2d_api_response',\n                messageId: messageId,\n                success: false,\n                error: error.message || '调用失败'\n              }, '*');\n            });\n          } else {\n            // 同步方法，直接发送结果\n            (event.source as any)?.postMessage({\n              type: 'live2d_api_response',\n              messageId: messageId,\n              success: true,\n              result: result\n            }, '*');\n          }\n        } else {\n          // 方法不存在\n          (event.source as any)?.postMessage({\n            type: 'live2d_api_response',\n            messageId: messageId,\n            success: false,\n            error: `方法 ${method} 不存在`\n          }, '*');\n        }\n      } catch (error: any) {\n        // 发送错误响应\n        (event.source as any)?.postMessage({\n          type: 'live2d_api_response',\n          messageId: messageId,\n          success: false,\n          error: error.message || '调用失败'\n        }, '*');\n      }\n    }\n  });\n});\n\n/**\n * 页面卸载时释放资源\n */\nwindow.addEventListener('beforeunload', () => {\n  if (app) {\n    app.release();\n  }\n});\n"], "names": ["ControlPanelApp", "LAppDelegate", "modelSelector", "deleteModelBtn", "availableModels", "model", "index", "option", "event", "target", "selectedIndex", "selected<PERSON><PERSON>l", "modelName", "randomMotionBtn", "LAppDefine.MotionGroupIdle", "stopMotionBtn", "randomExpressionBtn", "resetExpressionBtn", "testLipSyncBtn", "showBubbleBtn", "hideBubbleBtn", "bubbleTextInput", "positionButtons", "button", "position", "btn", "checkModelReady", "modelIndex", "validation", "error", "modelInfo", "motionCount", "expressionCount", "motionGroups", "expressions", "totalMotions", "group", "motionCountElement", "expressionCountElement", "modelInfoElement", "motionButtonsContainer", "motionsData", "groupName", "motion", "i", "expressionButtonsContainer", "expressionName", "motionIndex", "texts", "selectedText", "rect", "canvasContainer", "containerRect", "newX", "newY", "bubbleWidth", "bubbleHeight", "touch", "audioPath", "audio", "wavFileHandler", "LAppDefine.ModelDirSize", "expressionNames", "text", "autoHide", "message", "statusElement", "LAppPal", "fileInput", "selectFileBtn", "uploadBtn", "selected<PERSON><PERSON><PERSON><PERSON>", "file", "_a", "uploadStatus", "uploadStatusText", "progressBar", "formData", "response", "result", "analysisInfo", "errorMessage", "checkCanvas", "canvas", "e", "deltaX", "deltaY", "scaleFactor", "touch1", "touch2", "distance", "resetTransformBtn", "subdelegates", "subdelegate", "view", "viewMatrix", "toggleBtn", "controlPanel", "app", "messageId", "method", "args", "api", "res", "_b", "_c"], "mappings": "oEAgBA,MAAMA,CAAgB,CAAtB,aAAA,CAGE,KAAQ,mBAA6B,EACrC,KAAQ,cAAoC,KAC5C,KAAQ,YAAkC,KAC1C,KAAQ,gBAA0B,SAClC,KAAQ,eAAgC,KACxC,KAAQ,YAAuB,GAC/B,KAAQ,YAAwC,CAAE,EAAG,EAAG,EAAG,CAAE,EAG7D,KAAQ,iBAA4B,GACpC,KAAQ,gBAA2B,GACnC,KAAQ,YAAsB,EAC9B,KAAQ,YAAsB,EAC9B,KAAQ,YAAsB,EAC9B,KAAQ,cAAwB,EAChC,KAAQ,cAAwB,EAChC,KAAQ,kBAA6B,EAAA,CAKrC,MAAa,YAA4B,CAElC,KAAA,UAAYC,EAAa,YAAY,EAC1C,KAAK,UAAU,WAAW,EAGrB,KAAA,eAAiB,KAAK,UAAU,iBAAiB,EAGtD,MAAM,KAAK,aAAa,EAGxB,KAAK,UAAU,IAAI,CAAA,CAMrB,MAAc,cAA8B,CAE1C,MAAM,KAAK,wBAAwB,EAGnC,KAAK,yBAAyB,EAG9B,KAAK,uBAAuB,EAG5B,KAAK,sBAAsB,EAG3B,KAAK,4BAA4B,EAGjC,KAAK,sBAAsB,EAG3B,KAAK,uBAAuB,CAAA,CAM9B,MAAc,yBAAyC,CAC/C,MAAAC,EAAgB,SAAS,eAAe,eAAe,EACvDC,EAAiB,SAAS,eAAe,gBAAgB,EAGzDC,EAAkB,MAAM,KAAK,eAAe,sBAAsB,EAGxEF,EAAc,UAAY,oCAGVE,EAAA,QAAQ,CAACC,EAAOC,IAAU,CAClC,MAAAC,EAAS,SAAS,cAAc,QAAQ,EACvCA,EAAA,MAAQD,EAAM,SAAS,EACvBC,EAAA,YAAc,GAAGF,EAAM,IAAI,IAAIA,EAAM,SAAW,SAAW,IAAM,IAAI,GACrEE,EAAA,aAAa,kBAAmBF,EAAM,IAAI,EACjDE,EAAO,aAAa,uBAAwBF,EAAM,eAAe,UAAU,EAC3EE,EAAO,aAAa,mBAAoBF,EAAM,WAAW,UAAU,EACnEH,EAAc,YAAYK,CAAM,CAAA,CACjC,EAGaL,EAAA,iBAAiB,SAAU,MAAOM,GAAU,CACxD,MAAMC,EAASD,EAAM,OACfE,EAAgB,SAASD,EAAO,KAAK,EAE3C,GAAI,CAAC,MAAMC,CAAa,GAAKN,EAAgBM,CAAa,EAAG,CACrD,MAAAC,EAAgBP,EAAgBM,CAAa,EAC7C,MAAA,KAAK,kBAAkBC,EAAc,IAAI,EAC/CR,EAAe,MAAM,QAAU,QAChBA,EAAA,aAAa,kBAAmBQ,EAAc,IAAI,EAGjE,KAAK,iBAAiBA,CAAa,CAAA,MAEnCR,EAAe,MAAM,QAAU,OAC/B,KAAK,cAAc,CACrB,CACD,EAGcA,EAAA,iBAAiB,QAAS,IAAM,CACvC,MAAAS,EAAYT,EAAe,aAAa,iBAAiB,EAC3DS,GACF,KAAK,YAAYA,CAAS,CAC5B,CACD,EAGGR,EAAgB,OAAS,IAC3BF,EAAc,MAAQ,IACtB,MAAM,KAAK,kBAAkBE,EAAgB,CAAC,EAAE,IAAI,EACpDD,EAAe,MAAM,QAAU,QAC/BA,EAAe,aAAa,kBAAmBC,EAAgB,CAAC,EAAE,IAAI,EAGjE,KAAA,iBAAiBA,EAAgB,CAAC,CAAC,EAC1C,CAMM,0BAAiC,CAEjC,MAAAS,EAAkB,SAAS,eAAe,iBAAiB,EAChDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC1C,KAAA,eAAe,iBAAiBC,CAA0B,EAC/D,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,eAAe,eAAe,EACnC,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAsB,SAAS,eAAe,qBAAqB,EACpDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CACnD,KAAK,eAAe,oBAAoB,EACxC,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAqB,SAAS,eAAe,oBAAoB,EACnDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAE5C,MAAAZ,EAAQ,KAAK,eAAe,gBAAgB,EAC9CA,IACIA,EAAA,uBAAuB,eAAe,EAC5C,KAAK,aAAa,MAAM,EAC1B,GAII,MAAAa,EAAiB,SAAS,eAAe,gBAAgB,EAC/CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC9C,KAAK,YAAY,CAAA,EAClB,CAMK,wBAA+B,CAEhC,KAAA,cAAgB,SAAS,eAAe,cAAc,EACtD,KAAA,YAAc,SAAS,eAAe,YAAY,EAGjD,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,iBAAiB,CAAA,GAIlB,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,iBAAiB,CAAA,GAIlB,MAAAC,EAAkB,SAAS,eAAe,iBAAiB,EAChDA,GAAA,MAAAA,EAAA,iBAAiB,QAAUb,GAAU,CACpD,MAAMC,EAASD,EAAM,OACjB,KAAK,cACF,KAAA,YAAY,YAAcC,EAAO,MACxC,GAII,MAAAa,EAAkB,SAAS,iBAAiB,kBAAkB,EACpEA,EAAgB,QAAkBC,GAAA,CACzBA,EAAA,iBAAiB,QAAUf,GAAU,CAC1C,MAAMC,EAASD,EAAM,OACfgB,EAAWf,EAAO,aAAa,eAAe,EAEhDe,IAEFF,EAAgB,QAAeG,GAAAA,EAAI,UAAU,OAAO,QAAQ,CAAC,EACtDhB,EAAA,UAAU,IAAI,QAAQ,EAG7B,KAAK,kBAAkBe,CAAQ,EACjC,CACD,CAAA,CACF,EAGI,KAAA,kBAAkB,KAAK,eAAe,EAG3C,KAAK,oBAAoB,EAGzB,WAAW,IAAM,CACX,KAAK,cACP,KAAK,YAAY,YAAc,kBAC/B,KAAK,iBAAiB,IAEvB,GAAI,CAAA,CAMD,wBAA+B,CAErC,MAAME,EAAkB,SAAY,CAC9B,KAAK,eAAe,gBACtB,KAAK,gBAAgB,EACrB,MAAM,KAAK,oBAAoB,EAC/B,MAAM,KAAK,wBAAwB,EACnC,KAAK,aAAa,QAAQ,GAE1B,WAAWA,EAAiB,GAAG,CAEnC,EAEgBA,EAAA,CAAA,CAMV,YAAYC,EAA0B,CAC5C,KAAK,mBAAqBA,EACrB,KAAA,eAAe,cAAcA,CAAU,EAC5C,KAAK,aAAa,WAAW,EAG7B,KAAK,aAAa,EAGlB,KAAK,uBAAuB,CAAA,CAM9B,MAAc,kBAAkBf,EAAkC,CAC3D,KAAA,eAAe,gBAAgBA,CAAS,EACxC,KAAA,aAAa,WAAWA,CAAS,KAAK,EAG3C,KAAK,aAAa,EAGlB,KAAK,uBAAuB,EAGxB,GAAA,CACF,MAAMgB,EAAa,MAAM,KAAK,eAAe,cAAchB,CAAS,EAChEgB,GAAc,CAACA,EAAW,SACvB,KAAA,aAAa,MAAMhB,CAAS,UAAUgB,EAAW,SAAS,OAAO,KAAK,IAAI,CAAC,EAAE,QAE7EC,EAAO,CACN,QAAA,KAAK,UAAWA,CAAK,CAAA,CAC/B,CAMM,iBAAiBxB,EAAkB,CACnC,MAAAyB,EAAY,SAAS,eAAe,WAAW,EAC/CC,EAAc,SAAS,eAAe,aAAa,EACnDC,EAAkB,SAAS,eAAe,iBAAiB,EAE7DF,GAAaC,GAAeC,IAC9BF,EAAU,MAAM,QAAU,QAC1BC,EAAY,YAAc1B,EAAM,aAAeA,EAAM,aAAa,OAAO,WAAa,IACtF2B,EAAgB,YAAc3B,EAAM,YAAcA,EAAM,YAAY,OAAO,WAAa,IAC1F,CAMM,eAAsB,CACtB,MAAAyB,EAAY,SAAS,eAAe,WAAW,EACjDA,IACFA,EAAU,MAAM,QAAU,OAC5B,CAMM,qBAAqC,CACrC,MAAA5B,EAAgB,SAAS,eAAe,eAAe,EACzD,OAAAA,GAAiBA,EAAc,cAAgB,EAC1BA,EAAc,QAAQA,EAAc,aAAa,EAClD,aAAa,iBAAiB,EAE/C,IAAA,CAMD,iBAAwB,CACxB,MAAAG,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,GAAS,CAACA,EAAM,kBACnB,OAGI,MAAA4B,EAAe,KAAK,eAAe,gBAAgB,EACnDC,EAAc,KAAK,eAAe,mBAAmB,EAG3D,IAAIC,EAAe,EACnBF,EAAa,QAAiBG,GAAA,CACZD,GAAA,KAAK,eAAe,eAAeC,CAAK,CAAA,CACzD,EAGK,MAAAC,EAAqB,SAAS,eAAe,aAAa,EAC1DC,EAAyB,SAAS,eAAe,iBAAiB,EAClEC,EAAmB,SAAS,eAAe,WAAW,EAExDF,IAAoBA,EAAmB,YAAcF,EAAa,SAAS,GAC3EG,IAAwBA,EAAuB,YAAcJ,EAAY,OAAO,SAAS,GACzFK,IAAmCA,EAAA,MAAM,QAAU,OAAA,CAMzD,MAAc,qBAAqC,CAC3C,MAAAC,EAAyB,SAAS,eAAe,eAAe,EACtE,GAAI,CAACA,EAAwB,OAE7BA,EAAuB,UAAY,uCAE/B,GAAA,CAGF,GADqB,KAAK,eAAe,gBAAgB,EACvC,CAEV,MAAA5B,EAAY,KAAK,oBAAoB,EAC3C,GAAIA,EAAW,CACb,MAAM6B,EAAc,MAAM,KAAK,eAAe,0BAA0B7B,CAAS,EAEjF4B,EAAuB,UAAY,GAEnC,UAAWE,KAAaD,EAClBA,EAAY,eAAeC,CAAS,GAClBD,EAAYC,CAAS,EAC7B,QAAQ,CAACC,EAAQrC,IAAU,CACrC,GAAIqC,EAAO,OAAQ,CACX,MAAApB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,wBACnBA,EAAO,YAAc,GAAGmB,CAAS,IAAIpC,EAAQ,CAAC,GACvCiB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,WAAWmB,EAAWpC,CAAK,EAC/C,KAAK,aAAa,SAASoC,CAAS,IAAIpC,EAAQ,CAAC,EAAE,EAC9C,KAAA,iBAAiBoC,EAAWpC,CAAK,CAAA,CACvC,EACDkC,EAAuB,YAAYjB,CAAM,CAAA,CAC3C,CACD,EAIDiB,EAAuB,SAAS,SAAW,IAC7CA,EAAuB,UAAY,qCAErC,MAAA,CACF,QAEKX,EAAO,CACN,QAAA,KAAK,uBAAwBA,CAAK,CAAA,CAI5CW,EAAuB,UAAY,GACd,KAAK,eAAe,gBAAgB,EAE5C,QAAiBJ,GAAA,CAC5B,MAAML,EAAc,KAAK,eAAe,eAAeK,CAAK,EAE5D,QAASQ,EAAI,EAAGA,EAAIb,EAAaa,IAAK,CAC9B,MAAArB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,wBACnBA,EAAO,YAAc,GAAGa,CAAK,IAAIQ,EAAI,CAAC,GAC/BrB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,WAAWa,EAAOQ,CAAC,EACvC,KAAK,aAAa,SAASR,CAAK,IAAIQ,EAAI,CAAC,EAAE,EACtC,KAAA,iBAAiBR,EAAOQ,CAAC,CAAA,CAC/B,EACDJ,EAAuB,YAAYjB,CAAM,CAAA,CAC3C,CACD,EAEGiB,EAAuB,SAAS,SAAW,IAC7CA,EAAuB,UAAY,oCACrC,CAMF,MAAc,yBAAyC,CAC/C,MAAAK,EAA6B,SAAS,eAAe,mBAAmB,EAC9E,GAAI,CAACA,EAA4B,OAEjCA,EAA2B,UAAY,uCAEnC,GAAA,CAGF,GADqB,KAAK,eAAe,gBAAgB,EACvC,CACV,MAAAjC,EAAY,KAAK,oBAAoB,EAC3C,GAAIA,EAAW,CACb,MAAMsB,EAAc,MAAM,KAAK,eAAe,8BAA8BtB,CAAS,EAErFiC,EAA2B,UAAY,GAEvCX,EAAY,QAA0BY,GAAA,CAC9B,MAAAvB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,4BACnBA,EAAO,YAAcuB,EACdvB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,cAAcuB,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,CAC5C,EACDD,EAA2B,YAAYtB,CAAM,CAAA,CAC9C,EAEGsB,EAA2B,SAAS,SAAW,IACjDA,EAA2B,UAAY,qCAEzC,MAAA,CACF,QAEKhB,EAAO,CACN,QAAA,KAAK,uBAAwBA,CAAK,CAAA,CAI5CgB,EAA2B,UAAY,GACnB,KAAK,eAAe,mBAAmB,EAE/C,QAA0BC,GAAA,CAC9B,MAAAvB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,4BACnBA,EAAO,YAAcuB,EACdvB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,cAAcuB,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,CAC5C,EACDD,EAA2B,YAAYtB,CAAM,CAAA,CAC9C,EAEGsB,EAA2B,SAAS,SAAW,IACjDA,EAA2B,UAAY,oCACzC,CAMM,cAAqB,CACrB,MAAAL,EAAyB,SAAS,eAAe,eAAe,EAChEK,EAA6B,SAAS,eAAe,mBAAmB,EACxEN,EAAmB,SAAS,eAAe,WAAW,EAExDC,IACFA,EAAuB,UAAY,qCAEjCK,IACFA,EAA2B,UAAY,qCAErCN,IACFA,EAAiB,MAAM,QAAU,OACnC,CAMM,kBAAyB,CAC1B,KAAK,gBAGN,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,MAInB,KAAA,cAAc,UAAU,IAAI,MAAM,EACvC,KAAK,aAAa,QAAQ,EAGrB,KAAA,eAAiB,OAAO,WAAW,IAAM,CAC5C,KAAK,iBAAiB,GACrB,GAAI,EAAA,CAMD,kBAAyB,CAC1B,KAAK,gBAGN,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,MAInB,KAAA,cAAc,UAAU,OAAO,MAAM,EAC1C,KAAK,aAAa,QAAQ,EAAA,CAMpB,iBAAiBH,EAAeW,EAA2B,CAC7D,GAAA,CAAC,KAAK,YAAa,OAqBvB,MAAMC,EAlB2C,CAC/C,KAAQ,CACN,WACA,YACA,YACA,UACA,cACF,EACA,QAAW,CACT,YACA,OACA,WACA,SACA,UAAA,CAEJ,EAG0BZ,CAAK,GAAK,CAAC,WAAW,EAG5C,IAAAa,EACAF,EAAcC,EAAM,OACtBC,EAAeD,EAAMD,CAAW,EAEjBE,EAAAD,EAAM,KAAK,MAAM,KAAK,OAAW,EAAAA,EAAM,MAAM,CAAC,EAI/D,KAAK,YAAY,YAAcC,EAGzB,MAAA5B,EAAkB,SAAS,eAAe,iBAAiB,EAC7DA,IACFA,EAAgB,MAAQ4B,GAI1B,KAAK,iBAAiB,CAAA,CAMhB,qBAA4B,CAC7B,KAAK,gBAGV,KAAK,cAAc,iBAAiB,YAAc,GAAkB,CAClE,KAAK,YAAc,GACd,KAAA,cAAe,UAAU,IAAI,UAAU,EAEtC,MAAAC,EAAO,KAAK,cAAe,sBAAsB,EACvD,KAAK,YAAY,EAAI,EAAE,QAAUA,EAAK,KACtC,KAAK,YAAY,EAAI,EAAE,QAAUA,EAAK,IAEtC,EAAE,eAAe,CAAA,CAClB,EAGQ,SAAA,iBAAiB,YAAc,GAAkB,CACxD,GAAI,CAAC,KAAK,aAAe,CAAC,KAAK,cAAe,OAExC,MAAAC,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EAG5D,IAAIE,EAAO,EAAE,QAAUD,EAAc,KAAO,KAAK,YAAY,EACzDE,EAAO,EAAE,QAAUF,EAAc,IAAM,KAAK,YAAY,EAGtD,MAAAG,EAAc,KAAK,cAAc,YACjCC,EAAe,KAAK,cAAc,aAGjCH,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMD,EAAc,MAAQG,EAAc,EAAE,CAAC,EACnED,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMF,EAAc,OAASI,EAAe,EAAE,CAAC,EAG5E,KAAK,cAAc,MAAM,KAAO,GAAGH,CAAI,KACvC,KAAK,cAAc,MAAM,IAAM,GAAGC,CAAI,KACjC,KAAA,cAAc,MAAM,MAAQ,GAC5B,KAAA,cAAc,MAAM,OAAS,GAC7B,KAAA,cAAc,MAAM,UAAY,EAAA,CACtC,EAGQ,SAAA,iBAAiB,UAAW,IAAM,CACrC,KAAK,aAAe,KAAK,gBAC3B,KAAK,YAAc,GACd,KAAA,cAAc,UAAU,OAAO,UAAU,EAChD,CACD,EAGD,KAAK,cAAc,iBAAiB,aAAe,GAAkB,CACnE,KAAK,YAAc,GACd,KAAA,cAAe,UAAU,IAAI,UAAU,EAEtC,MAAAG,EAAQ,EAAE,QAAQ,CAAC,EACnBP,EAAO,KAAK,cAAe,sBAAsB,EACvD,KAAK,YAAY,EAAIO,EAAM,QAAUP,EAAK,KAC1C,KAAK,YAAY,EAAIO,EAAM,QAAUP,EAAK,IAE1C,EAAE,eAAe,CAAA,CAClB,EAEQ,SAAA,iBAAiB,YAAc,GAAkB,CACxD,GAAI,CAAC,KAAK,aAAe,CAAC,KAAK,cAAe,OAExC,MAAAO,EAAQ,EAAE,QAAQ,CAAC,EACnBN,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EAG5D,IAAIE,EAAOI,EAAM,QAAUL,EAAc,KAAO,KAAK,YAAY,EAC7DE,EAAOG,EAAM,QAAUL,EAAc,IAAM,KAAK,YAAY,EAG1D,MAAAG,EAAc,KAAK,cAAc,YACjCC,EAAe,KAAK,cAAc,aAGjCH,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMD,EAAc,MAAQG,EAAc,EAAE,CAAC,EACnED,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMF,EAAc,OAASI,EAAe,EAAE,CAAC,EAG5E,KAAK,cAAc,MAAM,KAAO,GAAGH,CAAI,KACvC,KAAK,cAAc,MAAM,IAAM,GAAGC,CAAI,KACjC,KAAA,cAAc,MAAM,MAAQ,GAC5B,KAAA,cAAc,MAAM,OAAS,GAC7B,KAAA,cAAc,MAAM,UAAY,EAAA,CACtC,EAEQ,SAAA,iBAAiB,WAAY,IAAM,CACtC,KAAK,aAAe,KAAK,gBAC3B,KAAK,YAAc,GACd,KAAA,cAAc,UAAU,OAAO,UAAU,EAChD,CACD,EAAA,CAMK,kBAAkB9B,EAAwB,CAC5C,GAAC,KAAK,cAaV,OAXA,KAAK,gBAAkBA,EAGvB,KAAK,cAAc,UAAU,OAAO,MAAO,SAAU,OAAQ,OAAO,EAC/D,KAAA,cAAc,MAAM,KAAO,GAC3B,KAAA,cAAc,MAAM,IAAM,GAC1B,KAAA,cAAc,MAAM,MAAQ,GAC5B,KAAA,cAAc,MAAM,OAAS,GAC7B,KAAA,cAAc,MAAM,UAAY,GAG7BA,EAAU,CAChB,IAAK,cACE,KAAA,cAAc,MAAM,KAAO,OAC3B,KAAA,cAAc,MAAM,OAAS,OAC7B,KAAA,cAAc,UAAU,IAAI,QAAQ,EACzC,MACF,IAAK,eACE,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,OAAS,OAClC,KAAK,cAAc,UAAU,IAAI,SAAU,OAAO,EAClD,MACF,IAAK,WACE,KAAA,cAAc,MAAM,KAAO,OAC3B,KAAA,cAAc,MAAM,IAAM,OAC1B,KAAA,cAAc,UAAU,IAAI,KAAK,EACtC,MACF,IAAK,YACE,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,IAAM,OAC/B,KAAK,cAAc,UAAU,IAAI,MAAO,OAAO,EAC/C,MACF,IAAK,SACL,QAEO,KAAA,cAAc,MAAM,KAAO,MAC3B,KAAA,cAAc,MAAM,IAAM,MAC1B,KAAA,cAAc,MAAM,UAAY,wBACrC,KAAA,CACJ,CAMM,aAAoB,CACpB,MAAAnB,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAIE,KAAK,cACP,KAAK,YAAY,YAAc,gBAC/B,KAAK,iBAAiB,GAIpB,GAAA,CAEF,MAAMqD,EAAY,yBAGjBrD,EAAc,SAAW,GAGpB,MAAAsD,EAAQ,IAAI,MAAMD,CAAS,EACjCC,EAAM,OAAS,GAGf,MAAMC,EAAkBvD,EAAc,gBAClCuD,GAEF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,MAAMF,CAAS,CAAC,CAAA,CAChD,EAAE,KAAK,IAAM,CACZ,KAAK,aAAa,mBAAmB,EAGrC,WAAW,IAAM,CACX,KAAK,cACP,KAAK,YAAY,YAAc,qBAEhC,GAAI,CAAA,CACR,EAAE,MAAe7B,GAAA,CACR,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,sBAAsB,CAAA,CACzC,EAGK8B,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,UAAU,EACxB,KAAK,cACP,KAAK,YAAY,YAAc,YACjC,CACD,GAED,KAAK,aAAa,WAAW,QAExB9B,EAAO,CACN,QAAA,MAAM,YAAaA,CAAK,EAChC,KAAK,aAAa,kBAAkB,CAAA,CACtC,CASK,YAAYF,EAA0B,CACvCA,GAAc,GAAKA,EAAakC,GAC7B,KAAA,eAAe,cAAclC,CAAU,EAC5C,KAAK,aAAa,SAASA,EAAa,CAAC,EAAE,GAEtC,KAAA,aAAa,YAAYA,CAAU,EAAE,CAC5C,CAQK,WAAWS,EAAe9B,EAAgB,EAAS,CAC1C,KAAK,eAAe,gBAAgB,GAE3C,KAAA,eAAe,WAAW8B,EAAO9B,CAAK,EAC3C,KAAK,aAAa,SAAS8B,CAAK,IAAI9B,EAAQ,CAAC,EAAE,EAG1C,KAAA,iBAAiB8B,EAAO9B,CAAK,GAElC,KAAK,aAAa,UAAU,CAC9B,CAOK,eAAeA,EAAqB,CACnC,MAAAwD,EAAkB,KAAK,eAAe,mBAAmB,EAC/D,GAAIxD,GAAS,GAAKA,EAAQwD,EAAgB,OAAQ,CAC1C,MAAAhB,EAAiBgB,EAAgBxD,CAAK,EACvC,KAAA,eAAe,cAAcwC,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,MAEtC,KAAA,aAAa,YAAYxC,CAAK,EAAE,CACvC,CAMK,sBAA6B,CAClC,KAAK,eAAe,oBAAoB,EACxC,KAAK,aAAa,QAAQ,CAAA,CAQrB,WAAWyD,EAAcC,EAAoB,GAAY,CAC9D,GAAI,KAAK,YAAa,CACpB,KAAK,YAAY,YAAcD,EAGzB,MAAA1C,EAAkB,SAAS,eAAe,iBAAiB,EAC7DA,IACFA,EAAgB,MAAQ0C,EAC1B,CAGF,KAAK,iBAAiB,EAEjBC,GAEC,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,KAE1B,CAMK,YAAmB,CACxB,KAAK,iBAAiB,CAAA,CAOjB,qBAAqBN,EAAyB,CAC7C,MAAArD,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAGE,GAAA,CAEDA,EAAc,SAAW,GAG1B,MAAMsD,EAAQ,IAAI,MAAM,mBAAmBD,CAAS,EAAE,EACtDC,EAAM,OAAS,GAGf,MAAMC,EAAkBvD,EAAc,gBAClCuD,GACF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,MAAM,mBAAmBF,CAAS,EAAE,CAAC,CAAA,CACrE,EAAE,KAAK,IAAM,CACP,KAAA,aAAa,SAASA,CAAS,EAAE,CAAA,CACvC,EAAE,MAAe7B,GAAA,CACR,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,sBAAsB,CAAA,CACzC,EAEK8B,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,QAAQ,CAAA,CAC3B,GAED,KAAK,aAAa,WAAW,QAExB9B,EAAO,CACN,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,gBAAgB,CAAA,CACpC,CAMK,qBAA2B,CAEhC,OADc,KAAK,eAAe,gBAAgB,EAEzC,CACL,UAAW,gBACX,SAAU,GACV,aAAc,KAAK,eAAe,gBAAgB,EAClD,YAAa,KAAK,eAAe,mBAAmB,CACtD,EAEK,CACL,UAAW,WACX,SAAU,GACV,aAAc,CAAC,EACf,YAAa,CAAA,CACf,CAAA,CAQM,aAAaoC,EAAuB,CACpC,MAAAC,EAAgB,SAAS,eAAe,YAAY,EACtDA,IACFA,EAAc,YAAcD,GAItBE,EAAA,aAAa,mBAAmBF,CAAO,EAAE,CAAA,CAM3C,uBAA8B,CAC9B,MAAAG,EAAY,SAAS,eAAe,gBAAgB,EACpDC,EAAgB,SAAS,eAAe,eAAe,EACvDC,EAAY,SAAS,eAAe,WAAW,EAC/CC,EAAmB,SAAS,eAAe,kBAAkB,EAGrDF,EAAA,iBAAiB,QAAS,IAAM,CAC5CD,EAAU,MAAM,CAAA,CACjB,EAGSA,EAAA,iBAAiB,SAAW5D,GAAU,OAC9C,MAAMC,EAASD,EAAM,OACfgE,GAAOC,EAAAhE,EAAO,QAAP,YAAAgE,EAAe,GAExBD,IACEA,EAAK,OAAS,mBAAqBA,EAAK,KAAK,SAAS,MAAM,GAC7CD,EAAA,YAAc,QAAQC,EAAK,IAAI,GAChDD,EAAiB,MAAM,QAAU,QACjCD,EAAU,MAAM,QAAU,QAC1B,KAAK,aAAa,UAAUE,EAAK,IAAI,EAAE,IAEvC,MAAM,aAAa,EACnB/D,EAAO,MAAQ,IAEnB,CACD,EAGS6D,EAAA,iBAAiB,QAAS,IAAM,OAClC,MAAAE,GAAOC,EAAAL,EAAU,QAAV,YAAAK,EAAkB,GAC3BD,GACF,KAAK,YAAYA,CAAI,CACvB,CACD,CAAA,CAMH,MAAa,YAAYA,EAA2B,CAC5C,MAAAE,EAAe,SAAS,eAAe,cAAc,EACrDC,EAAmB,SAAS,eAAe,kBAAkB,EAC7DC,EAAc,SAAS,eAAe,aAAa,EACnDN,EAAY,SAAS,eAAe,WAAW,EAEjD,GAAA,CAEFI,EAAa,MAAM,QAAU,QAC7BA,EAAa,UAAY,gBACzBC,EAAiB,YAAc,UAC/BC,EAAY,MAAM,MAAQ,KAC1BN,EAAU,SAAW,GAGf,MAAAO,EAAW,IAAI,SACZA,EAAA,OAAO,WAAYL,CAAI,EAG1B,MAAAM,EAAW,MAAM,MAAM,yCAA0C,CACrE,OAAQ,OACR,KAAMD,CAAA,CACP,EAED,GAAIC,EAAS,GAAI,CACT,MAAAC,EAAS,MAAMD,EAAS,KAAK,EAQnC,GALAJ,EAAa,UAAY,wBACRC,EAAA,YAAc,YAAYI,EAAO,SAAS,OAC3DH,EAAY,MAAM,MAAQ,OAGtBG,EAAO,SAAU,CACnB,MAAMC,EAAyB,CAAC,EAC5BD,EAAO,SAAS,gBAClBC,EAAa,KAAK,OAAOD,EAAO,SAAS,YAAY,MAAM,GAAG,EAE5DA,EAAO,SAAS,YAClBC,EAAa,KAAK,QAAQD,EAAO,SAAS,aAAa,MAAM,GAAG,EAE9DA,EAAO,SAAS,QAAUA,EAAO,SAAS,OAAO,OAAS,GAC5DC,EAAa,KAAK,OAAOD,EAAO,SAAS,OAAO,MAAM,GAAG,EAGvDC,EAAa,OAAS,IACxBL,EAAiB,aAAe,KAAKK,EAAa,KAAK,IAAI,CAAC,IAC9D,CAIF,MAAM,KAAK,iBAAiB,EAG5B,KAAK,mBAAmB,EAExB,KAAK,aAAa,MAAMD,EAAO,SAAS,OAAO,CAAA,KAC1C,CACC,MAAAlD,EAAQ,MAAMiD,EAAS,KAAK,EAClC,MAAM,IAAI,MAAMjD,EAAM,OAAS,MAAM,CAAA,QAEhCA,EAAO,CAEd,MAAMoD,EAAepD,aAAiB,MAAQA,EAAM,QAAU,OAC9D6C,EAAa,UAAY,sBACRC,EAAA,YAAc,SAASM,CAAY,GAC/C,KAAA,aAAa,SAASA,CAAY,EAAE,CAAA,QACzC,CACAX,EAAU,SAAW,EAAA,CACvB,CAMF,MAAa,kBAAkC,CACzC,GAAA,CAEF,MAAM,KAAK,wBAAwB,EACnC,KAAK,aAAa,SAAS,QACpBzC,EAAO,CACN,QAAA,MAAM,YAAaA,CAAK,EAChC,KAAK,aAAa,UAAU,CAAA,CAC9B,CAMM,oBAA2B,CAC3B,MAAAuC,EAAY,SAAS,eAAe,gBAAgB,EACpDG,EAAmB,SAAS,eAAe,kBAAkB,EAC7DD,EAAY,SAAS,eAAe,WAAW,EAErDF,EAAU,MAAQ,GAClBG,EAAiB,MAAM,QAAU,OACjCD,EAAU,MAAM,QAAU,MAAA,CAM5B,MAAa,YAAY1D,EAAkC,CACzD,GAAK,QAAQ,YAAYA,CAAS,cAAc,EAI5C,GAAA,CACG,KAAA,aAAa,WAAWA,CAAS,KAAK,EAE3C,MAAMkE,EAAW,MAAM,MAAM,oCAAoClE,CAAS,GAAI,CAC5E,OAAQ,QAAA,CACT,EAED,GAAIkE,EAAS,GAAI,CACf,MAAMA,EAAS,KAAK,EACf,KAAA,aAAa,MAAMlE,CAAS,OAAO,EAGxC,MAAM,KAAK,iBAAiB,EAGtB,MAAAT,EAAiB,SAAS,eAAe,gBAAgB,EAC/DA,EAAe,MAAM,QAAU,MAAA,KAE1B,CACC,MAAA0B,EAAQ,MAAMiD,EAAS,KAAK,EAClC,MAAM,IAAI,MAAMjD,EAAM,OAAS,MAAM,CAAA,QAEhCA,EAAO,CACd,MAAMoD,EAAepD,aAAiB,MAAQA,EAAM,QAAU,OACzD,KAAA,aAAa,SAASoD,CAAY,EAAE,EACnC,MAAA,WAAWA,CAAY,EAAE,CAAA,CACjC,CAMM,6BAAoC,CAE1C,KAAK,2BAA2B,CAAA,CAM1B,4BAAmC,CACzC,MAAMC,EAAc,IAAM,CAClB,MAAA/B,EAAkB,SAAS,cAAc,mBAAmB,EAC5DgC,EAAShC,GAAA,YAAAA,EAAiB,cAAc,UAE1CgC,GACF,QAAQ,IAAI,6CAA6C,EACzD,KAAK,uBAAuBA,CAAM,GAGlC,WAAWD,EAAa,GAAG,CAE/B,EAEYA,EAAA,CAAA,CAMN,uBAAuBC,EAAiC,CAE9D,QAAQ,IAAI,gCAAgC,EAGrCA,EAAA,iBAAiB,YAAcC,GAAM,CAC1C,QAAQ,IAAI,oBAAqBA,EAAE,QAASA,EAAE,QAAQ,EAClDA,EAAE,SAEJ,KAAK,gBAAkB,GACvB,KAAK,YAAcA,EAAE,QACrB,QAAQ,IAAI,sBAAsB,GACzBA,EAAE,WAEX,KAAK,iBAAmB,GACxB,KAAK,YAAcA,EAAE,QACrB,KAAK,YAAcA,EAAE,QACrB,QAAQ,IAAI,uBAAuB,GAErCA,EAAE,eAAe,CAAA,CAClB,EAEMD,EAAA,iBAAiB,YAAcC,GAAM,CAC1C,GAAI,KAAK,iBAAkB,CACnB,MAAAC,EAASD,EAAE,QAAU,KAAK,YAC1BE,EAASF,EAAE,QAAU,KAAK,YAEhC,KAAK,eAAiBC,EAAS,IAC/B,KAAK,eAAiBC,EAAS,IAEvB,QAAA,IAAI,kBAAmBD,EAAQC,EAAQ,cAAe,KAAK,cAAe,KAAK,aAAa,EACpG,KAAK,oBAAoB,EAEzB,KAAK,YAAcF,EAAE,QACrB,KAAK,YAAcA,EAAE,OAAA,SACZ,KAAK,gBAAiB,CAEzB,MAAAG,EAAc,GADLH,EAAE,QAAU,KAAK,aACE,IAElC,KAAK,aAAeG,EACf,KAAA,YAAc,KAAK,IAAI,GAAK,KAAK,IAAI,EAAK,KAAK,WAAW,CAAC,EAEhE,QAAQ,IAAI,iBAAkBA,EAAa,aAAc,KAAK,WAAW,EACzE,KAAK,oBAAoB,EAEzB,KAAK,YAAcH,EAAE,OAAA,CACvB,CACD,EAEMD,EAAA,iBAAiB,UAAW,IAAM,CACvC,QAAQ,IAAI,gCAAgC,EAC5C,KAAK,iBAAmB,GACxB,KAAK,gBAAkB,EAAA,CACxB,EAGMA,EAAA,iBAAiB,aAAeC,GAAM,CACvC,GAAAA,EAAE,QAAQ,SAAW,EAEvB,KAAK,iBAAmB,GACxB,KAAK,YAAcA,EAAE,QAAQ,CAAC,EAAE,QAChC,KAAK,YAAcA,EAAE,QAAQ,CAAC,EAAE,gBACvBA,EAAE,QAAQ,SAAW,EAAG,CAEjC,KAAK,gBAAkB,GACjB,MAAAI,EAASJ,EAAE,QAAQ,CAAC,EACpBK,EAASL,EAAE,QAAQ,CAAC,EACpBM,EAAW,KAAK,KACpB,KAAK,IAAID,EAAO,QAAUD,EAAO,QAAS,CAAC,EAC3C,KAAK,IAAIC,EAAO,QAAUD,EAAO,QAAS,CAAC,CAC7C,EACA,KAAK,YAAcE,CAAA,CAErBN,EAAE,eAAe,CAAA,CAClB,EAEMD,EAAA,iBAAiB,YAAcC,GAAM,CAC1C,GAAI,KAAK,kBAAoBA,EAAE,QAAQ,SAAW,EAAG,CACnD,MAAMC,EAASD,EAAE,QAAQ,CAAC,EAAE,QAAU,KAAK,YACrCE,EAASF,EAAE,QAAQ,CAAC,EAAE,QAAU,KAAK,YAE3C,KAAK,eAAiBC,EAAS,IAC/B,KAAK,eAAiBC,EAAS,IAE/B,KAAK,oBAAoB,EAEzB,KAAK,YAAcF,EAAE,QAAQ,CAAC,EAAE,QAChC,KAAK,YAAcA,EAAE,QAAQ,CAAC,EAAE,OAAA,SACvB,KAAK,iBAAmBA,EAAE,QAAQ,SAAW,EAAG,CACnD,MAAAI,EAASJ,EAAE,QAAQ,CAAC,EACpBK,EAASL,EAAE,QAAQ,CAAC,EACpBM,EAAW,KAAK,KACpB,KAAK,IAAID,EAAO,QAAUD,EAAO,QAAS,CAAC,EAC3C,KAAK,IAAIC,EAAO,QAAUD,EAAO,QAAS,CAAC,CAC7C,EAEMD,EAAcG,EAAW,KAAK,YACpC,KAAK,aAAeH,EACf,KAAA,YAAc,KAAK,IAAI,GAAK,KAAK,IAAI,EAAK,KAAK,WAAW,CAAC,EAEhE,KAAK,oBAAoB,EAEzB,KAAK,YAAcG,CAAA,CAErBN,EAAE,eAAe,CAAA,CAClB,EAEMD,EAAA,iBAAiB,WAAY,IAAM,CACxC,KAAK,iBAAmB,GACxB,KAAK,gBAAkB,EAAA,CACxB,EAGMA,EAAA,iBAAiB,QAAUC,GAAM,CACtC,GAAIA,EAAE,QAAS,CACbA,EAAE,eAAe,EACjB,MAAMG,EAAcH,EAAE,OAAS,EAAI,GAAM,IACzC,KAAK,aAAeG,EACf,KAAA,YAAc,KAAK,IAAI,GAAK,KAAK,IAAI,EAAK,KAAK,WAAW,CAAC,EAChE,KAAK,oBAAoB,CAAA,CAC3B,CACD,EAGK,MAAAI,EAAoB,SAAS,eAAe,mBAAmB,EACjEA,GACgBA,EAAA,iBAAiB,QAAS,IAAM,CAChD,KAAK,oBAAoB,CAAA,CAC1B,CACH,CAMM,qBAA4B,CAClC,QAAQ,IAAI,4BAA6B,KAAK,YAAa,KAAK,cAAe,KAAK,aAAa,EAG3F,MAAAC,EAAgB,KAAK,UAAkB,cAG7C,GAFQ,QAAA,IAAI,gBAAiBA,CAAY,EAErCA,GAAgBA,EAAa,QAAQ,EAAI,EAAG,CACxC,MAAAC,EAAcD,EAAa,GAAG,CAAC,EAC7B,QAAA,IAAI,eAAgBC,CAAW,EAGvC,MAAMC,EAAQD,EAAoB,MAGlC,GAFQ,QAAA,IAAI,QAASC,CAAI,EAErBA,EAAM,CAER,MAAMC,EAAaD,EAAK,YAChB,QAAA,IAAI,cAAeC,CAAU,EAEjCA,GAEFA,EAAW,aAAa,EAGxBA,EAAW,MAAM,KAAK,YAAa,KAAK,WAAW,EAGxCA,EAAA,WAAW,KAAK,aAAa,EAC7BA,EAAA,WAAW,KAAK,aAAa,EAExC,QAAQ,IAAI,gCAAgC,EAC5C,KAAK,aAAa,cAAc,KAAK,YAAY,QAAQ,CAAC,CAAC,UAAU,KAAK,cAAc,QAAQ,CAAC,CAAC,KAAK,KAAK,cAAc,QAAQ,CAAC,CAAC,GAAG,GAEvI,QAAQ,IAAI,sBAAsB,CACpC,MAEA,QAAQ,IAAI,gBAAgB,CAC9B,MAEA,QAAQ,IAAI,iCAAiC,CAC/C,CAMM,qBAA4B,CAClC,KAAK,YAAc,EACnB,KAAK,cAAgB,EACrB,KAAK,cAAgB,EACrB,KAAK,oBAAoB,EACzB,KAAK,aAAa,SAAS,CAAA,CAMrB,uBAA8B,CAC9B,MAAAC,EAAY,SAAS,eAAe,gBAAgB,EACpDC,EAAe,SAAS,eAAe,cAAc,EAEvDD,GAAaC,IACLD,EAAA,iBAAiB,QAAS,IAAM,CACvBC,EAAa,UAAU,SAAS,QAAQ,GAI1CA,EAAA,UAAU,OAAO,QAAQ,EACtC,KAAK,aAAa,SAAS,IAGdA,EAAA,UAAU,IAAI,QAAQ,EACnC,KAAK,aAAa,SAAS,EAC7B,CACD,EAGQ,SAAA,iBAAiB,UAAYb,GAAM,CACtCA,EAAE,MAAQ,OAAS,CAACA,EAAE,SAAW,CAACA,EAAE,UAAY,CAACA,EAAE,SACrDA,EAAE,eAAe,EACjBY,EAAU,MAAM,EAClB,CACD,EACH,CAMK,SAAgB,CACrB/F,EAAa,gBAAgB,CAAA,CAEjC,CAGA,IAAIiG,EAKJ,OAAO,iBAAiB,mBAAoB,SAAY,CACtDA,EAAM,IAAIlG,EACV,MAAMkG,EAAI,WAAW,EAGpB,OAAe,mBAAqB,CAEnC,YAAc5F,GAAkB4F,EAAI,YAAY5F,CAAK,EAGrD,WAAY,CAAC8B,EAAe9B,EAAgB,IAAM4F,EAAI,WAAW9D,EAAO9B,CAAK,EAG7E,eAAiBA,GAAkB4F,EAAI,eAAe5F,CAAK,EAC3D,qBAAsB,IAAM4F,EAAI,qBAAqB,EAGrD,WAAY,CAACnC,EAAcC,EAAoB,KAASkC,EAAI,WAAWnC,EAAMC,CAAQ,EACrF,WAAY,IAAMkC,EAAI,WAAW,EAGjC,UAAYxC,GAAsBwC,EAAI,qBAAqBxC,CAAS,EAGpE,YAAcc,GAAe0B,EAAI,YAAY1B,CAAI,EACjD,iBAAkB,IAAM0B,EAAI,iBAAiB,EAC7C,YAActF,GAAsBsF,EAAI,YAAYtF,CAAS,EAG7D,aAAc,IAAMsF,EAAI,oBAAoB,EAG5C,KAAMA,CACR,EAGO,OAAA,iBAAiB,UAAY1F,GAAU,WAE5C,GAAIA,EAAM,MAAQA,EAAM,KAAK,OAAS,kBAAmB,CACvD,KAAM,CAAE,UAAA2F,EAAW,OAAAC,EAAQ,KAAAC,GAAS7F,EAAM,KAEtC,GAAA,CAEF,MAAM8F,EAAO,OAAe,mBAC5B,GAAIA,GAAO,OAAOA,EAAIF,CAAM,GAAM,WAAY,CAE5C,MAAMrB,EAASuB,EAAIF,CAAM,EAAE,GAAGC,CAAI,EAG9BtB,GAAU,OAAOA,EAAO,MAAS,WAC5BA,EAAA,KAAMwB,GAAa,QAEvB9B,EAAAjE,EAAM,SAAN,MAAAiE,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAA0B,EACA,QAAS,GACT,OAAQI,GACP,IAAG,CACP,EAAE,MAAO1E,GAAe,QAEtB4C,EAAAjE,EAAM,SAAN,MAAAiE,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAA0B,EACA,QAAS,GACT,MAAOtE,EAAM,SAAW,QACvB,IAAG,CACP,GAGA4C,EAAAjE,EAAM,SAAN,MAAAiE,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAA0B,EACA,QAAS,GACT,OAAApB,GACC,IACL,MAGCyB,EAAAhG,EAAM,SAAN,MAAAgG,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAL,EACA,QAAS,GACT,MAAO,MAAMC,CAAM,QAClB,WAEEvE,EAAY,EAElB4E,EAAAjG,EAAM,SAAN,MAAAiG,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAN,EACA,QAAS,GACT,MAAOtE,EAAM,SAAW,QACvB,IAAG,CACR,CACF,CACD,CACH,CAAC,EAKD,OAAO,iBAAiB,eAAgB,IAAM,CACxCqE,GACFA,EAAI,QAAQ,CAEhB,CAAC"}