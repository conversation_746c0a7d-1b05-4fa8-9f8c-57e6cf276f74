#!/usr/bin/env node

// 设置模块解析路径
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id) {
  if (id.startsWith('@npmcli/')) {
    const npmPath = '/Users/<USER>/WebstormProjects/AiChat/app/assets/node/node_modules/npm/node_modules';
    const fullPath = require.resolve(id, { paths: [npmPath] });
    return originalRequire.call(this, fullPath);
  }
  return originalRequire.call(this, id);
};

require('/Users/<USER>/WebstormProjects/AiChat/app/assets/node/node_modules/npm/lib/cli.js')(process)
