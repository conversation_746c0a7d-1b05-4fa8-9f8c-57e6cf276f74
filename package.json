{"name": "xinghe-ai-assistant", "version": "1.2.4", "description": "兴河 AI Assistant", "main": "main.js", "scripts": {"start": "electron .", "start:with-live2d": "node start-with-live2d.js", "live2d": "cd app/live2d && npm start", "prebuild": "node install-live2d-deps.js", "build": "electron-builder"}, "author": "", "license": "ISC", "devDependencies": {"electron": "^25.9.8", "electron-builder": "^24.9.1"}, "build": {"productName": "兴河 AI Assistant", "appId": "com.aichat.app", "mac": {"category": "public.app-category.productivity", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "icon": "favicon.png"}, "win": {"target": "nsis", "icon": "favicon.png"}, "linux": {"target": "AppImage", "icon": "favicon.png"}, "protocols": {"name": "aichat-protocol", "schemes": ["aichat"]}, "files": ["**/*", "!app/assets/bin/**/*", "!node_modules/bundled_node/**/*", "!app/live2d/node_modules/**/*", "app/live2d/**/*", "!app/live2d/node_modules/**/*", "!app/live2d/resources/**/*", "!app/live2d/uploads/**/*"], "extraResources": [{"from": "app/assets/bin", "to": "app/assets/bin", "filter": ["**/*"]}, {"from": "node_modules/bundled_node", "to": "bundled_node", "filter": ["**/*"]}, {"from": "node_modules/bundled_node_windows", "to": "bundled_node", "filter": ["**/*"]}, {"from": "node-v23.6.1-windows-x64", "to": "node-v23.6.1-windows-x64", "filter": ["**/*"]}, {"from": "app/live2d", "to": "app/live2d", "filter": ["**/*", "!resources/**/*", "!uploads/**/*"]}], "asarUnpack": ["app/assets/bin/**/*", "node_modules/bundled_node/**/*", "node_modules/bundled_node_windows/**/*", "node-v23.6.1-dar<PERSON>-arm64/**/*", "node-v23.6.1-windows-x64/**/*", "app/live2d/node_modules/**/*"], "directories": {"buildResources": "build", "output": "dist"}}}