{"version": 3, "file": "main-CPivpOcW.js", "sources": ["../../src/main.ts"], "sourcesContent": ["/**\n * Copyright(c) Live2D Inc. All rights reserved.\n *\n * Use of this source code is governed by the Live2D Open Software license\n * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.\n */\n\nimport { LAppDelegate } from './lappdelegate';\nimport * as LAppDefine from './lappdefine';\n\n/**\n * ブラウザロード後の処理\n */\nwindow.addEventListener(\n  'load',\n  (): void => {\n    // Initialize WebGL and create the application instance\n    if (!LAppDelegate.getInstance().initialize()) {\n      return;\n    }\n\n    LAppDelegate.getInstance().run();\n  },\n  { passive: true }\n);\n\n/**\n * 終了時の処理\n */\nwindow.addEventListener(\n  'beforeunload',\n  (): void => LAppDelegate.releaseInstance(),\n  { passive: true }\n);\n"], "names": ["LAppDelegate"], "mappings": "+CAaA,OAAO,iBACL,OACA,IAAY,CAELA,EAAa,YAAY,EAAE,cAInBA,EAAA,cAAc,IAAI,CACjC,EACA,CAAE,QAAS,EAAK,CAClB,EAKA,OAAO,iBACL,eACA,IAAYA,EAAa,gBAAgB,EACzC,CAAE,QAAS,EAAK,CAClB"}