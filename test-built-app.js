#!/usr/bin/env node

/**
 * 测试构建后的应用
 * 验证所有Live2D相关文件是否正确包含在构建中
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 测试构建后的应用...\n');

// 构建后的应用路径
const appPath = path.join(__dirname, 'dist', 'mac-arm64', '兴河 AI Assistant.app');
const contentsPath = path.join(appPath, 'Contents');
const resourcesPath = path.join(contentsPath, 'Resources');

console.log('📍 应用路径:');
console.log('  应用包:', appPath);
console.log('  Resources:', resourcesPath);

// 检查应用包是否存在
if (!fs.existsSync(appPath)) {
  console.error('❌ 应用包不存在:', appPath);
  console.error('💡 请先运行 npm run build');
  process.exit(1);
}

console.log('✅ 应用包存在\n');

// 检查关键文件
console.log('🔎 检查关键文件:');

const criticalFiles = [
  // 内嵌Node.js (根据平台选择)
  ...(process.platform === 'win32' ? [
    'node-v23.6.1-windows-x64/node.exe',
    'node-v23.6.1-windows-x64/npm.cmd',
    'node-v23.6.1-windows-x64/npx.cmd'
  ] : [
    'node-v23.6.1-darwin-arm64/bin/node',
    'node-v23.6.1-darwin-arm64/bin/npm',
    'node-v23.6.1-darwin-arm64/bin/npx'
  ]),

  // Live2D相关
  'app/live2d/start-live2d-server.js',
  'app/live2d/package.json',
  'app/live2d/server/model-manager.js',
  'app/live2d/control-panel.html',
  'app/live2d/index.html',

  // 主应用文件
  'app.asar'
];

let allFilesExist = true;
criticalFiles.forEach(relativePath => {
  const fullPath = path.join(resourcesPath, relativePath);
  const exists = fs.existsSync(fullPath);
  console.log(`  ${exists ? '✅' : '❌'} ${relativePath}`);
  
  if (!exists) {
    allFilesExist = false;
  }
  
  // 检查文件大小
  if (exists) {
    try {
      const stats = fs.statSync(fullPath);
      if (stats.isFile()) {
        const sizeKB = (stats.size / 1024).toFixed(2);
        console.log(`      📏 大小: ${sizeKB} KB`);
        
        // 检查可执行权限
        if (relativePath.includes('/bin/')) {
          const isExecutable = !!(stats.mode & parseInt('111', 8));
          console.log(`      🔐 可执行: ${isExecutable ? '是' : '否'}`);
        }
      }
    } catch (error) {
      console.log(`      ❌ 无法读取文件信息: ${error.message}`);
    }
  }
});

// 检查Live2D依赖
console.log('\n📦 检查Live2D依赖:');
const live2dNodeModulesPath = path.join(resourcesPath, 'app', 'live2d', 'node_modules');

if (fs.existsSync(live2dNodeModulesPath)) {
  console.log('✅ Live2D node_modules存在');
  
  try {
    const deps = fs.readdirSync(live2dNodeModulesPath);
    console.log(`  📋 依赖数量: ${deps.length}`);
    
    const expectedDeps = ['express', 'multer', 'jszip', 'cors'];
    expectedDeps.forEach(dep => {
      const depExists = deps.includes(dep);
      console.log(`    ${depExists ? '✅' : '❌'} ${dep}`);
    });
  } catch (error) {
    console.log(`  ❌ 无法读取依赖列表: ${error.message}`);
  }
} else {
  console.log('❌ Live2D node_modules不存在');
  allFilesExist = false;
}

// 检查app.asar内容
console.log('\n📦 检查app.asar内容:');
const asarPath = path.join(resourcesPath, 'app.asar');

if (fs.existsSync(asarPath)) {
  console.log('✅ app.asar存在');
  
  try {
    const stats = fs.statSync(asarPath);
    const sizeMB = (stats.size / 1024 / 1024).toFixed(2);
    console.log(`  📏 大小: ${sizeMB} MB`);
  } catch (error) {
    console.log(`  ❌ 无法读取app.asar信息: ${error.message}`);
  }
} else {
  console.log('❌ app.asar不存在');
  allFilesExist = false;
}

// 总结
console.log('\n📋 构建验证结果:');
if (allFilesExist) {
  console.log('✅ 所有关键文件都存在于构建中');
  console.log('\n🎉 构建验证通过！');
  console.log('\n📋 下一步:');
  console.log('  1. 安装构建后的应用');
  console.log('  2. 启动应用并检查Live2D服务是否正常启动');
  console.log('  3. 访问 http://localhost:3001 验证Live2D控制面板');
  console.log('  4. 测试Live2D模式切换功能');
  
  // 显示应用信息
  console.log('\n📱 应用信息:');
  console.log(`  应用包: ${appPath}`);
  console.log(`  DMG文件: ${path.join(__dirname, 'dist', '兴河 AI Assistant-1.2.4-arm64.dmg')}`);
  console.log(`  ZIP文件: ${path.join(__dirname, 'dist', '兴河 AI Assistant-1.2.4-arm64-mac.zip')}`);
  
} else {
  console.log('❌ 某些关键文件缺失');
  console.log('\n💡 建议:');
  console.log('  1. 检查构建配置中的extraResources设置');
  console.log('  2. 确保所有源文件都存在');
  console.log('  3. 重新运行构建过程');
  process.exit(1);
}

console.log('\n🔧 调试信息:');
console.log('  如果应用启动后Live2D服务无法启动，请检查:');
console.log('  1. 内嵌Node.js是否有执行权限');
console.log('  2. Live2D依赖是否完整');
console.log('  3. 应用日志中的错误信息');
console.log('  4. 端口3001是否被其他程序占用');
