#!/usr/bin/env node

/**
 * 测试修复后的Live2D服务器启动
 * 验证启动逻辑和输出捕获是否正常工作
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

console.log('🧪 测试修复后的Live2D服务器启动...\n');

// 模拟主应用中的启动逻辑
function startLive2DModelServer() {
  return new Promise((resolve, reject) => {
    try {
      console.log('启动Live2D模型管理服务器...');
      
      // 确定Live2D服务器路径
      const live2dServerPath = path.join(__dirname, 'app', 'live2d', 'start-live2d-server.js');
      const live2dWorkingDir = path.join(__dirname, 'app', 'live2d');
      
      // 使用内嵌Node.js (根据平台选择)
      let nodePath;
      if (process.platform === 'win32') {
        nodePath = path.join(__dirname, 'node-v23.6.1-windows-x64', 'node.exe');
      } else {
        nodePath = path.join(__dirname, 'node-v23.6.1-darwin-arm64', 'bin', 'node');
      }
      
      console.log('Live2D服务器路径:', live2dServerPath);
      console.log('Live2D工作目录:', live2dWorkingDir);
      console.log('使用Node.js:', nodePath);
      
      if (!fs.existsSync(live2dServerPath)) {
        return reject(new Error(`Live2D服务器启动脚本不存在: ${live2dServerPath}`));
      }
      
      if (!fs.existsSync(nodePath)) {
        return reject(new Error(`内嵌Node.js不存在: ${nodePath}`));
      }
      
      // 设置环境变量
      const env = {
        ...process.env,
        NODE_PATH: path.join(live2dWorkingDir, 'node_modules')
      };
      
      console.log('设置NODE_PATH:', env.NODE_PATH);
      
      // 启动Live2D服务器
      console.log('启动Live2D服务器...');
      console.log('  命令:', nodePath);
      console.log('  参数:', [live2dServerPath]);
      console.log('  工作目录:', live2dWorkingDir);
      
      const live2dServerProcess = spawn(nodePath, [live2dServerPath], {
        cwd: live2dWorkingDir,
        stdio: 'pipe',
        env: env,
        detached: false,
        shell: false
      });
      
      // 设置编码
      live2dServerProcess.stdout.setEncoding('utf8');
      live2dServerProcess.stderr.setEncoding('utf8');
      
      // 监听输出
      live2dServerProcess.stdout.on('data', (data) => {
        console.log(`[Live2D Server] ${data.toString().trim()}`);
        
        // 检查启动成功标志
        if (data.includes('服务地址: http://localhost:3001') || 
            data.includes('Live2D服务器启动成功') ||
            data.includes('Server running on port 3001') ||
            data.includes('Live2D模型管理服务器运行在 http://localhost:3001')) {
          console.log('✅ Live2D模型管理服务器启动成功');
          resolve(live2dServerProcess);
        }
      });
      
      live2dServerProcess.stderr.on('data', (data) => {
        const output = data.toString().trim();
        console.error(`[Live2D Server Error] ${output}`);
        
        // 某些启动信息可能在stderr中
        if (output.includes('服务地址: http://localhost:3001') ||
            output.includes('Live2D服务器启动成功') ||
            output.includes('Server running on port 3001') ||
            output.includes('Live2D模型管理服务器运行在 http://localhost:3001')) {
          console.log('✅ Live2D模型管理服务器启动成功');
          resolve(live2dServerProcess);
        }
      });
      
      // 进程退出处理
      live2dServerProcess.on('exit', (code, signal) => {
        console.log(`Live2D服务器进程退出，退出码: ${code}, 信号: ${signal}`);
        if (code !== 0 && code !== null) {
          console.error(`Live2D服务器异常退出: ${code}`);
          reject(new Error(`Live2D服务器异常退出: ${code}`));
        }
      });
      
      // 进程错误处理
      live2dServerProcess.on('error', (err) => {
        console.error('启动Live2D服务器失败:', err);
        reject(new Error(`启动Live2D服务器失败: ${err.message}`));
      });
      
      // 设置超时
      setTimeout(() => {
        if (live2dServerProcess && !live2dServerProcess.killed) {
          console.log('Live2D服务器启动超时，但进程仍在运行，假设启动成功');
          resolve(live2dServerProcess);
        }
      }, 10000); // 10秒超时
      
    } catch (error) {
      console.error('启动Live2D服务器异常:', error);
      reject(new Error(`启动Live2D服务器异常: ${error.message}`));
    }
  });
}

// 测试HTTP连接
function testConnection() {
  return new Promise((resolve, reject) => {
    console.log('\n🔗 测试HTTP连接...');
    
    const req = http.get('http://localhost:3001', (res) => {
      console.log(`✅ HTTP连接成功，状态码: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200 || data.length > 0) {
          console.log('✅ 服务器响应正常');
          resolve(true);
        } else {
          console.log('⚠️  服务器响应异常');
          resolve(false);
        }
      });
    });
    
    req.on('error', (err) => {
      console.error('❌ HTTP连接失败:', err.message);
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      console.error('❌ HTTP连接超时');
      req.destroy();
      reject(new Error('HTTP连接超时'));
    });
  });
}

// 主测试流程
async function runTest() {
  let serverProcess = null;
  
  try {
    // 启动Live2D服务器
    serverProcess = await startLive2DModelServer();
    
    // 等待一会让服务器完全启动
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 测试HTTP连接
    await testConnection();
    
    console.log('\n🎉 测试成功！Live2D服务器启动和连接都正常');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    process.exit(1);
  } finally {
    // 清理
    if (serverProcess && !serverProcess.killed) {
      console.log('\n🛑 停止Live2D服务器...');
      serverProcess.kill('SIGTERM');
      
      setTimeout(() => {
        if (!serverProcess.killed) {
          console.log('🔥 强制终止Live2D服务器...');
          serverProcess.kill('SIGKILL');
        }
        process.exit(0);
      }, 2000);
    } else {
      process.exit(0);
    }
  }
}

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 测试被中断');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 测试被终止');
  process.exit(1);
});

// 运行测试
runTest();
