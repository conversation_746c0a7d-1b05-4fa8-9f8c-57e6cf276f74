# Node.js

Node.js is an open-source, cross-platform JavaScript runtime environment.

For information on using Node.js, see the [Node.js website][].

The Node.js project uses an [open governance model](./GOVERNANCE.md). The
[OpenJS Foundation][] provides support for the project.

Contributors are expected to act in a collaborative manner to move
the project forward. We encourage the constructive exchange of contrary
opinions and compromise. The [TSC](./GOVERNANCE.md#technical-steering-committee)
reserves the right to limit or block contributors who repeatedly act in ways
that discourage, exhaust, or otherwise negatively affect other participants.

**This project has a [Code of Conduct][].**

## Table of contents

* [Support](#support)
* [Release types](#release-types)
  * [Download](#download)
    * [Current and LTS releases](#current-and-lts-releases)
    * [Nightly releases](#nightly-releases)
    * [API documentation](#api-documentation)
  * [Verifying binaries](#verifying-binaries)
* [Building Node.js](#building-nodejs)
* [Security](#security)
* [Contributing to Node.js](#contributing-to-nodejs)
* [Current project team members](#current-project-team-members)
  * [TSC (Technical Steering Committee)](#tsc-technical-steering-committee)
  * [Collaborators](#collaborators)
  * [Triagers](#triagers)
  * [Release keys](#release-keys)
* [License](#license)

## Support

Looking for help? Check out the
[instructions for getting support](.github/SUPPORT.md).

## Release types

* **Current**: Under active development. Code for the Current release is in the
  branch for its major version number (for example,
  [v22.x](https://github.com/nodejs/node/tree/v22.x)). Node.js releases a new
  major version every 6 months, allowing for breaking changes. This happens in
  April and October every year. Releases appearing each October have a support
  life of 8 months. Releases appearing each April convert to LTS (see below)
  each October.
* **LTS**: Releases that receive Long Term Support, with a focus on stability
  and security. Every even-numbered major version will become an LTS release.
  LTS releases receive 12 months of _Active LTS_ support and a further 18 months
  of _Maintenance_. LTS release lines have alphabetically-ordered code names,
  beginning with v4 Argon. There are no breaking changes or feature additions,
  except in some special circumstances.
* **Nightly**: Code from the Current branch built every 24-hours when there are
  changes. Use with caution.

Current and LTS releases follow [semantic versioning](https://semver.org). A
member of the Release Team [signs](#release-keys) each Current and LTS release.
For more information, see the
[Release README](https://github.com/nodejs/Release#readme).

### Download

Binaries, installers, and source tarballs are available at
<https://nodejs.org/en/download/>.

#### Current and LTS releases

<https://nodejs.org/download/release/>

The [latest](https://nodejs.org/download/release/latest/) directory is an
alias for the latest Current release. The latest-_codename_ directory is an
alias for the latest release from an LTS line. For example, the
[latest-hydrogen](https://nodejs.org/download/release/latest-hydrogen/)
directory contains the latest Hydrogen (Node.js 18) release.

#### Nightly releases

<https://nodejs.org/download/nightly/>

Each directory and filename includes the version (e.g., `v22.0.0`),
followed by the UTC date (e.g., `20240424` for April 24, 2024),
and the short commit SHA of the HEAD of the release (e.g., `ddd0a9e494`).
For instance, a full directory name might look like `v22.0.0-nightly20240424ddd0a9e494`.

#### API documentation

Documentation for the latest Current release is at <https://nodejs.org/api/>.
Version-specific documentation is available in each release directory in the
_docs_ subdirectory. Version-specific documentation is also at
<https://nodejs.org/download/docs/>.

### Verifying binaries

Download directories contain a `SHASUMS256.txt` file with SHA checksums for the
files.

To download `SHASUMS256.txt` using `curl`:

```bash
curl -O https://nodejs.org/dist/vx.y.z/SHASUMS256.txt
```

To check that downloaded files match the checksum, use `sha256sum`:

```bash
sha256sum -c SHASUMS256.txt --ignore-missing
```

For Current and LTS, the GPG detached signature of `SHASUMS256.txt` is in
`SHASUMS256.txt.sig`. You can use it with `gpg` to verify the integrity of
`SHASUMS256.txt`. You will first need to import
[the GPG keys of individuals authorized to create releases](#release-keys).

See [Release keys](#release-keys) for commands to import active release keys.

Next, download the `SHASUMS256.txt.sig` for the release:

```bash
curl -O https://nodejs.org/dist/vx.y.z/SHASUMS256.txt.sig
```

Then use `gpg --verify SHASUMS256.txt.sig SHASUMS256.txt` to verify
the file's signature.

## Building Node.js

See [BUILDING.md](BUILDING.md) for instructions on how to build Node.js from
source and a list of supported platforms.

## Security

For information on reporting security vulnerabilities in Node.js, see
[SECURITY.md](./SECURITY.md).

## Contributing to Node.js

* [Contributing to the project][]
* [Working Groups][]
* [Strategic initiatives][]
* [Technical values and prioritization][]

## Current project team members

For information about the governance of the Node.js project, see
[GOVERNANCE.md](./GOVERNANCE.md).

<!-- node-core-utils and find-inactive-tsc.mjs depend on the format of the TSC
     list. If the format changes, those utilities need to be tested and
     updated. -->

### TSC (Technical Steering Committee)

#### TSC voting members

<!--lint disable prohibited-strings-->

* [aduh95](https://github.com/aduh95) -
  **Antoine du Hamel** <<<EMAIL>>> (he/him)
* [anonrig](https://github.com/anonrig) -
  **Yagiz Nizipli** <<<EMAIL>>> (he/him)
* [benjamingr](https://github.com/benjamingr) -
  **Benjamin Gruenbaum** <<<EMAIL>>>
* [BridgeAR](https://github.com/BridgeAR) -
  **Ruben Bridgewater** <<<EMAIL>>> (he/him)
* [gireeshpunathil](https://github.com/gireeshpunathil) -
  **Gireesh Punathil** <<<EMAIL>>> (he/him)
* [jasnell](https://github.com/jasnell) -
  **James M Snell** <<<EMAIL>>> (he/him)
* [joyeecheung](https://github.com/joyeecheung) -
  **Joyee Cheung** <<<EMAIL>>> (she/her)
* [legendecas](https://github.com/legendecas) -
  **Chengzhong Wu** <<<EMAIL>>> (he/him)
* [marco-ippolito](https://github.com/marco-ippolito) -
  **Marco Ippolito** <<<EMAIL>>> (he/him)
* [mcollina](https://github.com/mcollina) -
  **Matteo Collina** <<<EMAIL>>> (he/him)
* [mhdawson](https://github.com/mhdawson) -
  **Michael Dawson** <<<EMAIL>>> (he/him)
* [RafaelGSS](https://github.com/RafaelGSS) -
  **Rafael Gonzaga** <<<EMAIL>>> (he/him)
* [richardlau](https://github.com/richardlau) -
  **Richard Lau** <<<EMAIL>>>
* [ronag](https://github.com/ronag) -
  **Robert Nagy** <<<EMAIL>>>
* [ruyadorno](https://github.com/ruyadorno) -
  **Ruy Adorno** <<<EMAIL>>> (he/him)
* [ShogunPanda](https://github.com/ShogunPanda) -
  **Paolo Insogna** <<<EMAIL>>> (he/him)
* [targos](https://github.com/targos) -
  **Michaël Zasso** <<<EMAIL>>> (he/him)
* [tniessen](https://github.com/tniessen) -
  **Tobias Nießen** <<<EMAIL>>> (he/him)

#### TSC regular members

* [apapirovski](https://github.com/apapirovski) -
  **Anatoli Papirovski** <<<EMAIL>>> (he/him)
* [BethGriggs](https://github.com/BethGriggs) -
  **Beth Griggs** <<<EMAIL>>> (she/her)
* [bnoordhuis](https://github.com/bnoordhuis) -
  **Ben Noordhuis** <<<EMAIL>>>
* [cjihrig](https://github.com/cjihrig) -
  **Colin Ihrig** <<<EMAIL>>> (he/him)
* [codebytere](https://github.com/codebytere) -
  **Shelley Vohr** <<<EMAIL>>> (she/her)
* [GeoffreyBooth](https://github.com/GeoffreyBooth) -
  **Geoffrey Booth** <<<EMAIL>>> (he/him)
* [MoLow](https://github.com/MoLow) -
  **Moshe Atlow** <<<EMAIL>>> (he/him)
* [Trott](https://github.com/Trott) -
  **Rich Trott** <<<EMAIL>>> (he/him)

<details>

<summary>TSC emeriti members</summary>

#### TSC emeriti members

* [addaleax](https://github.com/addaleax) -
  **Anna Henningsen** <<<EMAIL>>> (she/her)
* [ChALkeR](https://github.com/ChALkeR) -
  **Сковорода Никита Андреевич** <<<EMAIL>>> (he/him)
* [chrisdickinson](https://github.com/chrisdickinson) -
  **Chris Dickinson** <<<EMAIL>>>
* [danbev](https://github.com/danbev) -
  **Daniel Bevenius** <<<EMAIL>>> (he/him)
* [danielleadams](https://github.com/danielleadams) -
  **Danielle Adams** <<<EMAIL>>> (she/her)
* [evanlucas](https://github.com/evanlucas) -
  **Evan Lucas** <<<EMAIL>>> (he/him)
* [fhinkel](https://github.com/fhinkel) -
  **Franziska Hinkelmann** <<<EMAIL>>> (she/her)
* [Fishrock123](https://github.com/Fishrock123) -
  **Jeremiah Senkpiel** <<<EMAIL>>> (he/they)
* [gabrielschulhof](https://github.com/gabrielschulhof) -
  **Gabriel Schulhof** <<<EMAIL>>>
* [gibfahn](https://github.com/gibfahn) -
  **Gibson Fahnestock** <<<EMAIL>>> (he/him)
* [indutny](https://github.com/indutny) -
  **Fedor Indutny** <<<EMAIL>>>
* [isaacs](https://github.com/isaacs) -
  **Isaac Z. Schlueter** <<<EMAIL>>>
* [joshgav](https://github.com/joshgav) -
  **Josh Gavant** <<<EMAIL>>>
* [mmarchini](https://github.com/mmarchini) -
  **Mary Marchini** <<<EMAIL>>> (she/her)
* [mscdex](https://github.com/mscdex) -
  **Brian White** <<<EMAIL>>>
* [MylesBorins](https://github.com/MylesBorins) -
  **Myles Borins** <<<EMAIL>>> (he/him)
* [nebrius](https://github.com/nebrius) -
  **Bryan Hughes** <<<EMAIL>>>
* [ofrobots](https://github.com/ofrobots) -
  **Ali Ijaz Sheikh** <<<EMAIL>>> (he/him)
* [orangemocha](https://github.com/orangemocha) -
  **Alexis Campailla** <<<EMAIL>>>
* [piscisaureus](https://github.com/piscisaureus) -
  **Bert Belder** <<<EMAIL>>>
* [RaisinTen](https://github.com/RaisinTen) -
  **Darshan Sen** <<<EMAIL>>> (he/him)
* [rvagg](https://github.com/rvagg) -
  **Rod Vagg** <<<EMAIL>>>
* [sam-github](https://github.com/sam-github) -
  **Sam Roberts** <<<EMAIL>>>
* [shigeki](https://github.com/shigeki) -
  **Shigeki Ohtsu** <<<EMAIL>>> (he/him)
* [thefourtheye](https://github.com/thefourtheye) -
  **Sakthipriyan Vairamani** <<<EMAIL>>> (he/him)
* [TimothyGu](https://github.com/TimothyGu) -
  **Tiancheng "Timothy" Gu** <<<EMAIL>>> (he/him)
* [trevnorris](https://github.com/trevnorris) -
  **Trevor Norris** <<<EMAIL>>>

</details>

<!-- node-core-utils and find-inactive-collaborators.mjs depend on the format
     of the collaborator list. If the format changes, those utilities need to be
     tested and updated. -->

### Collaborators

* [abmusse](https://github.com/abmusse) -
  **Abdirahim Musse** <<<EMAIL>>>
* [addaleax](https://github.com/addaleax) -
  **Anna Henningsen** <<<EMAIL>>> (she/her)
* [aduh95](https://github.com/aduh95) -
  **Antoine du Hamel** <<<EMAIL>>> (he/him) - [Support me](https://github.com/sponsors/aduh95)
* [anonrig](https://github.com/anonrig) -
  **Yagiz Nizipli** <<<EMAIL>>> (he/him) - [Support me](https://github.com/sponsors/anonrig)
* [apapirovski](https://github.com/apapirovski) -
  **Anatoli Papirovski** <<<EMAIL>>> (he/him)
* [atlowChemi](https://github.com/atlowChemi) -
  **Chemi Atlow** <<<EMAIL>>> (he/him)
* [Ayase-252](https://github.com/Ayase-252) -
  **Qingyu Deng** <<<EMAIL>>>
* [bengl](https://github.com/bengl) -
  **Bryan English** <<<EMAIL>>> (he/him)
* [benjamingr](https://github.com/benjamingr) -
  **Benjamin Gruenbaum** <<<EMAIL>>>
* [BethGriggs](https://github.com/BethGriggs) -
  **Beth Griggs** <<<EMAIL>>> (she/her)
* [bnb](https://github.com/bnb) -
  **Tierney Cyren** <<<EMAIL>>> (they/them)
* [bnoordhuis](https://github.com/bnoordhuis) -
  **Ben Noordhuis** <<<EMAIL>>>
* [BridgeAR](https://github.com/BridgeAR) -
  **Ruben Bridgewater** <<<EMAIL>>> (he/him)
* [cclauss](https://github.com/cclauss) -
  **Christian Clauss** <<<EMAIL>>> (he/him)
* [cjihrig](https://github.com/cjihrig) -
  **Colin Ihrig** <<<EMAIL>>> (he/him)
* [codebytere](https://github.com/codebytere) -
  **Shelley Vohr** <<<EMAIL>>> (she/her)
* [cola119](https://github.com/cola119) -
  **Kohei Ueno** <<<EMAIL>>> (he/him)
* [daeyeon](https://github.com/daeyeon) -
  **Daeyeon Jeong** <<<EMAIL>>> (he/him)
* [debadree25](https://github.com/debadree25) -
  **Debadree Chatterjee** <<<EMAIL>>> (he/him)
* [deokjinkim](https://github.com/deokjinkim) -
  **Deokjin Kim** <<<EMAIL>>> (he/him)
* [edsadr](https://github.com/edsadr) -
  **Adrian Estrada** <<<EMAIL>>> (he/him)
* [ErickWendel](https://github.com/ErickWendel) -
  **Erick Wendel** <<<EMAIL>>> (he/him)
* [Ethan-Arrowood](https://github.com/Ethan-Arrowood) -
  **Ethan Arrowood** <<<EMAIL>>> (he/him)
* [F3n67u](https://github.com/F3n67u) -
  **Feng Yu** <<<EMAIL>>> (he/him)
* [fhinkel](https://github.com/fhinkel) -
  **Franziska Hinkelmann** <<<EMAIL>>> (she/her)
* [Flarna](https://github.com/Flarna) -
  **Gerhard Stöbich** <<<EMAIL>>> (he/they)
* [gabrielschulhof](https://github.com/gabrielschulhof) -
  **Gabriel Schulhof** <<<EMAIL>>>
* [gengjiawen](https://github.com/gengjiawen) -
  **Jiawen Geng** <<<EMAIL>>>
* [GeoffreyBooth](https://github.com/GeoffreyBooth) -
  **Geoffrey Booth** <<<EMAIL>>> (he/him)
* [gireeshpunathil](https://github.com/gireeshpunathil) -
  **Gireesh Punathil** <<<EMAIL>>> (he/him)
* [guybedford](https://github.com/guybedford) -
  **Guy Bedford** <<<EMAIL>>> (he/him)
* [H4ad](https://github.com/H4ad) -
  **Vinícius Lourenço Claro Cardoso** <<<EMAIL>>> (he/him)
* [HarshithaKP](https://github.com/HarshithaKP) -
  **Harshitha K P** <<<EMAIL>>> (she/her)
* [himself65](https://github.com/himself65) -
  **Zeyu "Alex" Yang** <<<EMAIL>>> (he/him)
* [jakecastelli](https://github.com/jakecastelli) -
  **Jake Yuesong Li** <<<EMAIL>>> (he/him)
* [JakobJingleheimer](https://github.com/JakobJingleheimer) -
  **Jacob Smith** <<<EMAIL>>> (he/him)
* [jasnell](https://github.com/jasnell) -
  **James M Snell** <<<EMAIL>>> (he/him)
* [jazelly](https://github.com/jazelly) -
  **Jason Zhang** <<<EMAIL>>> (he/him)
* [jkrems](https://github.com/jkrems) -
  **Jan Krems** <<<EMAIL>>> (he/him)
* [joyeecheung](https://github.com/joyeecheung) -
  **Joyee Cheung** <<<EMAIL>>> (she/her)
* [juanarbol](https://github.com/juanarbol) -
  **Juan José Arboleda** <<<EMAIL>>> (he/him)
* [JungMinu](https://github.com/JungMinu) -
  **Minwoo Jung** <<<EMAIL>>> (he/him)
* [KhafraDev](https://github.com/KhafraDev) -
  **Matthew Aitken** <<<EMAIL>>> (he/him)
* [kvakil](https://github.com/kvakil) -
  **Keyhan Vakil** <<<EMAIL>>>
* [legendecas](https://github.com/legendecas) -
  **Chengzhong Wu** <<<EMAIL>>> (he/him)
* [lemire](https://github.com/lemire) -
  **Daniel Lemire** <<<EMAIL>>>
* [Linkgoron](https://github.com/Linkgoron) -
  **Nitzan Uziely** <<<EMAIL>>>
* [LiviaMedeiros](https://github.com/LiviaMedeiros) -
  **LiviaMedeiros** <<<EMAIL>>>
* [ljharb](https://github.com/ljharb) -
  **Jordan Harband** <<<EMAIL>>>
* [lpinca](https://github.com/lpinca) -
  **Luigi Pinca** <<<EMAIL>>> (he/him)
* [lukekarrys](https://github.com/lukekarrys) -
  **Luke Karrys** <<<EMAIL>>> (he/him)
* [Lxxyx](https://github.com/Lxxyx) -
  **Zijian Liu** <<<EMAIL>>> (he/him)
* [marco-ippolito](https://github.com/marco-ippolito) -
  **Marco Ippolito** <<<EMAIL>>> (he/him) - [Support me](https://github.com/sponsors/marco-ippolito)
* [marsonya](https://github.com/marsonya) -
  **Akhil Marsonya** <<<EMAIL>>> (he/him)
* [MattiasBuelens](https://github.com/MattiasBuelens) -
  **Mattias Buelens** <<<EMAIL>>> (he/him)
* [mcollina](https://github.com/mcollina) -
  **Matteo Collina** <<<EMAIL>>> (he/him) - [Support me](https://github.com/sponsors/mcollina)
* [meixg](https://github.com/meixg) -
  **Xuguang Mei** <<<EMAIL>>> (he/him)
* [mhdawson](https://github.com/mhdawson) -
  **Michael Dawson** <<<EMAIL>>> (he/him)
* [mildsunrise](https://github.com/mildsunrise) -
  **Alba Mendez** <<<EMAIL>>> (she/her)
* [MoLow](https://github.com/MoLow) -
  **Moshe Atlow** <<<EMAIL>>> (he/him)
* [MrJithil](https://github.com/MrJithil) -
  **Jithil P Ponnan** <<<EMAIL>>> (he/him)
* [ovflowd](https://github.com/ovflowd) -
  **Claudio Wunder** <<<EMAIL>>> (he/they)
* [panva](https://github.com/panva) -
  **Filip Skokan** <<<EMAIL>>> (he/him)
* [pimterry](https://github.com/pimterry) -
  **Tim Perry** <<<EMAIL>>> (he/him)
* [pmarchini](https://github.com/pmarchini) -
  **Pietro Marchini** <<<EMAIL>>> (he/him)
* [Qard](https://github.com/Qard) -
  **Stephen Belanger** <<<EMAIL>>> (he/him)
* [RafaelGSS](https://github.com/RafaelGSS) -
  **Rafael Gonzaga** <<<EMAIL>>> (he/him)
* [richardlau](https://github.com/richardlau) -
  **Richard Lau** <<<EMAIL>>>
* [rluvaton](https://github.com/rluvaton) -
  **Raz Luvaton** <<<EMAIL>>> (he/him)
* [ronag](https://github.com/ronag) -
  **Robert Nagy** <<<EMAIL>>>
* [ruyadorno](https://github.com/ruyadorno) -
  **Ruy Adorno** <<<EMAIL>>> (he/him)
* [santigimeno](https://github.com/santigimeno) -
  **Santiago Gimeno** <<<EMAIL>>>
* [ShogunPanda](https://github.com/ShogunPanda) -
  **Paolo Insogna** <<<EMAIL>>> (he/him)
* [srl295](https://github.com/srl295) -
  **Steven R Loomis** <<<EMAIL>>>
* [StefanStojanovic](https://github.com/StefanStojanovic) -
  **Stefan Stojanovic** <<<EMAIL>>> (he/him)
* [sxa](https://github.com/sxa) -
  **Stewart X Addison** <<<EMAIL>>> (he/him)
* [targos](https://github.com/targos) -
  **Michaël Zasso** <<<EMAIL>>> (he/him)
* [theanarkh](https://github.com/theanarkh) -
  **theanarkh** <<<EMAIL>>> (he/him)
* [tniessen](https://github.com/tniessen) -
  **Tobias Nießen** <<<EMAIL>>> (he/him)
* [trivikr](https://github.com/trivikr) -
  **Trivikram Kamat** <<<EMAIL>>>
* [Trott](https://github.com/Trott) -
  **Rich Trott** <<<EMAIL>>> (he/him)
* [UlisesGascon](https://github.com/UlisesGascon) -
  **Ulises Gascón** <<<EMAIL>>> (he/him)
* [vmoroz](https://github.com/vmoroz) -
  **Vladimir Morozov** <<<EMAIL>>> (he/him)
* [VoltrexKeyva](https://github.com/VoltrexKeyva) -
  **Mohammed Keyvanzadeh** <<<EMAIL>>> (he/him)
* [zcbenz](https://github.com/zcbenz) -
  **Cheng Zhao** <<<EMAIL>>> (he/him)
* [ZYSzys](https://github.com/ZYSzys) -
  **Yongsheng Zhang** <<<EMAIL>>> (he/him)

<details>

<summary>Emeriti</summary>

<!-- find-inactive-collaborators.mjs depends on the format of the emeriti list.
     If the format changes, those utilities need to be tested and updated. -->

### Collaborator emeriti

* [ak239](https://github.com/ak239) -
  **Aleksei Koziatinskii** <<<EMAIL>>>
* [andrasq](https://github.com/andrasq) -
  **Andras** <<<EMAIL>>>
* [AndreasMadsen](https://github.com/AndreasMadsen) -
  **Andreas Madsen** <<<EMAIL>>> (he/him)
* [AnnaMag](https://github.com/AnnaMag) -
  **Anna M. Kedzierska** <<<EMAIL>>>
* [antsmartian](https://github.com/antsmartian) -
  **Anto Aravinth** <<<EMAIL>>> (he/him)
* [aqrln](https://github.com/aqrln) -
  **Alexey Orlenko** <<<EMAIL>>> (he/him)
* [AshCripps](https://github.com/AshCripps) -
  **Ash Cripps** <<<EMAIL>>>
* [bcoe](https://github.com/bcoe) -
  **Ben Coe** <<<EMAIL>>> (he/him)
* [bmeck](https://github.com/bmeck) -
  **Bradley Farias** <<<EMAIL>>>
* [bmeurer](https://github.com/bmeurer) -
  **Benedikt Meurer** <<<EMAIL>>>
* [boneskull](https://github.com/boneskull) -
  **Christopher Hiller** <<<EMAIL>>> (he/him)
* [brendanashworth](https://github.com/brendanashworth) -
  **Brendan Ashworth** <<<EMAIL>>>
* [bzoz](https://github.com/bzoz) -
  **Bartosz Sosnowski** <<<EMAIL>>>
* [calvinmetcalf](https://github.com/calvinmetcalf) -
  **Calvin Metcalf** <<<EMAIL>>>
* [ChALkeR](https://github.com/ChALkeR) -
  **Сковорода Никита Андреевич** <<<EMAIL>>> (he/him)
* [chrisdickinson](https://github.com/chrisdickinson) -
  **Chris Dickinson** <<<EMAIL>>>
* [claudiorodriguez](https://github.com/claudiorodriguez) -
  **Claudio Rodriguez** <<<EMAIL>>>
* [danbev](https://github.com/danbev) -
  **Daniel Bevenius** <<<EMAIL>>> (he/him)
* [danielleadams](https://github.com/danielleadams) -
  **Danielle Adams** <<<EMAIL>>> (she/her)
* [DavidCai1993](https://github.com/DavidCai1993) -
  **David Cai** <<<EMAIL>>> (he/him)
* [davisjam](https://github.com/davisjam) -
  **Jamie Davis** <<<EMAIL>>> (he/him)
* [devnexen](https://github.com/devnexen) -
  **David Carlier** <<<EMAIL>>>
* [devsnek](https://github.com/devsnek) -
  **Gus Caplan** <<<EMAIL>>> (they/them)
* [digitalinfinity](https://github.com/digitalinfinity) -
  **Hitesh Kanwathirtha** <<<EMAIL>>> (he/him)
* [dmabupt](https://github.com/dmabupt) -
  **Xu Meng** <<<EMAIL>>> (he/him)
* [dnlup](https://github.com/dnlup) -
  **dnlup** <<<EMAIL>>>
* [eljefedelrodeodeljefe](https://github.com/eljefedelrodeodeljefe) -
  **Robert Jefe Lindstaedt** <<<EMAIL>>>
* [estliberitas](https://github.com/estliberitas) -
  **Alexander Makarenko** <<<EMAIL>>>
* [eugeneo](https://github.com/eugeneo) -
  **Eugene Ostroukhov** <<<EMAIL>>>
* [evanlucas](https://github.com/evanlucas) -
  **Evan Lucas** <<<EMAIL>>> (he/him)
* [firedfox](https://github.com/firedfox) -
  **Daniel Wang** <<<EMAIL>>>
* [Fishrock123](https://github.com/Fishrock123) -
  **Jeremiah Senkpiel** <<<EMAIL>>> (he/they)
* [gdams](https://github.com/gdams) -
  **George Adams** <<<EMAIL>>> (he/him)
* [geek](https://github.com/geek) -
  **Wyatt Preul** <<<EMAIL>>>
* [gibfahn](https://github.com/gibfahn) -
  **Gibson Fahnestock** <<<EMAIL>>> (he/him)
* [glentiki](https://github.com/glentiki) -
  **Glen Keane** <<<EMAIL>>> (he/him)
* [hashseed](https://github.com/hashseed) -
  **Yang Guo** <<<EMAIL>>> (he/him)
* [hiroppy](https://github.com/hiroppy) -
  **Yuta Hiroto** <<<EMAIL>>> (he/him)
* [iansu](https://github.com/iansu) -
  **Ian Sutherland** <<<EMAIL>>>
* [iarna](https://github.com/iarna) -
  **Rebecca Turner** <<<EMAIL>>>
* [imran-iq](https://github.com/imran-iq) -
  **Imran Iqbal** <<<EMAIL>>>
* [imyller](https://github.com/imyller) -
  **Ilkka Myller** <<<EMAIL>>>
* [indutny](https://github.com/indutny) -
  **Fedor Indutny** <<<EMAIL>>>
* [isaacs](https://github.com/isaacs) -
  **Isaac Z. Schlueter** <<<EMAIL>>>
* [italoacasas](https://github.com/italoacasas) -
  **Italo A. Casas** <<<EMAIL>>> (he/him)
* [JacksonTian](https://github.com/JacksonTian) -
  **Jackson Tian** <<<EMAIL>>>
* [jasongin](https://github.com/jasongin) -
  **Jason Ginchereau** <<<EMAIL>>>
* [jbergstroem](https://github.com/jbergstroem) -
  **Johan Bergström** <<<EMAIL>>>
* [jdalton](https://github.com/jdalton) -
  **John-David Dalton** <<<EMAIL>>>
* [jhamhader](https://github.com/jhamhader) -
  **Yuval Brik** <<<EMAIL>>>
* [joaocgreis](https://github.com/joaocgreis) -
  **João Reis** <<<EMAIL>>>
* [joesepi](https://github.com/joesepi) -
  **Joe Sepi** <<<EMAIL>>> (he/him)
* [joshgav](https://github.com/joshgav) -
  **Josh Gavant** <<<EMAIL>>>
* [julianduque](https://github.com/julianduque) -
  **Julian Duque** <<<EMAIL>>> (he/him)
* [kfarnung](https://github.com/kfarnung) -
  **Kyle Farnung** <<<EMAIL>>> (he/him)
* [kunalspathak](https://github.com/kunalspathak) -
  **Kunal Pathak** <<<EMAIL>>>
* [kuriyosh](https://github.com/kuriyosh) -
  **Yoshiki Kurihara** <<<EMAIL>>> (he/him)
* [lance](https://github.com/lance) -
  **Lance Ball** <<<EMAIL>>> (he/him)
* [Leko](https://github.com/Leko) -
  **Shingo Inoue** <<<EMAIL>>> (he/him)
* [lucamaraschi](https://github.com/lucamaraschi) -
  **Luca Maraschi** <<<EMAIL>>> (he/him)
* [lundibundi](https://github.com/lundibundi) -
  **Denys Otrishko** <<<EMAIL>>> (he/him)
* [lxe](https://github.com/lxe) -
  **Aleksey Smolenchuk** <<<EMAIL>>>
* [maclover7](https://github.com/maclover7) -
  **Jon Moss** <<<EMAIL>>> (he/him)
* [mafintosh](https://github.com/mafintosh) -
  **Mathias Buus** <<<EMAIL>>> (he/him)
* [matthewloring](https://github.com/matthewloring) -
  **Matthew Loring** <<<EMAIL>>>
* [Mesteery](https://github.com/Mesteery) -
  **Mestery** <<<EMAIL>>> (he/him)
* [micnic](https://github.com/micnic) -
  **Nicu Micleușanu** <<<EMAIL>>> (he/him)
* [mikeal](https://github.com/mikeal) -
  **Mikeal Rogers** <<<EMAIL>>>
* [miladfarca](https://github.com/miladfarca) -
  **Milad Fa** <<<EMAIL>>> (he/him)
* [misterdjules](https://github.com/misterdjules) -
  **Julien Gilli** <<<EMAIL>>>
* [mmarchini](https://github.com/mmarchini) -
  **Mary Marchini** <<<EMAIL>>> (she/her)
* [monsanto](https://github.com/monsanto) -
  **Christopher Monsanto** <<<EMAIL>>>
* [MoonBall](https://github.com/MoonBall) -
  **Chen Gang** <<<EMAIL>>>
* [mscdex](https://github.com/mscdex) -
  **Brian White** <<<EMAIL>>>
* [MylesBorins](https://github.com/MylesBorins) -
  **Myles Borins** <<<EMAIL>>> (he/him)
* [not-an-aardvark](https://github.com/not-an-aardvark) -
  **Teddy Katz** <<<EMAIL>>> (he/him)
* [ofrobots](https://github.com/ofrobots) -
  **Ali Ijaz Sheikh** <<<EMAIL>>> (he/him)
* [Olegas](https://github.com/Olegas) -
  **Oleg Elifantiev** <<<EMAIL>>>
* [orangemocha](https://github.com/orangemocha) -
  **Alexis Campailla** <<<EMAIL>>>
* [othiym23](https://github.com/othiym23) -
  **Forrest L Norvell** <<<EMAIL>>> (they/them/themself)
* [oyyd](https://github.com/oyyd) -
  **Ouyang Yadong** <<<EMAIL>>> (he/him)
* [petkaantonov](https://github.com/petkaantonov) -
  **Petka Antonov** <<<EMAIL>>>
* [phillipj](https://github.com/phillipj) -
  **Phillip Johnsen** <<<EMAIL>>>
* [piscisaureus](https://github.com/piscisaureus) -
  **Bert Belder** <<<EMAIL>>>
* [pmq20](https://github.com/pmq20) -
  **Minqi Pan** <<<EMAIL>>>
* [PoojaDurgad](https://github.com/PoojaDurgad) -
  **Pooja D P** <<<EMAIL>>> (she/her)
* [princejwesley](https://github.com/princejwesley) -
  **Prince John Wesley** <<<EMAIL>>>
* [psmarshall](https://github.com/psmarshall) -
  **Peter Marshall** <<<EMAIL>>> (he/him)
* [puzpuzpuz](https://github.com/puzpuzpuz) -
  **Andrey Pechkurov** <<<EMAIL>>> (he/him)
* [RaisinTen](https://github.com/RaisinTen) -
  **Darshan Sen** <<<EMAIL>>> (he/him)
* [refack](https://github.com/refack) -
  **Refael Ackermann (רפאל פלחי)** <<<EMAIL>>> (he/him/הוא/אתה)
* [rexagod](https://github.com/rexagod) -
  **Pranshu Srivastava** <<<EMAIL>>> (he/him)
* [rickyes](https://github.com/rickyes) -
  **Ricky Zhou** <<<EMAIL>>> (he/him)
* [rlidwka](https://github.com/rlidwka) -
  **Alex Kocharin** <<<EMAIL>>>
* [rmg](https://github.com/rmg) -
  **Ryan Graham** <<<EMAIL>>>
* [robertkowalski](https://github.com/robertkowalski) -
  **Robert Kowalski** <<<EMAIL>>>
* [romankl](https://github.com/romankl) -
  **Roman Klauke** <<<EMAIL>>>
* [ronkorving](https://github.com/ronkorving) -
  **Ron Korving** <<<EMAIL>>>
* [RReverser](https://github.com/RReverser) -
  **Ingvar Stepanyan** <<<EMAIL>>>
* [rubys](https://github.com/rubys) -
  **Sam Ruby** <<<EMAIL>>>
* [rvagg](https://github.com/rvagg) -
  **Rod Vagg** <<<EMAIL>>>
* [ryzokuken](https://github.com/ryzokuken) -
  **Ujjwal Sharma** <<<EMAIL>>> (he/him)
* [saghul](https://github.com/saghul) -
  **Saúl Ibarra Corretgé** <<<EMAIL>>>
* [sam-github](https://github.com/sam-github) -
  **Sam Roberts** <<<EMAIL>>>
* [sebdeckers](https://github.com/sebdeckers) -
  **Sebastiaan Deckers** <<<EMAIL>>>
* [seishun](https://github.com/seishun) -
  **Nikolai Vavilov** <<<EMAIL>>>
* [shigeki](https://github.com/shigeki) -
  **Shigeki Ohtsu** <<<EMAIL>>> (he/him)
* [shisama](https://github.com/shisama) -
  **Masashi Hirano** <<<EMAIL>>> (he/him)
* [silverwind](https://github.com/silverwind) -
  **Roman Reiss** <<<EMAIL>>>
* [starkwang](https://github.com/starkwang) -
  **Weijia Wang** <<<EMAIL>>>
* [stefanmb](https://github.com/stefanmb) -
  **Stefan Budeanu** <<<EMAIL>>>
* [tellnes](https://github.com/tellnes) -
  **Christian Tellnes** <<<EMAIL>>>
* [thefourtheye](https://github.com/thefourtheye) -
  **Sakthipriyan Vairamani** <<<EMAIL>>> (he/him)
* [thlorenz](https://github.com/thlorenz) -
  **Thorsten Lorenz** <<<EMAIL>>>
* [TimothyGu](https://github.com/TimothyGu) -
  **Tiancheng "Timothy" Gu** <<<EMAIL>>> (he/him)
* [trevnorris](https://github.com/trevnorris) -
  **Trevor Norris** <<<EMAIL>>>
* [tunniclm](https://github.com/tunniclm) -
  **Mike Tunnicliffe** <<<EMAIL>>>
* [vdeturckheim](https://github.com/vdeturckheim) -
  **Vladimir de Turckheim** <<<EMAIL>>> (he/him)
* [vkurchatkin](https://github.com/vkurchatkin) -
  **Vladimir Kurchatkin** <<<EMAIL>>>
* [vsemozhetbyt](https://github.com/vsemozhetbyt) -
  **Vse Mozhet Byt** <<<EMAIL>>> (he/him)
* [watilde](https://github.com/watilde) -
  **Daijiro Wachi** <<<EMAIL>>> (he/him)
* [watson](https://github.com/watson) -
  **Thomas Watson** <<<EMAIL>>>
* [whitlockjc](https://github.com/whitlockjc) -
  **Jeremy Whitlock** <<<EMAIL>>>
* [XadillaX](https://github.com/XadillaX) -
  **Khaidi Chu** <<<EMAIL>>> (he/him)
* [yashLadha](https://github.com/yashLadha) -
  **Yash Ladha** <<<EMAIL>>> (he/him)
* [yhwang](https://github.com/yhwang) -
  **Yihong Wang** <<<EMAIL>>>
* [yorkie](https://github.com/yorkie) -
  **Yorkie Liu** <<<EMAIL>>>
* [yosuke-furukawa](https://github.com/yosuke-furukawa) -
  **Yosuke Furukawa** <<<EMAIL>>>

</details>

<!--lint enable prohibited-strings-->

Collaborators follow the [Collaborator Guide](./doc/contributing/collaborator-guide.md) in
maintaining the Node.js project.

### Triagers

* [atlowChemi](https://github.com/atlowChemi) -
  **Chemi Atlow** <<<EMAIL>>> (he/him)
* [Ayase-252](https://github.com/Ayase-252) -
  **Qingyu Deng** <<<EMAIL>>>
* [bmuenzenmeyer](https://github.com/bmuenzenmeyer) -
  **Brian Muenzenmeyer** <<<EMAIL>>> (he/him)
* [CanadaHonk](https://github.com/CanadaHonk) -
  **Oliver Medhurst** <<<EMAIL>>> (they/them)
* [daeyeon](https://github.com/daeyeon) -
  **Daeyeon Jeong** <<<EMAIL>>> (he/him)
* [F3n67u](https://github.com/F3n67u) -
  **Feng Yu** <<<EMAIL>>> (he/him)
* [gireeshpunathil](https://github.com/gireeshpunathil) -
  **Gireesh Punathil** <<<EMAIL>>> (he/him)
* [iam-frankqiu](https://github.com/iam-frankqiu) -
  **Frank Qiu** <<<EMAIL>>> (he/him)
* [KevinEady](https://github.com/KevinEady) -
  **Kevin Eady** <<<EMAIL>>> (he/him)
* [kvakil](https://github.com/kvakil) -
  **Keyhan Vakil** <<<EMAIL>>>
* [marsonya](https://github.com/marsonya) -
  **Akhil Marsonya** <<<EMAIL>>> (he/him)
* [meixg](https://github.com/meixg) -
  **Xuguang Mei** <<<EMAIL>>> (he/him)
* [mertcanaltin](https://github.com/mertcanaltin) -
  **Mert Can Altin** <<<EMAIL>>>
* [preveen-stack](https://github.com/preveen-stack) -
  **Preveen Padmanabhan** <<<EMAIL>>> (he/him)
* [VoltrexKeyva](https://github.com/VoltrexKeyva) -
  **Mohammed Keyvanzadeh** <<<EMAIL>>> (he/him)

Triagers follow the [Triage Guide](./doc/contributing/issues.md#triaging-a-bug-report) when
responding to new issues.

### Release keys

Primary GPG keys for Node.js Releasers (some Releasers sign with subkeys):

* **Antoine du Hamel** <<<EMAIL>>>
  `C0D6248439F1D5604AAFFB4021D900FFDB233756`
* **Juan José Arboleda** <<<EMAIL>>>
  `DD792F5973C6DE52C432CBDAC77ABFA00DDBF2B7`
* **Marco Ippolito** <<<EMAIL>>>
  `CC68F5A3106FF448322E48ED27F5E38D5B0A215F`
* **Michaël Zasso** <<<EMAIL>>>
  `8FCCA13FEF1D0C2E91008E09770F7A9A5AE15600`
* **Rafael Gonzaga** <<<EMAIL>>>
  `890C08DB8579162FEE0DF9DB8BEAB4DFCF555EF4`
* **Richard Lau** <<<EMAIL>>>
  `C82FA3AE1CBEDC6BE46B9360C43CEC45C17AB93C`
* **Ruy Adorno** <<<EMAIL>>>
  `108F52B48DB57BB0CC439B2997B01419BD92F80A`
* **Ulises Gascón** <<<EMAIL>>>
  `A363A499291CBBC940DD62E41F10027AF002F8B0`

To import the full set of trusted release keys (including subkeys possibly used
to sign releases):

```bash
gpg --keyserver hkps://keys.openpgp.org --recv-keys C0D6248439F1D5604AAFFB4021D900FFDB233756 # Antoine du Hamel
gpg --keyserver hkps://keys.openpgp.org --recv-keys DD792F5973C6DE52C432CBDAC77ABFA00DDBF2B7 # Juan José Arboleda
gpg --keyserver hkps://keys.openpgp.org --recv-keys CC68F5A3106FF448322E48ED27F5E38D5B0A215F # Marco Ippolito
gpg --keyserver hkps://keys.openpgp.org --recv-keys 8FCCA13FEF1D0C2E91008E09770F7A9A5AE15600 # Michaël Zasso
gpg --keyserver hkps://keys.openpgp.org --recv-keys 890C08DB8579162FEE0DF9DB8BEAB4DFCF555EF4 # Rafael Gonzaga
gpg --keyserver hkps://keys.openpgp.org --recv-keys C82FA3AE1CBEDC6BE46B9360C43CEC45C17AB93C # Richard Lau
gpg --keyserver hkps://keys.openpgp.org --recv-keys 108F52B48DB57BB0CC439B2997B01419BD92F80A # Ruy Adorno
gpg --keyserver hkps://keys.openpgp.org --recv-keys A363A499291CBBC940DD62E41F10027AF002F8B0 # Ulises Gascón
```

See [Verifying binaries](#verifying-binaries) for how to use these keys to
verify a downloaded file.

<details>

<summary>Other keys used to sign some previous releases</summary>

* **Beth Griggs** <<<EMAIL>>>
  `4ED778F539E3634C779C87C6D7062848A1AB005C`
* **Bryan English** <<<EMAIL>>>
  `141F07595B7B3FFE74309A937405533BE57C7D57`
* **Chris Dickinson** <<<EMAIL>>>
  `9554F04D7259F04124DE6B476D5A82AC7E37093B`
* **Colin Ihrig** <<<EMAIL>>>
  `94AE36675C464D64BAFA68DD7434390BDBE9B9C5`
* **Danielle Adams** <<<EMAIL>>>
  `1C050899334244A8AF75E53792EF661D867B9DFA`
  `74F12602B6F1C4E913FAA37AD3A89613643B6201`
* **Evan Lucas** <<<EMAIL>>>
  `B9AE9905FFD7803F25714661B63B535A4C206CA9`
* **Gibson Fahnestock** <<<EMAIL>>>
  `77984A986EBC2AA786BC0F66B01FBB92821C587A`
* **Isaac Z. Schlueter** <<<EMAIL>>>
  `93C7E9E91B49E432C2F75674B0A78B0A6C481CF6`
* **Italo A. Casas** <<<EMAIL>>>
  `56730D5401028683275BD23C23EFEFE93C4CFFFE`
* **James M Snell** <<<EMAIL>>>
  `71DCFD284A79C3B38668286BC97EC7A07EDE3FC1`
* **Jeremiah Senkpiel** <<<EMAIL>>>
  `FD3A5288F042B6850C66B31F09FE44734EB7990E`
* **Juan José Arboleda** <<<EMAIL>>>
  `61FC681DFB92A079F1685E77973F295594EC4689`
* **Julien Gilli** <<<EMAIL>>>
  `114F43EE0176B71C7BC219DD50A3051F888C628D`
* **Myles Borins** <<<EMAIL>>>
  `C4F0DFFF4E8C1A8236409D08E73BC641CC11F4C8`
* **Rod Vagg** <<<EMAIL>>>
  `DD8F2338BAE7501E3DD5AC78C273792F7D83545D`
* **Ruben Bridgewater** <<<EMAIL>>>
  `A48C2BEE680E841632CD4E44F07496B3EB3C1762`
* **Shelley Vohr** <<<EMAIL>>>
  `B9E2F5981AA6E0CD28160D9FF13993A75599653C`
* **Timothy J Fontaine** <<<EMAIL>>>
  `7937DFD2AB06298B2293C3187D33FF9D0246406D`

</details>

### Security release stewards

When possible, the commitment to take slots in the
security release steward rotation is made by companies in order
to ensure individuals who act as security stewards have the
support and recognition from their employer to be able to
prioritize security releases. Security release stewards manage security
releases on a rotation basis as outlined in the
[security release process](./doc/contributing/security-release-process.md).

* [Datadog](https://www.datadoghq.com/)
  * [bengl](https://github.com/bengl) -
    **Bryan English** <<<EMAIL>>> (he/him)
* [NodeSource](https://nodesource.com/)
  * [juanarbol](https://github.com/juanarbol) -
    **Juan José Arboleda** <<<EMAIL>>> (he/him)
  * [RafaelGSS](https://github.com/RafaelGSS) -
    **Rafael Gonzaga** <<<EMAIL>>> (he/him)
* [Platformatic](https://platformatic.dev/)
  * [mcollina](https://github.com/mcollina) -
    **Matteo Collina** <<<EMAIL>>> (he/him)
* [Red Hat](https://redhat.com) / [IBM](https://ibm.com)
  * [joesepi](https://github.com/joesepi) -
    **Joe Sepi** <<<EMAIL>>> (he/him)
  * [mhdawson](https://github.com/mhdawson) -
    **Michael Dawson** <<<EMAIL>>> (he/him)

## License

Node.js is available under the
[MIT License](https://opensource.org/licenses/MIT). Node.js also includes
external libraries that are available under a variety of licenses.  See
[LICENSE](https://github.com/nodejs/node/blob/HEAD/LICENSE) for the full
license text.

[Code of Conduct]: https://github.com/nodejs/admin/blob/HEAD/CODE_OF_CONDUCT.md
[Contributing to the project]: CONTRIBUTING.md
[Node.js website]: https://nodejs.org/
[OpenJS Foundation]: https://openjsf.org/
[Strategic initiatives]: doc/contributing/strategic-initiatives.md
[Technical values and prioritization]: doc/contributing/technical-values.md
[Working Groups]: https://github.com/nodejs/TSC/blob/HEAD/WORKING_GROUPS.md
