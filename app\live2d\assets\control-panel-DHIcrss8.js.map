{"version": 3, "file": "control-panel-DHIcrss8.js", "sources": ["../../src/audio-converter.ts", "../../src/control-panel.ts"], "sourcesContent": ["/**\n * 音频格式检测和转换工具类\n * 支持MP3和WAV格式的检测，以及MP3到WAV的转换\n */\nexport class AudioConverter {\n  private static audioContext: AudioContext | null = null;\n\n  /**\n   * 获取或创建AudioContext实例\n   */\n  private static getAudioContext(): AudioContext {\n    if (!this.audioContext) {\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    }\n    return this.audioContext;\n  }\n\n  /**\n   * 检测base64音频数据的格式\n   * @param base64Data base64编码的音频数据\n   * @returns 'mp3' | 'wav' | 'unknown'\n   */\n  static detectAudioFormat(base64Data: string): 'mp3' | 'wav' | 'unknown' {\n    try {\n      // 移除data URL前缀（如果存在）\n      const cleanBase64 = base64Data.replace(/^data:audio\\/[^;]+;base64,/, '');\n      \n      // 解码base64获取前几个字节\n      const binaryString = atob(cleanBase64);\n      const bytes = new Uint8Array(Math.min(12, binaryString.length));\n      for (let i = 0; i < bytes.length; i++) {\n        bytes[i] = binaryString.charCodeAt(i);\n      }\n\n      // 检测WAV格式 (RIFF...WAVE)\n      if (bytes.length >= 12) {\n        const riff = String.fromCharCode(bytes[0], bytes[1], bytes[2], bytes[3]);\n        const wave = String.fromCharCode(bytes[8], bytes[9], bytes[10], bytes[11]);\n        if (riff === 'RIFF' && wave === 'WAVE') {\n          return 'wav';\n        }\n      }\n\n      // 检测MP3格式\n      if (bytes.length >= 3) {\n        // ID3v2标签\n        if (bytes[0] === 0x49 && bytes[1] === 0x44 && bytes[2] === 0x33) {\n          return 'mp3';\n        }\n        \n        // MP3帧头 (11111111 111xxxxx)\n        if (bytes[0] === 0xFF && (bytes[1] & 0xE0) === 0xE0) {\n          return 'mp3';\n        }\n      }\n\n      return 'unknown';\n    } catch (error) {\n      console.error('检测音频格式失败:', error);\n      return 'unknown';\n    }\n  }\n\n  /**\n   * 将base64音频数据转换为ArrayBuffer\n   * @param base64Data base64编码的音频数据\n   * @returns ArrayBuffer\n   */\n  static base64ToArrayBuffer(base64Data: string): ArrayBuffer {\n    // 移除data URL前缀（如果存在）\n    const cleanBase64 = base64Data.replace(/^data:audio\\/[^;]+;base64,/, '');\n    \n    const binaryString = atob(cleanBase64);\n    const bytes = new Uint8Array(binaryString.length);\n    for (let i = 0; i < binaryString.length; i++) {\n      bytes[i] = binaryString.charCodeAt(i);\n    }\n    return bytes.buffer;\n  }\n\n  /**\n   * 使用Web Audio API将MP3转换为WAV格式的ArrayBuffer\n   * @param mp3ArrayBuffer MP3格式的ArrayBuffer\n   * @returns Promise<ArrayBuffer> WAV格式的ArrayBuffer\n   */\n  static async convertMp3ToWav(mp3ArrayBuffer: ArrayBuffer): Promise<ArrayBuffer> {\n    try {\n      const audioContext = this.getAudioContext();\n\n      // 使用Web Audio API解码MP3\n      const audioBuffer = await audioContext.decodeAudioData(mp3ArrayBuffer.slice(0));\n\n      // 将AudioBuffer转换为WAV格式\n      return this.audioBufferToWav(audioBuffer);\n    } catch (error) {\n      console.error('MP3转WAV失败:', error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`MP3转WAV失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 将AudioBuffer转换为WAV格式的ArrayBuffer\n   * @param audioBuffer Web Audio API的AudioBuffer\n   * @returns ArrayBuffer WAV格式的数据\n   */\n  static audioBufferToWav(audioBuffer: AudioBuffer): ArrayBuffer {\n    const numberOfChannels = audioBuffer.numberOfChannels;\n    const sampleRate = audioBuffer.sampleRate;\n    const length = audioBuffer.length;\n    const bitsPerSample = 16;\n    const bytesPerSample = bitsPerSample / 8;\n    \n    // 计算WAV文件大小\n    const dataSize = length * numberOfChannels * bytesPerSample;\n    const fileSize = 44 + dataSize; // WAV头部44字节 + 数据\n    \n    // 创建ArrayBuffer\n    const arrayBuffer = new ArrayBuffer(fileSize);\n    const view = new DataView(arrayBuffer);\n    \n    // 写入WAV头部\n    let offset = 0;\n    \n    // RIFF标识符\n    this.writeString(view, offset, 'RIFF'); offset += 4;\n    // 文件大小-8\n    view.setUint32(offset, fileSize - 8, true); offset += 4;\n    // WAVE标识符\n    this.writeString(view, offset, 'WAVE'); offset += 4;\n    \n    // fmt子块\n    this.writeString(view, offset, 'fmt '); offset += 4;\n    // fmt子块大小\n    view.setUint32(offset, 16, true); offset += 4;\n    // 音频格式 (PCM = 1)\n    view.setUint16(offset, 1, true); offset += 2;\n    // 声道数\n    view.setUint16(offset, numberOfChannels, true); offset += 2;\n    // 采样率\n    view.setUint32(offset, sampleRate, true); offset += 4;\n    // 字节率\n    view.setUint32(offset, sampleRate * numberOfChannels * bytesPerSample, true); offset += 4;\n    // 块对齐\n    view.setUint16(offset, numberOfChannels * bytesPerSample, true); offset += 2;\n    // 位深度\n    view.setUint16(offset, bitsPerSample, true); offset += 2;\n    \n    // data子块\n    this.writeString(view, offset, 'data'); offset += 4;\n    // data子块大小\n    view.setUint32(offset, dataSize, true); offset += 4;\n    \n    // 写入音频数据\n    for (let channel = 0; channel < numberOfChannels; channel++) {\n      const channelData = audioBuffer.getChannelData(channel);\n      let dataOffset = 44 + channel * bytesPerSample;\n      \n      for (let i = 0; i < length; i++) {\n        // 将浮点数转换为16位整数\n        const sample = Math.max(-1, Math.min(1, channelData[i]));\n        const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\n        \n        view.setInt16(dataOffset, intSample, true);\n        dataOffset += numberOfChannels * bytesPerSample;\n      }\n    }\n    \n    return arrayBuffer;\n  }\n\n  /**\n   * 在DataView中写入字符串\n   * @param view DataView实例\n   * @param offset 偏移量\n   * @param string 要写入的字符串\n   */\n  private static writeString(view: DataView, offset: number, string: string): void {\n    for (let i = 0; i < string.length; i++) {\n      view.setUint8(offset + i, string.charCodeAt(i));\n    }\n  }\n\n  /**\n   * 处理base64音频数据，自动检测格式并转换为WAV\n   * @param base64Data base64编码的音频数据\n   * @returns Promise<ArrayBuffer> WAV格式的ArrayBuffer\n   */\n  static async processBase64Audio(base64Data: string): Promise<ArrayBuffer> {\n    const format = this.detectAudioFormat(base64Data);\n    const arrayBuffer = this.base64ToArrayBuffer(base64Data);\n    \n    switch (format) {\n      case 'wav':\n        console.log('检测到WAV格式，直接使用');\n        return arrayBuffer;\n        \n      case 'mp3':\n        console.log('检测到MP3格式，转换为WAV');\n        return await this.convertMp3ToWav(arrayBuffer);\n        \n      default:\n        console.warn('未知音频格式，尝试作为MP3处理');\n        try {\n          return await this.convertMp3ToWav(arrayBuffer);\n        } catch (error) {\n          console.error('作为MP3处理失败，尝试作为WAV处理');\n          return arrayBuffer;\n        }\n    }\n  }\n}\n", "/**\n * Copyright(c) Live2D Inc. All rights reserved.\n *\n * Use of this source code is governed by the Live2D Open Software license\n * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.\n */\n\nimport { LAppDelegate } from './lappdelegate';\nimport { LAppLive2DManager } from './lapplive2dmanager';\nimport { LAppPal } from './lapppal';\nimport * as LAppDefine from './lappdefine';\nimport { AudioConverter } from './audio-converter';\n\n/**\n * 控制面板应用程序类\n * 管理Live2D模型的控制界面\n */\nclass ControlPanelApp {\n  private _delegate: LAppDelegate;\n  private _live2DManager: LAppLive2DManager;\n  private _currentModelIndex: number = 0;\n  private _speechBubble: HTMLElement | null = null;\n  private _bubbleText: HTMLElement | null = null;\n  private _bubblePosition: string = 'center';\n  private _bubbleTimeout: number | null = null;\n  private _isDragging: boolean = false;\n  private _dragOffset: { x: number; y: number } = { x: 0, y: 0 };\n  private _bubbleManuallyPositioned: boolean = false;\n  private _immersiveMode: boolean = false;\n  private _backgroundOpacity: number = 0; // 默认完全显示背景\n  private _currentBackgroundFile: File | null = null;\n  private _currentBackgroundUrl: string | null = null;\n\n  /**\n   * 初始化应用程序\n   */\n  public async initialize(): Promise<void> {\n    // 获取应用程序委托实例\n    this._delegate = LAppDelegate.getInstance();\n    this._delegate.initialize();\n\n    // 获取Live2D管理器\n    this._live2DManager = this._delegate.getLive2DManager();\n\n    // 初始化UI\n    await this.initializeUI();\n\n    // 开始渲染循环\n    this._delegate.run();\n  }\n\n  /**\n   * 初始化用户界面\n   */\n  private async initializeUI(): Promise<void> {\n    // 初始化模型选择器\n    await this.initializeModelSelector();\n\n    // 初始化控制按钮\n    this.initializeControlButtons();\n\n    // 初始化文字气泡\n    this.initializeSpeechBubble();\n\n    // 初始化模型上传功能\n    this.initializeModelUpload();\n\n    // 监听模型加载完成事件\n    this.setupModelLoadListener();\n  }\n\n  /**\n   * 初始化模型选择器\n   */\n  private async initializeModelSelector(): Promise<void> {\n    const modelSelector = document.getElementById('modelSelector') as HTMLSelectElement;\n    const deleteModelBtn = document.getElementById('deleteModelBtn') as HTMLButtonElement;\n\n    // 动态检测可用模型\n    const availableModels = await this._live2DManager.detectAvailableModels();\n\n    // 清空现有选项\n    modelSelector.innerHTML = '<option value=\"\">选择模型...</option>';\n\n    // 添加模型选项\n    availableModels.forEach((model, index) => {\n      const option = document.createElement('option');\n      option.value = index.toString();\n      option.textContent = `${model.name} ${model.status === 'active' ? '✓' : '⚠️'}`;\n      option.setAttribute('data-model-name', model.name);\n      option.setAttribute('data-has-expressions', model.hasExpressions.toString());\n      option.setAttribute('data-has-motions', model.hasMotions.toString());\n      modelSelector.appendChild(option);\n    });\n\n    // 监听选择变化\n    modelSelector.addEventListener('change', async (event) => {\n      const target = event.target as HTMLSelectElement;\n      const selectedIndex = parseInt(target.value);\n\n      if (!isNaN(selectedIndex) && availableModels[selectedIndex]) {\n        const selectedModel = availableModels[selectedIndex];\n        await this.switchModelByName(selectedModel.name);\n        deleteModelBtn.style.display = 'block';\n        deleteModelBtn.setAttribute('data-model-name', selectedModel.name);\n\n        // 显示模型信息\n        this.displayModelInfo(selectedModel);\n      } else {\n        deleteModelBtn.style.display = 'none';\n        this.hideModelInfo();\n      }\n    });\n\n    // 删除模型按钮事件\n    deleteModelBtn.addEventListener('click', () => {\n      const modelName = deleteModelBtn.getAttribute('data-model-name');\n      if (modelName) {\n        this.deleteModel(modelName);\n      }\n    });\n\n    // 默认选择第一个模型\n    if (availableModels.length > 0) {\n      modelSelector.value = '0';\n      await this.switchModelByName(availableModels[0].name);\n      deleteModelBtn.style.display = 'block';\n      deleteModelBtn.setAttribute('data-model-name', availableModels[0].name);\n\n      // 显示模型信息\n      this.displayModelInfo(availableModels[0]);\n    }\n  }\n\n  /**\n   * 初始化控制按钮\n   */\n  private initializeControlButtons(): void {\n    // 面板切换按钮\n    const panelToggleBtn = document.getElementById('panelToggleBtn');\n    panelToggleBtn?.addEventListener('click', () => {\n      this.toggleControlPanel();\n    });\n\n    // 随机动作按钮\n    const randomMotionBtn = document.getElementById('randomMotionBtn');\n    randomMotionBtn?.addEventListener('click', () => {\n      this._live2DManager.playRandomMotion(LAppDefine.MotionGroupIdle);\n      this.updateStatus('播放随机动作');\n    });\n\n    // 停止动作按钮\n    const stopMotionBtn = document.getElementById('stopMotionBtn');\n    stopMotionBtn?.addEventListener('click', () => {\n      this._live2DManager.stopAllMotions();\n      this.updateStatus('停止所有动作');\n    });\n\n    // 随机表情按钮\n    const randomExpressionBtn = document.getElementById('randomExpressionBtn');\n    randomExpressionBtn?.addEventListener('click', () => {\n      this._live2DManager.setRandomExpression();\n      this.updateStatus('设置随机表情');\n    });\n\n    // 重置表情按钮\n    const resetExpressionBtn = document.getElementById('resetExpressionBtn');\n    resetExpressionBtn?.addEventListener('click', () => {\n      // 通过设置空表情来重置\n      const model = this._live2DManager.getCurrentModel();\n      if (model) {\n        model.getExpressionManager().stopAllMotions();\n        this.updateStatus('重置表情');\n      }\n    });\n\n    // 测试口型同步按钮\n    const testLipSyncBtn = document.getElementById('testLipSyncBtn');\n    testLipSyncBtn?.addEventListener('click', () => {\n      this.testLipSync();\n    });\n\n    // 重置位置缩放按钮\n    const resetTransformBtn = document.getElementById('resetTransformBtn');\n    resetTransformBtn?.addEventListener('click', () => {\n      this.resetTransform();\n    });\n\n\n\n    // 背景透明度滑动条\n    const backgroundOpacitySlider = document.getElementById('backgroundOpacitySlider') as HTMLInputElement;\n    const opacityValue = document.getElementById('opacityValue');\n\n    backgroundOpacitySlider?.addEventListener('input', (e) => {\n      const target = e.target as HTMLInputElement;\n      const value = parseInt(target.value);\n      this._backgroundOpacity = value;\n      if (opacityValue) {\n        opacityValue.textContent = `${value}%`;\n      }\n      this.updateBackgroundOpacity();\n    });\n\n    // 背景文件选择按钮\n    const selectBackgroundBtn = document.getElementById('selectBackgroundBtn');\n    const backgroundFileInput = document.getElementById('backgroundFileInput') as HTMLInputElement;\n\n    selectBackgroundBtn?.addEventListener('click', () => {\n      backgroundFileInput?.click();\n    });\n\n    // 背景文件选择事件\n    backgroundFileInput?.addEventListener('change', (e) => {\n      const target = e.target as HTMLInputElement;\n      const file = target.files?.[0];\n      if (file) {\n        this.handleBackgroundFileSelect(file);\n      }\n    });\n\n    // 应用背景按钮\n    const applyBackgroundBtn = document.getElementById('applyBackgroundBtn');\n    applyBackgroundBtn?.addEventListener('click', () => {\n      this.applyBackground();\n    });\n\n    // 清除背景按钮\n    const clearBackgroundBtn = document.getElementById('clearBackgroundBtn');\n    clearBackgroundBtn?.addEventListener('click', () => {\n      this.clearBackground();\n    });\n  }\n\n  /**\n   * 初始化文字气泡\n   */\n  private initializeSpeechBubble(): void {\n    // 获取气泡元素\n    this._speechBubble = document.getElementById('speechBubble');\n    this._bubbleText = document.getElementById('bubbleText');\n\n    // 显示气泡按钮\n    const showBubbleBtn = document.getElementById('showBubbleBtn');\n    showBubbleBtn?.addEventListener('click', () => {\n      this.showSpeechBubble();\n    });\n\n    // 隐藏气泡按钮\n    const hideBubbleBtn = document.getElementById('hideBubbleBtn');\n    hideBubbleBtn?.addEventListener('click', () => {\n      this.hideSpeechBubble();\n    });\n\n    // 文字输入框\n    const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n    bubbleTextInput?.addEventListener('input', (event) => {\n      const target = event.target as HTMLTextAreaElement;\n      if (this._bubbleText) {\n        this._bubbleText.textContent = target.value;\n      }\n    });\n\n    // 位置控制按钮\n    const positionButtons = document.querySelectorAll('.position-button');\n    positionButtons.forEach(button => {\n      button.addEventListener('click', (event) => {\n        const target = event.target as HTMLElement;\n        const position = target.getAttribute('data-position');\n\n        if (position) {\n          // 更新按钮状态\n          positionButtons.forEach(btn => btn.classList.remove('active'));\n          target.classList.add('active');\n\n          // 更新气泡位置\n          this.setBubblePosition(position);\n        }\n      });\n    });\n\n    // 设置初始位置\n    this.setBubblePosition(this._bubblePosition);\n\n    // 添加拖动事件\n    this.setupBubbleDragging();\n\n    // 页面加载后3秒显示欢迎气泡\n    setTimeout(() => {\n      if (this._bubbleText) {\n        this._bubbleText.textContent = '欢迎使用Live2D控制面板！';\n        this.showSpeechBubble();\n      }\n    }, 3000);\n  }\n\n  /**\n   * 设置模型加载监听器\n   */\n  private setupModelLoadListener(): void {\n    // 定期检查模型是否加载完成\n    const checkModelReady = async () => {\n      if (this._live2DManager.isModelReady()) {\n        this.updateModelInfo();\n        await this.updateMotionButtons();\n        await this.updateExpressionButtons();\n        this.updateStatus('模型加载完成');\n      } else {\n        setTimeout(checkModelReady, 100);\n      }\n    };\n\n    checkModelReady();\n  }\n\n  /**\n   * 切换模型\n   */\n  private switchModel(modelIndex: number): void {\n    this._currentModelIndex = modelIndex;\n    this._live2DManager.switchToModel(modelIndex);\n    this.updateStatus('正在加载模型...');\n\n    // 清空按钮\n    this.clearButtons();\n\n    // 等待模型加载完成\n    this.setupModelLoadListener();\n  }\n\n  /**\n   * 根据模型名称切换模型\n   */\n  private async switchModelByName(modelName: string): Promise<void> {\n    this._live2DManager.loadModelByName(modelName);\n    this.updateStatus(`正在加载模型: ${modelName}...`);\n\n    // 清空按钮\n    this.clearButtons();\n\n    // 等待模型加载完成\n    this.setupModelLoadListener();\n\n    // 验证模型\n    try {\n      const validation = await this._live2DManager.validateModel(modelName);\n      if (validation && !validation.isValid) {\n        this.updateStatus(`模型 ${modelName} 存在问题: ${validation.analysis.issues.join(', ')}`);\n      }\n    } catch (error) {\n      console.warn('模型验证失败:', error);\n    }\n  }\n\n  /**\n   * 显示模型信息\n   */\n  private displayModelInfo(model: any): void {\n    const modelInfo = document.getElementById('modelInfo');\n    const motionCount = document.getElementById('motionCount');\n    const expressionCount = document.getElementById('expressionCount');\n\n    if (modelInfo && motionCount && expressionCount) {\n      modelInfo.style.display = 'block';\n      motionCount.textContent = model.motionGroups ? model.motionGroups.length.toString() : '0';\n      expressionCount.textContent = model.expressions ? model.expressions.length.toString() : '0';\n    }\n  }\n\n  /**\n   * 隐藏模型信息\n   */\n  private hideModelInfo(): void {\n    const modelInfo = document.getElementById('modelInfo');\n    if (modelInfo) {\n      modelInfo.style.display = 'none';\n    }\n  }\n\n  /**\n   * 获取当前模型名称\n   */\n  private getCurrentModelName(): string | null {\n    const modelSelector = document.getElementById('modelSelector') as HTMLSelectElement;\n    if (modelSelector && modelSelector.selectedIndex > 0) {\n      const selectedOption = modelSelector.options[modelSelector.selectedIndex];\n      return selectedOption.getAttribute('data-model-name');\n    }\n    return null;\n  }\n\n  /**\n   * 更新模型信息\n   */\n  private updateModelInfo(): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model || !model.getModelSetting()) {\n      return;\n    }\n\n    const motionGroups = this._live2DManager.getMotionGroups();\n    const expressions = this._live2DManager.getExpressionNames();\n\n    // 计算总动作数\n    let totalMotions = 0;\n    motionGroups.forEach(group => {\n      totalMotions += this._live2DManager.getMotionCount(group);\n    });\n\n    // 更新显示\n    const motionCountElement = document.getElementById('motionCount');\n    const expressionCountElement = document.getElementById('expressionCount');\n    const modelInfoElement = document.getElementById('modelInfo');\n\n    if (motionCountElement) motionCountElement.textContent = totalMotions.toString();\n    if (expressionCountElement) expressionCountElement.textContent = expressions.length.toString();\n    if (modelInfoElement) modelInfoElement.style.display = 'grid';\n  }\n\n  /**\n   * 更新动作按钮\n   */\n  private async updateMotionButtons(): Promise<void> {\n    const motionButtonsContainer = document.getElementById('motionButtons');\n    if (!motionButtonsContainer) return;\n\n    motionButtonsContainer.innerHTML = '<div class=\"loading\">正在加载动作...</div>';\n\n    try {\n      // 尝试从服务器获取当前模型的动作信息\n      const currentModel = this._live2DManager.getCurrentModel();\n      if (currentModel) {\n        // 获取模型名称（这里需要一个方法来获取当前模型名称）\n        const modelName = this.getCurrentModelName();\n        if (modelName) {\n          const motionsData = await this._live2DManager.getModelMotionsFromServer(modelName);\n\n          motionButtonsContainer.innerHTML = '';\n\n          for (const groupName in motionsData) {\n            if (motionsData.hasOwnProperty(groupName)) {\n              const motionArray = motionsData[groupName] as any[];\n              motionArray.forEach((motion, index) => {\n                if (motion.exists) {\n                  const button = document.createElement('button');\n                  button.className = 'control-button motion';\n                  button.textContent = `${groupName} ${index + 1}`;\n                  button.addEventListener('click', () => {\n                    this._live2DManager.playMotion(groupName, index);\n                    this.updateStatus(`播放动作: ${groupName} ${index + 1}`);\n                  });\n                  motionButtonsContainer.appendChild(button);\n                }\n              });\n            }\n          }\n\n          if (motionButtonsContainer.children.length === 0) {\n            motionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用动作</div>';\n          }\n          return;\n        }\n      }\n    } catch (error) {\n      console.warn('从服务器获取动作信息失败，使用本地方法:', error);\n    }\n\n    // 回退到本地方法\n    motionButtonsContainer.innerHTML = '';\n    const motionGroups = this._live2DManager.getMotionGroups();\n\n    motionGroups.forEach(group => {\n      const motionCount = this._live2DManager.getMotionCount(group);\n\n      for (let i = 0; i < motionCount; i++) {\n        const button = document.createElement('button');\n        button.className = 'control-button motion';\n        button.textContent = `${group} ${i + 1}`;\n        button.addEventListener('click', () => {\n          this._live2DManager.playMotion(group, i);\n          this.updateStatus(`播放动作: ${group} ${i + 1}`);\n        });\n        motionButtonsContainer.appendChild(button);\n      }\n    });\n\n    if (motionButtonsContainer.children.length === 0) {\n      motionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用动作</div>';\n    }\n  }\n\n  /**\n   * 更新表情按钮\n   */\n  private async updateExpressionButtons(): Promise<void> {\n    const expressionButtonsContainer = document.getElementById('expressionButtons');\n    if (!expressionButtonsContainer) return;\n\n    expressionButtonsContainer.innerHTML = '<div class=\"loading\">正在加载表情...</div>';\n\n    try {\n      // 尝试从服务器获取当前模型的表情信息\n      const currentModel = this._live2DManager.getCurrentModel();\n      if (currentModel) {\n        const modelName = this.getCurrentModelName();\n        if (modelName) {\n          const expressions = await this._live2DManager.getModelExpressionsFromServer(modelName);\n\n          expressionButtonsContainer.innerHTML = '';\n\n          expressions.forEach(expressionName => {\n            const button = document.createElement('button');\n            button.className = 'control-button expression';\n            button.textContent = expressionName;\n            button.addEventListener('click', () => {\n              this._live2DManager.setExpression(expressionName);\n              this.updateStatus(`设置表情: ${expressionName}`);\n            });\n            expressionButtonsContainer.appendChild(button);\n          });\n\n          if (expressionButtonsContainer.children.length === 0) {\n            expressionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用表情</div>';\n          }\n          return;\n        }\n      }\n    } catch (error) {\n      console.warn('从服务器获取表情信息失败，使用本地方法:', error);\n    }\n\n    // 回退到本地方法\n    expressionButtonsContainer.innerHTML = '';\n    const expressions = this._live2DManager.getExpressionNames();\n\n    expressions.forEach(expressionName => {\n      const button = document.createElement('button');\n      button.className = 'control-button expression';\n      button.textContent = expressionName;\n      button.addEventListener('click', () => {\n        this._live2DManager.setExpression(expressionName);\n        this.updateStatus(`设置表情: ${expressionName}`);\n      });\n      expressionButtonsContainer.appendChild(button);\n    });\n\n    if (expressionButtonsContainer.children.length === 0) {\n      expressionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用表情</div>';\n    }\n  }\n\n  /**\n   * 清空按钮\n   */\n  private clearButtons(): void {\n    const motionButtonsContainer = document.getElementById('motionButtons');\n    const expressionButtonsContainer = document.getElementById('expressionButtons');\n    const modelInfoElement = document.getElementById('modelInfo');\n\n    if (motionButtonsContainer) {\n      motionButtonsContainer.innerHTML = '<div class=\"loading\">加载中...</div>';\n    }\n    if (expressionButtonsContainer) {\n      expressionButtonsContainer.innerHTML = '<div class=\"loading\">加载中...</div>';\n    }\n    if (modelInfoElement) {\n      modelInfoElement.style.display = 'none';\n    }\n  }\n\n  /**\n   * 显示文字气泡\n   */\n  private showSpeechBubble(): void {\n    if (!this._speechBubble) return;\n\n    // 清除之前的自动隐藏定时器\n    if (this._bubbleTimeout) {\n      clearTimeout(this._bubbleTimeout);\n      this._bubbleTimeout = null;\n    }\n\n    // 显示气泡\n    this._speechBubble.classList.add('show');\n    this.updateStatus('显示文字气泡');\n\n    // 5秒后自动隐藏\n    this._bubbleTimeout = window.setTimeout(() => {\n      this.hideSpeechBubble();\n    }, 5000);\n  }\n\n  /**\n   * 隐藏文字气泡\n   */\n  private hideSpeechBubble(): void {\n    if (!this._speechBubble) return;\n\n    // 清除自动隐藏定时器\n    if (this._bubbleTimeout) {\n      clearTimeout(this._bubbleTimeout);\n      this._bubbleTimeout = null;\n    }\n\n    // 隐藏气泡\n    this._speechBubble.classList.remove('show');\n    this.updateStatus('隐藏文字气泡');\n  }\n\n\n\n  /**\n   * 设置气泡拖动功能\n   */\n  private setupBubbleDragging(): void {\n    if (!this._speechBubble) return;\n\n    // 鼠标按下事件\n    this._speechBubble.addEventListener('mousedown', (e: MouseEvent) => {\n      this._isDragging = true;\n\n      // 获取气泡的实际位置（相对于canvas容器）\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n      const bubbleRect = this._speechBubble!.getBoundingClientRect();\n\n      // 计算气泡相对于容器的实际位置\n      const actualLeft = bubbleRect.left - containerRect.left;\n      const actualTop = bubbleRect.top - containerRect.top;\n\n      // 计算鼠标相对于气泡左上角的偏移\n      this._dragOffset.x = e.clientX - containerRect.left - actualLeft;\n      this._dragOffset.y = e.clientY - containerRect.top - actualTop;\n\n      e.preventDefault();\n    });\n\n    // 鼠标移动事件\n    document.addEventListener('mousemove', (e: MouseEvent) => {\n      if (!this._isDragging || !this._speechBubble) return;\n\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n\n      // 计算新位置（相对于canvas容器）\n      let newX = e.clientX - containerRect.left - this._dragOffset.x;\n      let newY = e.clientY - containerRect.top - this._dragOffset.y;\n\n      // 获取气泡的尺寸\n      const bubbleWidth = this._speechBubble.offsetWidth;\n      const bubbleHeight = this._speechBubble.offsetHeight;\n\n      // 限制在画布范围内\n      newX = Math.max(10, Math.min(newX, containerRect.width - bubbleWidth - 10));\n      newY = Math.max(10, Math.min(newY, containerRect.height - bubbleHeight - 10));\n\n      // 直接设置位置，清除所有可能影响位置的样式\n      this._speechBubble.style.left = `${newX}px`;\n      this._speechBubble.style.top = `${newY}px`;\n      this._speechBubble.style.right = 'auto';\n      this._speechBubble.style.bottom = 'auto';\n      this._speechBubble.style.transform = 'none';\n\n      // 标记为手动定位，避免默认居中样式的干扰\n      this._speechBubble.classList.add('manually-positioned');\n    });\n\n    // 鼠标释放事件\n    document.addEventListener('mouseup', () => {\n      if (this._isDragging) {\n        this._isDragging = false;\n      }\n    });\n\n    // 触摸事件支持\n    this._speechBubble.addEventListener('touchstart', (e: TouchEvent) => {\n      this._isDragging = true;\n\n      const touch = e.touches[0];\n\n      // 获取气泡的实际位置（相对于canvas容器）\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n      const bubbleRect = this._speechBubble!.getBoundingClientRect();\n\n      // 计算气泡相对于容器的实际位置\n      const actualLeft = bubbleRect.left - containerRect.left;\n      const actualTop = bubbleRect.top - containerRect.top;\n\n      // 计算触摸点相对于气泡左上角的偏移\n      this._dragOffset.x = touch.clientX - containerRect.left - actualLeft;\n      this._dragOffset.y = touch.clientY - containerRect.top - actualTop;\n\n      e.preventDefault();\n    });\n\n    document.addEventListener('touchmove', (e: TouchEvent) => {\n      if (!this._isDragging || !this._speechBubble) return;\n\n      const touch = e.touches[0];\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n\n      // 计算新位置（相对于canvas容器）\n      let newX = touch.clientX - containerRect.left - this._dragOffset.x;\n      let newY = touch.clientY - containerRect.top - this._dragOffset.y;\n\n      // 获取气泡的尺寸\n      const bubbleWidth = this._speechBubble.offsetWidth;\n      const bubbleHeight = this._speechBubble.offsetHeight;\n\n      // 限制在画布范围内\n      newX = Math.max(10, Math.min(newX, containerRect.width - bubbleWidth - 10));\n      newY = Math.max(10, Math.min(newY, containerRect.height - bubbleHeight - 10));\n\n      // 直接设置位置，清除所有可能影响位置的样式\n      this._speechBubble.style.left = `${newX}px`;\n      this._speechBubble.style.top = `${newY}px`;\n      this._speechBubble.style.right = 'auto';\n      this._speechBubble.style.bottom = 'auto';\n      this._speechBubble.style.transform = 'none';\n\n      // 标记为手动定位\n      this._speechBubble.classList.add('manually-positioned');\n\n      e.preventDefault();\n    });\n\n    document.addEventListener('touchend', () => {\n      if (this._isDragging) {\n        this._isDragging = false;\n      }\n    });\n  }\n\n  /**\n   * 设置气泡位置\n   */\n  private setBubblePosition(position: string): void {\n    if (!this._speechBubble) return;\n\n    // 如果气泡已被用户手动拖动，则不重置位置\n    if (this._bubbleManuallyPositioned) {\n      return;\n    }\n\n    this._bubblePosition = position;\n\n    // 移除所有位置类和样式\n    this._speechBubble.classList.remove('top', 'bottom', 'left', 'right');\n    this._speechBubble.style.left = '';\n    this._speechBubble.style.top = '';\n    this._speechBubble.style.right = '';\n    this._speechBubble.style.bottom = '';\n    this._speechBubble.style.transform = '';\n\n    // 根据位置设置样式\n    switch (position) {\n      case 'bottom-left':\n        this._speechBubble.style.left = '50px';\n        this._speechBubble.style.bottom = '50px';\n        this._speechBubble.classList.add('bottom');\n        break;\n      case 'bottom-right':\n        this._speechBubble.style.right = '50px';\n        this._speechBubble.style.bottom = '50px';\n        this._speechBubble.classList.add('bottom', 'right');\n        break;\n      case 'top-left':\n        this._speechBubble.style.left = '50px';\n        this._speechBubble.style.top = '50px';\n        this._speechBubble.classList.add('top');\n        break;\n      case 'top-right':\n        this._speechBubble.style.right = '50px';\n        this._speechBubble.style.top = '50px';\n        this._speechBubble.classList.add('top', 'right');\n        break;\n      case 'center':\n      default:\n        // 默认居中显示\n        this._speechBubble.style.left = '50%';\n        this._speechBubble.style.top = '50%';\n        this._speechBubble.style.transform = 'translate(-50%, -50%)';\n        break;\n    }\n  }\n\n  /**\n   * 重置Live2D模型的位置和缩放\n   */\n  private resetTransform(): void {\n    try {\n      // 通过Live2D管理器重置变换\n      const manager = this._live2DManager;\n      if (manager) {\n        // 获取当前模型\n        const model = manager.getCurrentModel();\n        if (model) {\n          // 发送重置消息给父窗口，让它处理重置逻辑\n          window.parent.postMessage({\n            type: 'live2d_reset_transform'\n          }, '*');\n\n          this.updateStatus('已重置Live2D模型位置和缩放');\n        } else {\n          this.updateStatus('没有找到当前模型');\n        }\n      } else {\n        this.updateStatus('Live2D管理器未初始化');\n      }\n    } catch (error) {\n      console.error('重置变换失败:', error);\n      this.updateStatus('重置变换失败');\n    }\n  }\n\n  /**\n   * 测试口型同步功能\n   */\n  private testLipSync(): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    // 显示测试气泡\n    if (this._bubbleText) {\n      this._bubbleText.textContent = '正在测试口型同步功能...';\n      this.showSpeechBubble();\n    }\n\n    // 使用模型自带的wav文件处理器和同时播放音频\n    try {\n      // 获取测试音频文件路径\n      const audioPath = '../../Resources/测试.wav';\n\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audio = new Audio(audioPath);\n      audio.volume = 0.8; // 设置音量\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        // 同时启动音频播放和口型同步\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.start(audioPath))\n        ]).then(() => {\n          this.updateStatus('开始播放测试音频，观察口型同步效果');\n\n          // 更新气泡文字\n          setTimeout(() => {\n            if (this._bubbleText) {\n              this._bubbleText.textContent = '口型同步测试中，请观察嘴部动画！';\n            }\n          }, 1000);\n        }).catch(error => {\n          console.error('播放音频失败:', error);\n          this.updateStatus('播放音频失败，请检查音频文件和浏览器权限');\n        });\n\n        // 音频结束时的处理\n        audio.addEventListener('ended', () => {\n          this.updateStatus('口型同步测试完成');\n          if (this._bubbleText) {\n            this._bubbleText.textContent = '口型同步测试完成！';\n          }\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('测试口型同步失败:', error);\n      this.updateStatus('测试口型同步失败，请检查音频文件');\n    }\n  }\n\n  // ==================== 公共API接口 ====================\n\n  /**\n   * 更换模型\n   * @param modelIdentifier 模型索引 (数字) 或模型名称 (字符串)\n   */\n  public async changeModel(modelIdentifier: number | string): Promise<void> {\n    try {\n      // 如果是数字，按索引切换\n      if (typeof modelIdentifier === 'number') {\n        if (modelIdentifier >= 0 && modelIdentifier < LAppDefine.ModelDirSize) {\n          this._live2DManager.switchToModel(modelIdentifier);\n          this.updateStatus(`切换到模型 ${modelIdentifier + 1}`);\n        } else {\n          this.updateStatus(`无效的模型索引: ${modelIdentifier}`);\n        }\n        return;\n      }\n\n      // 如果是字符串，按名称切换\n      if (typeof modelIdentifier === 'string') {\n        // 首先检查模型是否存在\n        const availableModels = await this._live2DManager.detectAvailableModels();\n        const targetModel = availableModels.find(model => model.name === modelIdentifier);\n\n        if (targetModel) {\n          await this.switchModelByName(modelIdentifier);\n          this.updateStatus(`切换到模型: ${modelIdentifier}`);\n        } else {\n          this.updateStatus(`无效的模型名称: ${modelIdentifier}`);\n          console.warn(`[Control Panel] 无效的模型名称: ${modelIdentifier}`);\n          console.log(`[Control Panel] 可用模型:`, availableModels.map(m => m.name));\n        }\n        return;\n      }\n\n      // 如果既不是数字也不是字符串\n      this.updateStatus(`无效的模型标识符类型: ${typeof modelIdentifier}`);\n    } catch (error) {\n      console.error('[Control Panel] 切换模型失败:', error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      this.updateStatus(`切换模型失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 播放动作\n   * @param group 动作组名 (如: \"Idle\", \"TapBody\")\n   * @param index 动作索引 (从0开始)\n   */\n  public playMotion(group: string, index: number = 0): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (model) {\n      this._live2DManager.playMotion(group, index);\n      this.updateStatus(`播放动作: ${group} ${index + 1}`);\n    } else {\n      this.updateStatus('请先选择一个模型');\n    }\n  }\n\n  /**\n   * 播放表情\n   * @param expressionIdentifier 表情索引 (数字) 或表情ID (字符串)\n   */\n  public playExpression(expressionIdentifier: number | string): void {\n    const expressionNames = this._live2DManager.getExpressionNames();\n\n    try {\n      let expressionName: string | null = null;\n\n      // 如果是数字，按索引查找\n      if (typeof expressionIdentifier === 'number') {\n        if (expressionIdentifier >= 0 && expressionIdentifier < expressionNames.length) {\n          expressionName = expressionNames[expressionIdentifier];\n        } else {\n          this.updateStatus(`无效的表情索引: ${expressionIdentifier}`);\n          return;\n        }\n      }\n\n      // 如果是字符串，按表情ID查找\n      if (typeof expressionIdentifier === 'string') {\n        if (expressionNames.includes(expressionIdentifier)) {\n          expressionName = expressionIdentifier;\n        } else {\n          this.updateStatus(`无效的表情ID: ${expressionIdentifier}`);\n          console.warn(`[Control Panel] 无效的表情ID: ${expressionIdentifier}`);\n          console.log(`[Control Panel] 可用表情:`, expressionNames);\n          return;\n        }\n      }\n\n      // 播放表情\n      if (expressionName) {\n        this._live2DManager.setExpression(expressionName);\n        this.updateStatus(`播放表情: ${expressionName}`);\n      } else {\n        this.updateStatus(`无效的表情标识符类型: ${typeof expressionIdentifier}`);\n      }\n    } catch (error) {\n      console.error('[Control Panel] 播放表情失败:', error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      this.updateStatus(`播放表情失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 播放随机表情\n   */\n  public playRandomExpression(): void {\n    this._live2DManager.setRandomExpression();\n    this.updateStatus('播放随机表情');\n  }\n\n  /**\n   * 显示文字气泡\n   * @param text 要显示的文字\n   * @param autoHide 是否自动隐藏 (默认: true, 5秒后隐藏)\n   */\n  public showBubble(text: string, autoHide: boolean = true): void {\n    if (this._bubbleText) {\n      this._bubbleText.textContent = text;\n\n      // 同时更新输入框\n      const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n      if (bubbleTextInput) {\n        bubbleTextInput.value = text;\n      }\n    }\n\n    this.showSpeechBubble();\n\n    if (!autoHide) {\n      // 清除自动隐藏定时器\n      if (this._bubbleTimeout) {\n        clearTimeout(this._bubbleTimeout);\n        this._bubbleTimeout = null;\n      }\n    }\n  }\n\n  /**\n   * 隐藏文字气泡\n   */\n  public hideBubble(): void {\n    this.hideSpeechBubble();\n  }\n\n  /**\n   * 播放音频并进行口型同步\n   * @param audioPath 音频文件路径 (相对于Resources目录)\n   */\n  public playAudioWithLipSync(audioPath: string): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    try {\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audio = new Audio(`../../Resources/${audioPath}`);\n      audio.volume = 0.8;\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.start(`../../Resources/${audioPath}`))\n        ]).then(() => {\n          this.updateStatus(`播放音频: ${audioPath}`);\n        }).catch(error => {\n          console.error('播放音频失败:', error);\n          this.updateStatus('播放音频失败，请检查音频文件和浏览器权限');\n        });\n\n        audio.addEventListener('ended', () => {\n          this.updateStatus('音频播放完成');\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('播放音频失败:', error);\n      this.updateStatus('播放音频失败，请检查音频文件');\n    }\n  }\n\n  /**\n   * 播放base64编码的音频并进行口型同步\n   * @param base64Data base64编码的音频数据 (支持WAV和MP3格式)\n   */\n  public async playAudioFromBase64(base64Data: string): Promise<void> {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    try {\n      this.updateStatus('正在处理音频数据...');\n\n      // 检测音频格式并转换为WAV\n      const wavArrayBuffer = await AudioConverter.processBase64Audio(base64Data);\n\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audioBlob = new Blob([wavArrayBuffer], { type: 'audio/wav' });\n      const audioUrl = URL.createObjectURL(audioBlob);\n      const audio = new Audio(audioUrl);\n      audio.volume = 0.8;\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.startFromArrayBuffer(wavArrayBuffer))\n        ]).then(() => {\n          this.updateStatus('播放base64音频成功');\n        }).catch(error => {\n          console.error('播放base64音频失败:', error);\n          this.updateStatus('播放base64音频失败，请检查音频数据和浏览器权限');\n        });\n\n        audio.addEventListener('ended', () => {\n          this.updateStatus('base64音频播放完成');\n          // 清理URL对象\n          URL.revokeObjectURL(audioUrl);\n        });\n\n        // 添加错误处理\n        audio.addEventListener('error', (error) => {\n          console.error('音频播放错误:', error);\n          this.updateStatus('音频播放出错');\n          URL.revokeObjectURL(audioUrl);\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('处理base64音频失败:', error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      this.updateStatus(`处理base64音频失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 获取当前模型信息\n   */\n  public getCurrentModelInfo(): any {\n    const model = this._live2DManager.getCurrentModel();\n    if (model) {\n      return {\n        modelName: 'Current Model',\n        hasModel: true,\n        motionGroups: this._live2DManager.getMotionGroups(),\n        expressions: this._live2DManager.getExpressionNames()\n      };\n    }\n    return {\n      modelName: 'No Model',\n      hasModel: false,\n      motionGroups: [],\n      expressions: []\n    };\n  }\n\n  // ==================== 私有方法 ====================\n\n  /**\n   * 切换控制面板显示状态\n   */\n  private toggleControlPanel(): void {\n    const controlPanel = document.querySelector('.control-panel') as HTMLElement;\n    if (controlPanel) {\n      controlPanel.classList.toggle('hidden');\n      console.log('控制面板显示状态已切换');\n    }\n  }\n\n  /**\n   * 更新状态信息\n   */\n  private updateStatus(message: string): void {\n    const statusElement = document.getElementById('statusText');\n    if (statusElement) {\n      statusElement.textContent = message;\n    }\n\n    // 在控制台也输出状态信息\n    LAppPal.printMessage(`[Control Panel] ${message}`);\n  }\n\n  /**\n   * 初始化模型上传功能\n   */\n  private initializeModelUpload(): void {\n    const fileInput = document.getElementById('modelFileInput') as HTMLInputElement;\n    const selectFileBtn = document.getElementById('selectFileBtn') as HTMLButtonElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n    const selectedFileName = document.getElementById('selectedFileName') as HTMLDivElement;\n\n    // 选择文件按钮点击事件\n    selectFileBtn.addEventListener('click', () => {\n      fileInput.click();\n    });\n\n    // 文件选择事件\n    fileInput.addEventListener('change', (event) => {\n      const target = event.target as HTMLInputElement;\n      const file = target.files?.[0];\n\n      if (file) {\n        if (file.type === 'application/zip' || file.name.endsWith('.zip')) {\n          selectedFileName.textContent = `已选择: ${file.name}`;\n          selectedFileName.style.display = 'block';\n          uploadBtn.style.display = 'block';\n          this.updateStatus(`已选择文件: ${file.name}`);\n        } else {\n          alert('请选择ZIP格式的文件');\n          target.value = '';\n        }\n      }\n    });\n\n    // 上传按钮点击事件\n    uploadBtn.addEventListener('click', () => {\n      const file = fileInput.files?.[0];\n      if (file) {\n        this.uploadModel(file);\n      }\n    });\n  }\n\n  /**\n   * 上传模型文件\n   */\n  public async uploadModel(file: File): Promise<void> {\n    const uploadStatus = document.getElementById('uploadStatus') as HTMLDivElement;\n    const uploadStatusText = document.getElementById('uploadStatusText') as HTMLParagraphElement;\n    const progressBar = document.getElementById('progressBar') as HTMLDivElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n\n    try {\n      // 显示上传状态\n      uploadStatus.style.display = 'block';\n      uploadStatus.className = 'upload-status';\n      uploadStatusText.textContent = '正在上传...';\n      progressBar.style.width = '0%';\n      uploadBtn.disabled = true;\n\n      // 创建FormData\n      const formData = new FormData();\n      formData.append('modelZip', file);\n\n      // 上传文件\n      const response = await fetch('http://localhost:3001/api/upload-model', {\n        method: 'POST',\n        body: formData\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n\n        // 上传成功\n        uploadStatus.className = 'upload-status success';\n        uploadStatusText.textContent = `上传成功! 模型 ${result.modelName} 已添加`;\n        progressBar.style.width = '100%';\n\n        // 显示分析结果\n        if (result.analysis) {\n          const analysisInfo: string[] = [];\n          if (result.analysis.hasExpressions) {\n            analysisInfo.push(`表情: ${result.analysis.expressions.length}个`);\n          }\n          if (result.analysis.hasMotions) {\n            analysisInfo.push(`动作组: ${result.analysis.motionGroups.length}个`);\n          }\n          if (result.analysis.issues && result.analysis.issues.length > 0) {\n            analysisInfo.push(`问题: ${result.analysis.issues.length}个`);\n          }\n\n          if (analysisInfo.length > 0) {\n            uploadStatusText.textContent += ` (${analysisInfo.join(', ')})`;\n          }\n        }\n\n        // 刷新模型列表\n        await this.refreshModelList();\n\n        // 清空文件选择\n        this.clearFileSelection();\n\n        // 通知主应用刷新内嵌模型数据\n        this.notifyMainAppModelUploaded(result.modelName);\n\n        this.updateStatus(`模型 ${result.modelName} 上传成功`);\n      } else {\n        const error = await response.json();\n        throw new Error(error.error || '上传失败');\n      }\n    } catch (error) {\n      // 上传失败\n      const errorMessage = error instanceof Error ? error.message : '未知错误';\n      uploadStatus.className = 'upload-status error';\n      uploadStatusText.textContent = `上传失败: ${errorMessage}`;\n      this.updateStatus(`上传失败: ${errorMessage}`);\n    } finally {\n      uploadBtn.disabled = false;\n    }\n  }\n\n  /**\n   * 刷新模型列表\n   */\n  public async refreshModelList(): Promise<void> {\n    try {\n      // 重新初始化模型选择器\n      await this.initializeModelSelector();\n      this.updateStatus('模型列表已更新');\n    } catch (error) {\n      console.error('刷新模型列表失败:', error);\n      this.updateStatus('刷新模型列表失败');\n    }\n  }\n\n  /**\n   * 清空文件选择\n   */\n  private clearFileSelection(): void {\n    const fileInput = document.getElementById('modelFileInput') as HTMLInputElement;\n    const selectedFileName = document.getElementById('selectedFileName') as HTMLDivElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n\n    fileInput.value = '';\n    selectedFileName.style.display = 'none';\n    uploadBtn.style.display = 'none';\n  }\n\n  /**\n   * 删除模型\n   */\n  public async deleteModel(modelName: string): Promise<void> {\n    if (!confirm(`确定要删除模型 \"${modelName}\" 吗？此操作不可撤销。`)) {\n      return;\n    }\n\n    try {\n      this.updateStatus(`正在删除模型: ${modelName}...`);\n\n      const response = await fetch(`http://localhost:3001/api/models/${modelName}`, {\n        method: 'DELETE'\n      });\n\n      if (response.ok) {\n        await response.json();\n        this.updateStatus(`模型 ${modelName} 删除成功`);\n\n        // 刷新模型列表\n        await this.refreshModelList();\n\n        // 隐藏删除按钮\n        const deleteModelBtn = document.getElementById('deleteModelBtn') as HTMLButtonElement;\n        deleteModelBtn.style.display = 'none';\n\n        // 通知主应用刷新内嵌模型数据\n        this.notifyMainAppModelDeleted(modelName);\n\n      } else {\n        const error = await response.json();\n        throw new Error(error.error || '删除失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '未知错误';\n      this.updateStatus(`删除失败: ${errorMessage}`);\n      alert(`删除模型失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 通知主应用模型已上传\n   */\n  private notifyMainAppModelUploaded(modelName: string): void {\n    try {\n      // 通过postMessage通知父窗口\n      if (window.parent && window.parent !== window) {\n        window.parent.postMessage({\n          type: 'live2d_model_uploaded',\n          modelName: modelName\n        }, '*');\n      }\n\n      // 如果在同一个窗口中，直接调用内嵌管理器的刷新方法\n      if ((window as any).embeddedLive2DManager &&\n          typeof (window as any).embeddedLive2DManager.refreshEmbeddedData === 'function') {\n        (window as any).embeddedLive2DManager.refreshEmbeddedData();\n      }\n    } catch (error) {\n      console.warn('通知主应用模型上传失败:', error);\n    }\n  }\n\n  /**\n   * 通知主应用模型已删除\n   */\n  private notifyMainAppModelDeleted(modelName: string): void {\n    try {\n      // 通过postMessage通知父窗口\n      if (window.parent && window.parent !== window) {\n        window.parent.postMessage({\n          type: 'live2d_model_deleted',\n          modelName: modelName\n        }, '*');\n      }\n\n      // 如果在同一个窗口中，直接调用内嵌管理器的刷新方法\n      if ((window as any).embeddedLive2DManager &&\n          typeof (window as any).embeddedLive2DManager.refreshEmbeddedData === 'function') {\n        (window as any).embeddedLive2DManager.refreshEmbeddedData();\n      }\n    } catch (error) {\n      console.warn('通知主应用模型删除失败:', error);\n    }\n  }\n\n  /**\n   * 切换沉浸模式\n   */\n  public toggleImmersiveMode(): void {\n    this._immersiveMode = !this._immersiveMode;\n\n    const backgroundOpacityControl = document.getElementById('backgroundOpacityControl');\n\n    if (this._immersiveMode) {\n      // 开启沉浸模式 - 显示透明度控制\n      backgroundOpacityControl?.style.setProperty('display', 'block');\n      this.updateLive2DBackgroundOpacity();\n      this.updateStatus('沉浸模式已开启 - 可调整背景透明度');\n    } else {\n      // 关闭沉浸模式 - 清除所有背景并设置完全透明\n      backgroundOpacityControl?.style.setProperty('display', 'none');\n      this.clearBackgroundInternal(); // 清除所有自定义背景\n      this.setLive2DBackgroundTransparent();\n      this.updateStatus('沉浸模式已关闭 - 背景已清除并设为完全透明');\n    }\n  }\n\n  /**\n   * 更新Live2D背景透明度\n   */\n  private updateLive2DBackgroundOpacity(): void {\n    if (!this._immersiveMode) return;\n\n    // 检查是否有自定义背景\n    const body = document.body;\n    const hasCustomBackground = body.style.backgroundImage || document.getElementById('live2d-background-video');\n\n    if (!hasCustomBackground) {\n      // 没有自定义背景时，始终保持透明，显示下层index.html内容\n      body.style.backgroundColor = 'transparent';\n    } else {\n      // 有自定义背景时，通过控制背景透明度来实现效果\n      this.updateBackgroundOverlay();\n    }\n  }\n\n  /**\n   * 设置Live2D背景完全透明\n   */\n  private setLive2DBackgroundTransparent(): void {\n    // 设置背景完全透明\n    const body = document.body;\n    body.style.backgroundColor = 'transparent';\n\n    // 移除遮罩层\n    this.removeBackgroundOverlay();\n  }\n\n  /**\n   * 更新背景遮罩层\n   */\n  private updateBackgroundOverlay(): void {\n    // 移除现有遮罩\n    this.removeBackgroundOverlay();\n\n    // 确保自定义背景始终可见\n    this.restoreHiddenBackground();\n\n    // 透明度逻辑：\n    // 0% = 完全显示自定义背景\n    // 100% = 完全隐藏自定义背景，显示下层index.html\n\n    if (this._backgroundOpacity === 0) {\n      // 0%：完全显示自定义背景，不需要遮罩\n      return;\n    }\n\n    if (this._backgroundOpacity === 100) {\n      // 100%：完全隐藏自定义背景，显示下层内容\n      // 创建完全不透明的遮罩来隐藏背景\n      const overlay = document.createElement('div');\n      overlay.id = 'live2d-background-overlay';\n      overlay.style.position = 'fixed';\n      overlay.style.top = '0';\n      overlay.style.left = '0';\n      overlay.style.width = '100vw';\n      overlay.style.height = '100vh';\n      overlay.style.pointerEvents = 'none';\n      overlay.style.zIndex = '-5';\n      overlay.style.backgroundColor = 'transparent'; // 完全透明，让下层内容显示\n\n      // 通过设置body背景为透明来\"隐藏\"自定义背景\n      document.body.style.backgroundColor = 'transparent';\n\n      // 临时隐藏自定义背景\n      const body = document.body;\n      const backgroundImage = body.style.backgroundImage;\n      const backgroundVideo = document.getElementById('live2d-background-video');\n\n      if (backgroundImage) {\n        body.style.backgroundImage = '';\n        body.setAttribute('data-hidden-background', backgroundImage);\n      }\n      if (backgroundVideo) {\n        backgroundVideo.style.display = 'none';\n      }\n\n      return;\n    }\n\n    // 1-99%：创建渐变遮罩，部分隐藏背景\n    const overlay = document.createElement('div');\n    overlay.id = 'live2d-background-overlay';\n    overlay.style.position = 'fixed';\n    overlay.style.top = '0';\n    overlay.style.left = '0';\n    overlay.style.width = '100vw';\n    overlay.style.height = '100vh';\n    overlay.style.pointerEvents = 'none';\n    overlay.style.zIndex = '-5';\n\n    // 使用透明度来控制背景可见度\n    // 透明度越高，遮罩越不透明，背景越不可见\n    const opacity = this._backgroundOpacity / 100;\n    overlay.style.backgroundColor = `rgba(255, 255, 255, ${opacity})`;\n\n    document.body.appendChild(overlay);\n  }\n\n  /**\n   * 恢复被隐藏的背景\n   */\n  private restoreHiddenBackground(): void {\n    const body = document.body;\n    const hiddenBackground = body.getAttribute('data-hidden-background');\n    const backgroundVideo = document.getElementById('live2d-background-video');\n\n    if (hiddenBackground) {\n      body.style.backgroundImage = hiddenBackground;\n      body.removeAttribute('data-hidden-background');\n    }\n\n    if (backgroundVideo) {\n      backgroundVideo.style.display = 'block';\n    }\n  }\n\n  /**\n   * 移除背景遮罩层\n   */\n  private removeBackgroundOverlay(): void {\n    const overlay = document.getElementById('live2d-background-overlay');\n    if (overlay) {\n      overlay.remove();\n    }\n  }\n\n  /**\n   * 更新背景透明度（保留原方法用于控制面板）\n   */\n  private updateBackgroundOpacity(): void {\n    if (this._immersiveMode) {\n      this.updateLive2DBackgroundOpacity();\n    }\n  }\n\n  /**\n   * 设置背景透明度\n   */\n  public setBackgroundOpacity(opacity: number): void {\n    if (opacity < 0 || opacity > 100) {\n      console.warn('透明度值应在0-100之间');\n      return;\n    }\n\n    this._backgroundOpacity = opacity;\n\n    // 更新滑动条显示\n    const backgroundOpacitySlider = document.getElementById('backgroundOpacitySlider') as HTMLInputElement;\n    const opacityValue = document.getElementById('opacityValue');\n\n    if (backgroundOpacitySlider) {\n      backgroundOpacitySlider.value = opacity.toString();\n    }\n    if (opacityValue) {\n      opacityValue.textContent = `${opacity}%`;\n    }\n\n    // 如果沉浸模式开启，更新背景\n    if (this._immersiveMode) {\n      this.updateBackgroundOpacity();\n    }\n  }\n\n  /**\n   * 处理背景文件选择\n   */\n  private handleBackgroundFileSelect(file: File): void {\n    this._currentBackgroundFile = file;\n\n    const backgroundFileName = document.getElementById('backgroundFileName');\n    const applyBackgroundBtn = document.getElementById('applyBackgroundBtn');\n\n    if (backgroundFileName) {\n      backgroundFileName.textContent = `已选择: ${file.name}`;\n      backgroundFileName.style.display = 'block';\n    }\n\n    if (applyBackgroundBtn) {\n      applyBackgroundBtn.style.display = 'inline-block';\n    }\n\n    this.updateStatus(`已选择背景文件: ${file.name}`);\n  }\n\n  /**\n   * 应用背景\n   */\n  private applyBackground(): void {\n    if (!this._currentBackgroundFile) {\n      this.updateStatus('请先选择背景文件');\n      return;\n    }\n\n    // 清理之前的背景URL\n    if (this._currentBackgroundUrl) {\n      URL.revokeObjectURL(this._currentBackgroundUrl);\n    }\n\n    // 创建新的URL\n    this._currentBackgroundUrl = URL.createObjectURL(this._currentBackgroundFile);\n\n    // 应用背景\n    this.setBackgroundFromUrl(this._currentBackgroundUrl, this._currentBackgroundFile.type);\n\n    this.updateStatus(`背景已应用: ${this._currentBackgroundFile.name}`);\n    this.showBackgroundStatus('success', '背景设置成功');\n  }\n\n  /**\n   * 从URL设置背景\n   */\n  public setBackgroundFromUrl(url: string, type?: string): void {\n    // 清除现有背景\n    this.clearBackgroundInternal();\n\n    // 获取body元素作为背景容器\n    const body = document.body;\n\n    if (type && type.startsWith('video/')) {\n      // 视频背景 - 添加到body作为背景\n      const video = document.createElement('video');\n      video.src = url;\n      video.autoplay = true;\n      video.loop = true;\n      video.muted = true;\n      video.style.position = 'fixed';\n      video.style.top = '0';\n      video.style.left = '0';\n      video.style.width = '100vw';\n      video.style.height = '100vh';\n      video.style.objectFit = 'cover';\n      video.style.zIndex = '-10';\n      video.style.pointerEvents = 'none';\n      video.id = 'live2d-background-video';\n\n      body.appendChild(video);\n      this.updateStatus('视频背景已设置到Live2D背景');\n    } else {\n      // 图片背景 - 设置到body\n      body.style.backgroundImage = `url(${url})`;\n      body.style.backgroundSize = 'cover';\n      body.style.backgroundPosition = 'center';\n      body.style.backgroundRepeat = 'no-repeat';\n      body.style.backgroundAttachment = 'fixed';\n      this.updateStatus('图片背景已设置到Live2D背景');\n    }\n  }\n\n  /**\n   * 清除背景\n   */\n  public clearBackground(): void {\n    this.clearBackgroundInternal();\n\n    // 清理文件选择状态\n    const backgroundFileName = document.getElementById('backgroundFileName');\n    const applyBackgroundBtn = document.getElementById('applyBackgroundBtn');\n    const backgroundFileInput = document.getElementById('backgroundFileInput') as HTMLInputElement;\n\n    if (backgroundFileName) {\n      backgroundFileName.style.display = 'none';\n    }\n    if (applyBackgroundBtn) {\n      applyBackgroundBtn.style.display = 'none';\n    }\n    if (backgroundFileInput) {\n      backgroundFileInput.value = '';\n    }\n\n    // 清理URL\n    if (this._currentBackgroundUrl) {\n      URL.revokeObjectURL(this._currentBackgroundUrl);\n      this._currentBackgroundUrl = null;\n    }\n\n    this._currentBackgroundFile = null;\n    this.updateStatus('背景已清除');\n    this.showBackgroundStatus('success', '背景已清除');\n  }\n\n  /**\n   * 内部清除背景方法\n   */\n  private clearBackgroundInternal(): void {\n    const body = document.body;\n\n    // 清除body的背景图片\n    body.style.backgroundImage = '';\n    body.style.backgroundSize = '';\n    body.style.backgroundPosition = '';\n    body.style.backgroundRepeat = '';\n    body.style.backgroundAttachment = '';\n\n    // 清除隐藏背景的标记\n    body.removeAttribute('data-hidden-background');\n\n    // 移除Live2D背景视频元素\n    const backgroundVideo = document.getElementById('live2d-background-video');\n    if (backgroundVideo) {\n      backgroundVideo.remove();\n    }\n\n    // 移除背景遮罩层\n    this.removeBackgroundOverlay();\n\n    // 恢复默认透明背景\n    if (this._immersiveMode) {\n      this.updateLive2DBackgroundOpacity();\n    } else {\n      body.style.backgroundColor = 'transparent';\n    }\n  }\n\n  /**\n   * 显示背景状态信息\n   */\n  private showBackgroundStatus(type: 'success' | 'error', message: string): void {\n    const backgroundStatus = document.getElementById('backgroundStatus');\n    const backgroundStatusText = document.getElementById('backgroundStatusText');\n\n    if (backgroundStatus && backgroundStatusText) {\n      backgroundStatus.className = `upload-status ${type}`;\n      backgroundStatusText.textContent = message;\n      backgroundStatus.style.display = 'block';\n\n      // 3秒后隐藏状态\n      setTimeout(() => {\n        backgroundStatus.style.display = 'none';\n      }, 3000);\n    }\n  }\n\n  /**\n   * 释放资源\n   */\n  public release(): void {\n    // 清理背景URL\n    if (this._currentBackgroundUrl) {\n      URL.revokeObjectURL(this._currentBackgroundUrl);\n    }\n    LAppDelegate.releaseInstance();\n  }\n}\n\n// 全局应用程序实例\nlet app: ControlPanelApp;\n\n/**\n * 页面加载完成后初始化应用程序\n */\nwindow.addEventListener('DOMContentLoaded', async () => {\n  app = new ControlPanelApp();\n  await app.initialize();\n\n  // 将API暴露到全局对象，方便外部调用\n  (window as any).Live2DControlPanel = {\n    // 模型控制\n    changeModel: (modelIdentifier: number | string) => app.changeModel(modelIdentifier),\n\n    // 动作控制\n    playMotion: (group: string, index: number = 0) => app.playMotion(group, index),\n\n    // 表情控制\n    playExpression: (expressionIdentifier: number | string) => app.playExpression(expressionIdentifier),\n    playRandomExpression: () => app.playRandomExpression(),\n\n    // 气泡控制\n    showBubble: (text: string, autoHide: boolean = true) => app.showBubble(text, autoHide),\n    hideBubble: () => app.hideBubble(),\n\n    // 音频播放\n    playAudio: (audioPath: string) => app.playAudioWithLipSync(audioPath),\n    playAudioFromBase64: (base64Data: string) => app.playAudioFromBase64(base64Data),\n\n    // 模型上传\n    uploadModel: (file: File) => app.uploadModel(file),\n    refreshModelList: () => app.refreshModelList(),\n    deleteModel: (modelName: string) => app.deleteModel(modelName),\n\n    // 信息获取\n    getModelInfo: () => app.getCurrentModelInfo(),\n\n    // 沉浸模式控制\n    toggleImmersiveMode: () => app.toggleImmersiveMode(),\n    setBackgroundOpacity: (opacity: number) => app.setBackgroundOpacity(opacity),\n\n    // 背景控制\n    setBackgroundFromUrl: (url: string, type?: string) => app.setBackgroundFromUrl(url, type),\n    clearBackground: () => app.clearBackground(),\n\n    // 内部实例（高级用户使用）\n    _app: app\n  };\n\n  // 添加消息监听器，处理来自父窗口的API调用\n  window.addEventListener('message', (event) => {\n    // 处理控制面板切换消息\n    if (event.data && event.data.type === 'toggle_panel') {\n      const controlPanel = document.querySelector('.control-panel') as HTMLElement;\n      if (controlPanel) {\n        controlPanel.classList.toggle('hidden');\n        console.log('控制面板显示状态已切换');\n      }\n      return;\n    }\n\n    // 验证消息来源（可选，根据需要调整）\n    if (event.data && event.data.type === 'live2d_api_call') {\n      const { messageId, method, args } = event.data;\n\n      try {\n        // 获取API方法\n        const api = (window as any).Live2DControlPanel;\n        if (api && typeof api[method] === 'function') {\n          // 调用API方法\n          const result = api[method](...args);\n\n          // 如果返回Promise，等待结果\n          if (result && typeof result.then === 'function') {\n            result.then((res: any) => {\n              // 发送成功响应\n              (event.source as any)?.postMessage({\n                type: 'live2d_api_response',\n                messageId: messageId,\n                success: true,\n                result: res\n              }, '*');\n            }).catch((error: any) => {\n              // 发送错误响应\n              (event.source as any)?.postMessage({\n                type: 'live2d_api_response',\n                messageId: messageId,\n                success: false,\n                error: error.message || '调用失败'\n              }, '*');\n            });\n          } else {\n            // 同步方法，直接发送结果\n            (event.source as any)?.postMessage({\n              type: 'live2d_api_response',\n              messageId: messageId,\n              success: true,\n              result: result\n            }, '*');\n          }\n        } else {\n          // 方法不存在\n          (event.source as any)?.postMessage({\n            type: 'live2d_api_response',\n            messageId: messageId,\n            success: false,\n            error: `方法 ${method} 不存在`\n          }, '*');\n        }\n      } catch (error: any) {\n        // 发送错误响应\n        (event.source as any)?.postMessage({\n          type: 'live2d_api_response',\n          messageId: messageId,\n          success: false,\n          error: error.message || '调用失败'\n        }, '*');\n      }\n    }\n  });\n});\n\n/**\n * 页面卸载时释放资源\n */\nwindow.addEventListener('beforeunload', () => {\n  if (app) {\n    app.release();\n  }\n});\n"], "names": ["_AudioConverter", "base64Data", "cleanBase64", "binaryString", "bytes", "i", "riff", "wave", "error", "mp3<PERSON><PERSON><PERSON><PERSON><PERSON>er", "audioBuffer", "errorMessage", "numberOfChannels", "sampleRate", "length", "bitsPerSample", "bytesPerSample", "dataSize", "fileSize", "arrayBuffer", "view", "offset", "channel", "channelData", "dataOffset", "sample", "intSample", "string", "format", "AudioConverter", "ControlPanelApp", "LAppDelegate", "modelSelector", "deleteModelBtn", "availableModels", "model", "index", "option", "event", "target", "selectedIndex", "selected<PERSON><PERSON>l", "modelName", "panelToggleBtn", "randomMotionBtn", "LAppDefine.MotionGroupIdle", "stopMotionBtn", "randomExpressionBtn", "resetExpressionBtn", "testLipSyncBtn", "resetTransformBtn", "backgroundOpacitySlider", "opacityValue", "e", "value", "selectBackgroundBtn", "backgroundFileInput", "file", "_a", "applyBackgroundBtn", "clearBackgroundBtn", "showBubbleBtn", "hideBubbleBtn", "bubbleTextInput", "positionButtons", "button", "position", "btn", "checkModelReady", "modelIndex", "validation", "modelInfo", "motionCount", "expressionCount", "motionGroups", "expressions", "totalMotions", "group", "motionCountElement", "expressionCountElement", "modelInfoElement", "motionButtonsContainer", "motionsData", "groupName", "motion", "expressionButtonsContainer", "expressionName", "canvasContainer", "containerRect", "bubbleRect", "actualLeft", "actualTop", "newX", "newY", "bubbleWidth", "bubbleHeight", "touch", "manager", "audioPath", "audio", "wavFileHandler", "modelIdentifier", "LAppDefine.ModelDirSize", "m", "expressionIdentifier", "expressionNames", "text", "autoHide", "wavA<PERSON><PERSON><PERSON><PERSON>er", "audioBlob", "audioUrl", "controlPanel", "message", "statusElement", "LAppPal", "fileInput", "selectFileBtn", "uploadBtn", "selected<PERSON><PERSON><PERSON><PERSON>", "uploadStatus", "uploadStatusText", "progressBar", "formData", "response", "result", "analysisInfo", "backgroundOpacityControl", "body", "overlay", "backgroundImage", "backgroundVideo", "opacity", "hiddenBackground", "backgroundFileName", "url", "type", "video", "backgroundStatus", "backgroundStatusText", "app", "messageId", "method", "args", "api", "res", "_b", "_c"], "mappings": "oEAIO,MAAMA,EAAN,MAAMA,CAAe,CAM1B,OAAe,iBAAgC,CACzC,OAAC,KAAK,eACR,KAAK,aAAe,IAAK,OAAO,cAAiB,OAAe,qBAE3D,KAAK,YAAA,CAQd,OAAO,kBAAkBC,EAA+C,CAClE,GAAA,CAEF,MAAMC,EAAcD,EAAW,QAAQ,6BAA8B,EAAE,EAGjEE,EAAe,KAAKD,CAAW,EAC/BE,EAAQ,IAAI,WAAW,KAAK,IAAI,GAAID,EAAa,MAAM,CAAC,EAC9D,QAASE,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChCD,EAAMC,CAAC,EAAIF,EAAa,WAAWE,CAAC,EAIlC,GAAAD,EAAM,QAAU,GAAI,CACtB,MAAME,EAAO,OAAO,aAAaF,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EACjEG,EAAO,OAAO,aAAaH,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,EAAE,EAAGA,EAAM,EAAE,CAAC,EACrE,GAAAE,IAAS,QAAUC,IAAS,OACvB,MAAA,KACT,CAIE,OAAAH,EAAM,QAAU,IAEdA,EAAM,CAAC,IAAM,IAAQA,EAAM,CAAC,IAAM,IAAQA,EAAM,CAAC,IAAM,IAKvDA,EAAM,CAAC,IAAM,MAASA,EAAM,CAAC,EAAI,OAAU,KACtC,MAIJ,gBACAI,EAAO,CACN,eAAA,MAAM,YAAaA,CAAK,EACzB,SAAA,CACT,CAQF,OAAO,oBAAoBP,EAAiC,CAE1D,MAAMC,EAAcD,EAAW,QAAQ,6BAA8B,EAAE,EAEjEE,EAAe,KAAKD,CAAW,EAC/BE,EAAQ,IAAI,WAAWD,EAAa,MAAM,EAChD,QAASE,EAAI,EAAGA,EAAIF,EAAa,OAAQE,IACvCD,EAAMC,CAAC,EAAIF,EAAa,WAAWE,CAAC,EAEtC,OAAOD,EAAM,MAAA,CAQf,aAAa,gBAAgBK,EAAmD,CAC1E,GAAA,CAIF,MAAMC,EAAc,MAHC,KAAK,gBAAgB,EAGH,gBAAgBD,EAAe,MAAM,CAAC,CAAC,EAGvE,OAAA,KAAK,iBAAiBC,CAAW,QACjCF,EAAO,CACN,QAAA,MAAM,aAAcA,CAAK,EACjC,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EAC1E,MAAM,IAAI,MAAM,cAAcG,CAAY,EAAE,CAAA,CAC9C,CAQF,OAAO,iBAAiBD,EAAuC,CAC7D,MAAME,EAAmBF,EAAY,iBAC/BG,EAAaH,EAAY,WACzBI,EAASJ,EAAY,OACrBK,EAAgB,GAChBC,EAAiBD,EAAgB,EAGjCE,EAAWH,EAASF,EAAmBI,EACvCE,EAAW,GAAKD,EAGhBE,EAAc,IAAI,YAAYD,CAAQ,EACtCE,EAAO,IAAI,SAASD,CAAW,EAGrC,IAAIE,EAAS,EAGR,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAElDD,EAAK,UAAUC,EAAQH,EAAW,EAAG,EAAI,EAAaG,GAAA,EAEjD,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAG7C,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAE7CD,EAAA,UAAUC,EAAQ,GAAI,EAAI,EAAaA,GAAA,EAEvCD,EAAA,UAAUC,EAAQ,EAAG,EAAI,EAAaA,GAAA,EAEtCD,EAAA,UAAUC,EAAQT,EAAkB,EAAI,EAAaS,GAAA,EAErDD,EAAA,UAAUC,EAAQR,EAAY,EAAI,EAAaQ,GAAA,EAEpDD,EAAK,UAAUC,EAAQR,EAAaD,EAAmBI,EAAgB,EAAI,EAAaK,GAAA,EAExFD,EAAK,UAAUC,EAAQT,EAAmBI,EAAgB,EAAI,EAAaK,GAAA,EAEtED,EAAA,UAAUC,EAAQN,EAAe,EAAI,EAAaM,GAAA,EAGlD,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAE7CD,EAAA,UAAUC,EAAQJ,EAAU,EAAI,EAAaI,GAAA,EAGlD,QAASC,EAAU,EAAGA,EAAUV,EAAkBU,IAAW,CACrD,MAAAC,EAAcb,EAAY,eAAeY,CAAO,EAClD,IAAAE,EAAa,GAAKF,EAAUN,EAEhC,QAASX,EAAI,EAAGA,EAAIS,EAAQT,IAAK,CAEzB,MAAAoB,EAAS,KAAK,IAAI,GAAI,KAAK,IAAI,EAAGF,EAAYlB,CAAC,CAAC,CAAC,EACjDqB,EAAYD,EAAS,EAAIA,EAAS,MAASA,EAAS,MAErDL,EAAA,SAASI,EAAYE,EAAW,EAAI,EACzCF,GAAcZ,EAAmBI,CAAA,CACnC,CAGK,OAAAG,CAAA,CAST,OAAe,YAAYC,EAAgBC,EAAgBM,EAAsB,CAC/E,QAAStB,EAAI,EAAGA,EAAIsB,EAAO,OAAQtB,IACjCe,EAAK,SAASC,EAAShB,EAAGsB,EAAO,WAAWtB,CAAC,CAAC,CAChD,CAQF,aAAa,mBAAmBJ,EAA0C,CAClE,MAAA2B,EAAS,KAAK,kBAAkB3B,CAAU,EAC1CkB,EAAc,KAAK,oBAAoBlB,CAAU,EAEvD,OAAQ2B,EAAQ,CACd,IAAK,MACH,eAAQ,IAAI,eAAe,EACpBT,EAET,IAAK,MACH,eAAQ,IAAI,iBAAiB,EACtB,MAAM,KAAK,gBAAgBA,CAAW,EAE/C,QACE,QAAQ,KAAK,kBAAkB,EAC3B,GAAA,CACK,OAAA,MAAM,KAAK,gBAAgBA,CAAW,OAC/B,CACd,eAAQ,MAAM,qBAAqB,EAC5BA,CAAA,CACT,CACJ,CAEJ,EA9MEnB,EAAe,aAAoC,KAD9C,IAAM6B,EAAN7B,ECaP,MAAM8B,CAAgB,CAAtB,aAAA,CAGE,KAAQ,mBAA6B,EACrC,KAAQ,cAAoC,KAC5C,KAAQ,YAAkC,KAC1C,KAAQ,gBAA0B,SAClC,KAAQ,eAAgC,KACxC,KAAQ,YAAuB,GAC/B,KAAQ,YAAwC,CAAE,EAAG,EAAG,EAAG,CAAE,EAC7D,KAAQ,0BAAqC,GAC7C,KAAQ,eAA0B,GAClC,KAAQ,mBAA6B,EACrC,KAAQ,uBAAsC,KAC9C,KAAQ,sBAAuC,IAAA,CAK/C,MAAa,YAA4B,CAElC,KAAA,UAAYC,EAAa,YAAY,EAC1C,KAAK,UAAU,WAAW,EAGrB,KAAA,eAAiB,KAAK,UAAU,iBAAiB,EAGtD,MAAM,KAAK,aAAa,EAGxB,KAAK,UAAU,IAAI,CAAA,CAMrB,MAAc,cAA8B,CAE1C,MAAM,KAAK,wBAAwB,EAGnC,KAAK,yBAAyB,EAG9B,KAAK,uBAAuB,EAG5B,KAAK,sBAAsB,EAG3B,KAAK,uBAAuB,CAAA,CAM9B,MAAc,yBAAyC,CAC/C,MAAAC,EAAgB,SAAS,eAAe,eAAe,EACvDC,EAAiB,SAAS,eAAe,gBAAgB,EAGzDC,EAAkB,MAAM,KAAK,eAAe,sBAAsB,EAGxEF,EAAc,UAAY,oCAGVE,EAAA,QAAQ,CAACC,EAAOC,IAAU,CAClC,MAAAC,EAAS,SAAS,cAAc,QAAQ,EACvCA,EAAA,MAAQD,EAAM,SAAS,EACvBC,EAAA,YAAc,GAAGF,EAAM,IAAI,IAAIA,EAAM,SAAW,SAAW,IAAM,IAAI,GACrEE,EAAA,aAAa,kBAAmBF,EAAM,IAAI,EACjDE,EAAO,aAAa,uBAAwBF,EAAM,eAAe,UAAU,EAC3EE,EAAO,aAAa,mBAAoBF,EAAM,WAAW,UAAU,EACnEH,EAAc,YAAYK,CAAM,CAAA,CACjC,EAGaL,EAAA,iBAAiB,SAAU,MAAOM,GAAU,CACxD,MAAMC,EAASD,EAAM,OACfE,EAAgB,SAASD,EAAO,KAAK,EAE3C,GAAI,CAAC,MAAMC,CAAa,GAAKN,EAAgBM,CAAa,EAAG,CACrD,MAAAC,EAAgBP,EAAgBM,CAAa,EAC7C,MAAA,KAAK,kBAAkBC,EAAc,IAAI,EAC/CR,EAAe,MAAM,QAAU,QAChBA,EAAA,aAAa,kBAAmBQ,EAAc,IAAI,EAGjE,KAAK,iBAAiBA,CAAa,CAAA,MAEnCR,EAAe,MAAM,QAAU,OAC/B,KAAK,cAAc,CACrB,CACD,EAGcA,EAAA,iBAAiB,QAAS,IAAM,CACvC,MAAAS,EAAYT,EAAe,aAAa,iBAAiB,EAC3DS,GACF,KAAK,YAAYA,CAAS,CAC5B,CACD,EAGGR,EAAgB,OAAS,IAC3BF,EAAc,MAAQ,IACtB,MAAM,KAAK,kBAAkBE,EAAgB,CAAC,EAAE,IAAI,EACpDD,EAAe,MAAM,QAAU,QAC/BA,EAAe,aAAa,kBAAmBC,EAAgB,CAAC,EAAE,IAAI,EAGjE,KAAA,iBAAiBA,EAAgB,CAAC,CAAC,EAC1C,CAMM,0BAAiC,CAEjC,MAAAS,EAAiB,SAAS,eAAe,gBAAgB,EAC/CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC9C,KAAK,mBAAmB,CAAA,GAIpB,MAAAC,EAAkB,SAAS,eAAe,iBAAiB,EAChDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC1C,KAAA,eAAe,iBAAiBC,CAA0B,EAC/D,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,eAAe,eAAe,EACnC,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAsB,SAAS,eAAe,qBAAqB,EACpDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CACnD,KAAK,eAAe,oBAAoB,EACxC,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAqB,SAAS,eAAe,oBAAoB,EACnDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAE5C,MAAAb,EAAQ,KAAK,eAAe,gBAAgB,EAC9CA,IACIA,EAAA,uBAAuB,eAAe,EAC5C,KAAK,aAAa,MAAM,EAC1B,GAII,MAAAc,EAAiB,SAAS,eAAe,gBAAgB,EAC/CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC9C,KAAK,YAAY,CAAA,GAIb,MAAAC,EAAoB,SAAS,eAAe,mBAAmB,EAClDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CACjD,KAAK,eAAe,CAAA,GAMhB,MAAAC,EAA0B,SAAS,eAAe,yBAAyB,EAC3EC,EAAe,SAAS,eAAe,cAAc,EAElCD,GAAA,MAAAA,EAAA,iBAAiB,QAAUE,GAAM,CACxD,MAAMd,EAASc,EAAE,OACXC,EAAQ,SAASf,EAAO,KAAK,EACnC,KAAK,mBAAqBe,EACtBF,IACWA,EAAA,YAAc,GAAGE,CAAK,KAErC,KAAK,wBAAwB,CAAA,GAIzB,MAAAC,EAAsB,SAAS,eAAe,qBAAqB,EACnEC,EAAsB,SAAS,eAAe,qBAAqB,EAEpDD,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CACnDC,GAAA,MAAAA,EAAqB,OAAM,GAIRA,GAAA,MAAAA,EAAA,iBAAiB,SAAWH,GAAM,OAE/C,MAAAI,GAAOC,EADEL,EAAE,OACG,QAAP,YAAAK,EAAe,GACxBD,GACF,KAAK,2BAA2BA,CAAI,CACtC,GAII,MAAAE,EAAqB,SAAS,eAAe,oBAAoB,EACnDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAClD,KAAK,gBAAgB,CAAA,GAIjB,MAAAC,EAAqB,SAAS,eAAe,oBAAoB,EACnDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAClD,KAAK,gBAAgB,CAAA,EACtB,CAMK,wBAA+B,CAEhC,KAAA,cAAgB,SAAS,eAAe,cAAc,EACtD,KAAA,YAAc,SAAS,eAAe,YAAY,EAGjD,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,iBAAiB,CAAA,GAIlB,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,iBAAiB,CAAA,GAIlB,MAAAC,EAAkB,SAAS,eAAe,iBAAiB,EAChDA,GAAA,MAAAA,EAAA,iBAAiB,QAAUzB,GAAU,CACpD,MAAMC,EAASD,EAAM,OACjB,KAAK,cACF,KAAA,YAAY,YAAcC,EAAO,MACxC,GAII,MAAAyB,EAAkB,SAAS,iBAAiB,kBAAkB,EACpEA,EAAgB,QAAkBC,GAAA,CACzBA,EAAA,iBAAiB,QAAU3B,GAAU,CAC1C,MAAMC,EAASD,EAAM,OACf4B,EAAW3B,EAAO,aAAa,eAAe,EAEhD2B,IAEFF,EAAgB,QAAeG,GAAAA,EAAI,UAAU,OAAO,QAAQ,CAAC,EACtD5B,EAAA,UAAU,IAAI,QAAQ,EAG7B,KAAK,kBAAkB2B,CAAQ,EACjC,CACD,CAAA,CACF,EAGI,KAAA,kBAAkB,KAAK,eAAe,EAG3C,KAAK,oBAAoB,EAGzB,WAAW,IAAM,CACX,KAAK,cACP,KAAK,YAAY,YAAc,kBAC/B,KAAK,iBAAiB,IAEvB,GAAI,CAAA,CAMD,wBAA+B,CAErC,MAAME,EAAkB,SAAY,CAC9B,KAAK,eAAe,gBACtB,KAAK,gBAAgB,EACrB,MAAM,KAAK,oBAAoB,EAC/B,MAAM,KAAK,wBAAwB,EACnC,KAAK,aAAa,QAAQ,GAE1B,WAAWA,EAAiB,GAAG,CAEnC,EAEgBA,EAAA,CAAA,CAMV,YAAYC,EAA0B,CAC5C,KAAK,mBAAqBA,EACrB,KAAA,eAAe,cAAcA,CAAU,EAC5C,KAAK,aAAa,WAAW,EAG7B,KAAK,aAAa,EAGlB,KAAK,uBAAuB,CAAA,CAM9B,MAAc,kBAAkB3B,EAAkC,CAC3D,KAAA,eAAe,gBAAgBA,CAAS,EACxC,KAAA,aAAa,WAAWA,CAAS,KAAK,EAG3C,KAAK,aAAa,EAGlB,KAAK,uBAAuB,EAGxB,GAAA,CACF,MAAM4B,EAAa,MAAM,KAAK,eAAe,cAAc5B,CAAS,EAChE4B,GAAc,CAACA,EAAW,SACvB,KAAA,aAAa,MAAM5B,CAAS,UAAU4B,EAAW,SAAS,OAAO,KAAK,IAAI,CAAC,EAAE,QAE7E9D,EAAO,CACN,QAAA,KAAK,UAAWA,CAAK,CAAA,CAC/B,CAMM,iBAAiB2B,EAAkB,CACnC,MAAAoC,EAAY,SAAS,eAAe,WAAW,EAC/CC,EAAc,SAAS,eAAe,aAAa,EACnDC,EAAkB,SAAS,eAAe,iBAAiB,EAE7DF,GAAaC,GAAeC,IAC9BF,EAAU,MAAM,QAAU,QAC1BC,EAAY,YAAcrC,EAAM,aAAeA,EAAM,aAAa,OAAO,WAAa,IACtFsC,EAAgB,YAActC,EAAM,YAAcA,EAAM,YAAY,OAAO,WAAa,IAC1F,CAMM,eAAsB,CACtB,MAAAoC,EAAY,SAAS,eAAe,WAAW,EACjDA,IACFA,EAAU,MAAM,QAAU,OAC5B,CAMM,qBAAqC,CACrC,MAAAvC,EAAgB,SAAS,eAAe,eAAe,EACzD,OAAAA,GAAiBA,EAAc,cAAgB,EAC1BA,EAAc,QAAQA,EAAc,aAAa,EAClD,aAAa,iBAAiB,EAE/C,IAAA,CAMD,iBAAwB,CACxB,MAAAG,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,GAAS,CAACA,EAAM,kBACnB,OAGI,MAAAuC,EAAe,KAAK,eAAe,gBAAgB,EACnDC,EAAc,KAAK,eAAe,mBAAmB,EAG3D,IAAIC,EAAe,EACnBF,EAAa,QAAiBG,GAAA,CACZD,GAAA,KAAK,eAAe,eAAeC,CAAK,CAAA,CACzD,EAGK,MAAAC,EAAqB,SAAS,eAAe,aAAa,EAC1DC,EAAyB,SAAS,eAAe,iBAAiB,EAClEC,EAAmB,SAAS,eAAe,WAAW,EAExDF,IAAoBA,EAAmB,YAAcF,EAAa,SAAS,GAC3EG,IAAwBA,EAAuB,YAAcJ,EAAY,OAAO,SAAS,GACzFK,IAAmCA,EAAA,MAAM,QAAU,OAAA,CAMzD,MAAc,qBAAqC,CAC3C,MAAAC,EAAyB,SAAS,eAAe,eAAe,EACtE,GAAI,CAACA,EAAwB,OAE7BA,EAAuB,UAAY,uCAE/B,GAAA,CAGF,GADqB,KAAK,eAAe,gBAAgB,EACvC,CAEV,MAAAvC,EAAY,KAAK,oBAAoB,EAC3C,GAAIA,EAAW,CACb,MAAMwC,EAAc,MAAM,KAAK,eAAe,0BAA0BxC,CAAS,EAEjFuC,EAAuB,UAAY,GAEnC,UAAWE,KAAaD,EAClBA,EAAY,eAAeC,CAAS,GAClBD,EAAYC,CAAS,EAC7B,QAAQ,CAACC,EAAQhD,IAAU,CACrC,GAAIgD,EAAO,OAAQ,CACX,MAAAnB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,wBACnBA,EAAO,YAAc,GAAGkB,CAAS,IAAI/C,EAAQ,CAAC,GACvC6B,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,WAAWkB,EAAW/C,CAAK,EAC/C,KAAK,aAAa,SAAS+C,CAAS,IAAI/C,EAAQ,CAAC,EAAE,CAAA,CACpD,EACD6C,EAAuB,YAAYhB,CAAM,CAAA,CAC3C,CACD,EAIDgB,EAAuB,SAAS,SAAW,IAC7CA,EAAuB,UAAY,qCAErC,MAAA,CACF,QAEKzE,EAAO,CACN,QAAA,KAAK,uBAAwBA,CAAK,CAAA,CAI5CyE,EAAuB,UAAY,GACd,KAAK,eAAe,gBAAgB,EAE5C,QAAiBJ,GAAA,CAC5B,MAAML,EAAc,KAAK,eAAe,eAAeK,CAAK,EAE5D,QAASxE,EAAI,EAAGA,EAAImE,EAAanE,IAAK,CAC9B,MAAA4D,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,wBACnBA,EAAO,YAAc,GAAGY,CAAK,IAAIxE,EAAI,CAAC,GAC/B4D,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,WAAWY,EAAOxE,CAAC,EACvC,KAAK,aAAa,SAASwE,CAAK,IAAIxE,EAAI,CAAC,EAAE,CAAA,CAC5C,EACD4E,EAAuB,YAAYhB,CAAM,CAAA,CAC3C,CACD,EAEGgB,EAAuB,SAAS,SAAW,IAC7CA,EAAuB,UAAY,oCACrC,CAMF,MAAc,yBAAyC,CAC/C,MAAAI,EAA6B,SAAS,eAAe,mBAAmB,EAC9E,GAAI,CAACA,EAA4B,OAEjCA,EAA2B,UAAY,uCAEnC,GAAA,CAGF,GADqB,KAAK,eAAe,gBAAgB,EACvC,CACV,MAAA3C,EAAY,KAAK,oBAAoB,EAC3C,GAAIA,EAAW,CACb,MAAMiC,EAAc,MAAM,KAAK,eAAe,8BAA8BjC,CAAS,EAErF2C,EAA2B,UAAY,GAEvCV,EAAY,QAA0BW,GAAA,CAC9B,MAAArB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,4BACnBA,EAAO,YAAcqB,EACdrB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,cAAcqB,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,CAC5C,EACDD,EAA2B,YAAYpB,CAAM,CAAA,CAC9C,EAEGoB,EAA2B,SAAS,SAAW,IACjDA,EAA2B,UAAY,qCAEzC,MAAA,CACF,QAEK7E,EAAO,CACN,QAAA,KAAK,uBAAwBA,CAAK,CAAA,CAI5C6E,EAA2B,UAAY,GACnB,KAAK,eAAe,mBAAmB,EAE/C,QAA0BC,GAAA,CAC9B,MAAArB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,4BACnBA,EAAO,YAAcqB,EACdrB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,cAAcqB,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,CAC5C,EACDD,EAA2B,YAAYpB,CAAM,CAAA,CAC9C,EAEGoB,EAA2B,SAAS,SAAW,IACjDA,EAA2B,UAAY,oCACzC,CAMM,cAAqB,CACrB,MAAAJ,EAAyB,SAAS,eAAe,eAAe,EAChEI,EAA6B,SAAS,eAAe,mBAAmB,EACxEL,EAAmB,SAAS,eAAe,WAAW,EAExDC,IACFA,EAAuB,UAAY,qCAEjCI,IACFA,EAA2B,UAAY,qCAErCL,IACFA,EAAiB,MAAM,QAAU,OACnC,CAMM,kBAAyB,CAC1B,KAAK,gBAGN,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,MAInB,KAAA,cAAc,UAAU,IAAI,MAAM,EACvC,KAAK,aAAa,QAAQ,EAGrB,KAAA,eAAiB,OAAO,WAAW,IAAM,CAC5C,KAAK,iBAAiB,GACrB,GAAI,EAAA,CAMD,kBAAyB,CAC1B,KAAK,gBAGN,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,MAInB,KAAA,cAAc,UAAU,OAAO,MAAM,EAC1C,KAAK,aAAa,QAAQ,EAAA,CAQpB,qBAA4B,CAC7B,KAAK,gBAGV,KAAK,cAAc,iBAAiB,YAAc,GAAkB,CAClE,KAAK,YAAc,GAGb,MAAAO,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EACtDE,EAAa,KAAK,cAAe,sBAAsB,EAGvDC,EAAaD,EAAW,KAAOD,EAAc,KAC7CG,EAAYF,EAAW,IAAMD,EAAc,IAGjD,KAAK,YAAY,EAAI,EAAE,QAAUA,EAAc,KAAOE,EACtD,KAAK,YAAY,EAAI,EAAE,QAAUF,EAAc,IAAMG,EAErD,EAAE,eAAe,CAAA,CAClB,EAGQ,SAAA,iBAAiB,YAAc,GAAkB,CACxD,GAAI,CAAC,KAAK,aAAe,CAAC,KAAK,cAAe,OAExC,MAAAJ,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EAG5D,IAAIK,EAAO,EAAE,QAAUJ,EAAc,KAAO,KAAK,YAAY,EACzDK,EAAO,EAAE,QAAUL,EAAc,IAAM,KAAK,YAAY,EAGtD,MAAAM,EAAc,KAAK,cAAc,YACjCC,EAAe,KAAK,cAAc,aAGjCH,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMJ,EAAc,MAAQM,EAAc,EAAE,CAAC,EACnED,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAML,EAAc,OAASO,EAAe,EAAE,CAAC,EAG5E,KAAK,cAAc,MAAM,KAAO,GAAGH,CAAI,KACvC,KAAK,cAAc,MAAM,IAAM,GAAGC,CAAI,KACjC,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,OAAS,OAC7B,KAAA,cAAc,MAAM,UAAY,OAGhC,KAAA,cAAc,UAAU,IAAI,qBAAqB,CAAA,CACvD,EAGQ,SAAA,iBAAiB,UAAW,IAAM,CACrC,KAAK,cACP,KAAK,YAAc,GACrB,CACD,EAGD,KAAK,cAAc,iBAAiB,aAAe,GAAkB,CACnE,KAAK,YAAc,GAEb,MAAAG,EAAQ,EAAE,QAAQ,CAAC,EAGnBT,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EACtDE,EAAa,KAAK,cAAe,sBAAsB,EAGvDC,EAAaD,EAAW,KAAOD,EAAc,KAC7CG,EAAYF,EAAW,IAAMD,EAAc,IAGjD,KAAK,YAAY,EAAIQ,EAAM,QAAUR,EAAc,KAAOE,EAC1D,KAAK,YAAY,EAAIM,EAAM,QAAUR,EAAc,IAAMG,EAEzD,EAAE,eAAe,CAAA,CAClB,EAEQ,SAAA,iBAAiB,YAAc,GAAkB,CACxD,GAAI,CAAC,KAAK,aAAe,CAAC,KAAK,cAAe,OAExC,MAAAK,EAAQ,EAAE,QAAQ,CAAC,EACnBT,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EAG5D,IAAIK,EAAOI,EAAM,QAAUR,EAAc,KAAO,KAAK,YAAY,EAC7DK,EAAOG,EAAM,QAAUR,EAAc,IAAM,KAAK,YAAY,EAG1D,MAAAM,EAAc,KAAK,cAAc,YACjCC,EAAe,KAAK,cAAc,aAGjCH,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMJ,EAAc,MAAQM,EAAc,EAAE,CAAC,EACnED,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAML,EAAc,OAASO,EAAe,EAAE,CAAC,EAG5E,KAAK,cAAc,MAAM,KAAO,GAAGH,CAAI,KACvC,KAAK,cAAc,MAAM,IAAM,GAAGC,CAAI,KACjC,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,OAAS,OAC7B,KAAA,cAAc,MAAM,UAAY,OAGhC,KAAA,cAAc,UAAU,IAAI,qBAAqB,EAEtD,EAAE,eAAe,CAAA,CAClB,EAEQ,SAAA,iBAAiB,WAAY,IAAM,CACtC,KAAK,cACP,KAAK,YAAc,GACrB,CACD,EAAA,CAMK,kBAAkB3B,EAAwB,CAC5C,GAAC,KAAK,eAGN,MAAK,0BAeT,OAXA,KAAK,gBAAkBA,EAGvB,KAAK,cAAc,UAAU,OAAO,MAAO,SAAU,OAAQ,OAAO,EAC/D,KAAA,cAAc,MAAM,KAAO,GAC3B,KAAA,cAAc,MAAM,IAAM,GAC1B,KAAA,cAAc,MAAM,MAAQ,GAC5B,KAAA,cAAc,MAAM,OAAS,GAC7B,KAAA,cAAc,MAAM,UAAY,GAG7BA,EAAU,CAChB,IAAK,cACE,KAAA,cAAc,MAAM,KAAO,OAC3B,KAAA,cAAc,MAAM,OAAS,OAC7B,KAAA,cAAc,UAAU,IAAI,QAAQ,EACzC,MACF,IAAK,eACE,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,OAAS,OAClC,KAAK,cAAc,UAAU,IAAI,SAAU,OAAO,EAClD,MACF,IAAK,WACE,KAAA,cAAc,MAAM,KAAO,OAC3B,KAAA,cAAc,MAAM,IAAM,OAC1B,KAAA,cAAc,UAAU,IAAI,KAAK,EACtC,MACF,IAAK,YACE,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,IAAM,OAC/B,KAAK,cAAc,UAAU,IAAI,MAAO,OAAO,EAC/C,MACF,IAAK,SACL,QAEO,KAAA,cAAc,MAAM,KAAO,MAC3B,KAAA,cAAc,MAAM,IAAM,MAC1B,KAAA,cAAc,MAAM,UAAY,wBACrC,KAAA,CACJ,CAMM,gBAAuB,CACzB,GAAA,CAEF,MAAM+B,EAAU,KAAK,eACjBA,EAEYA,EAAQ,gBAAgB,GAGpC,OAAO,OAAO,YAAY,CACxB,KAAM,0BACL,GAAG,EAEN,KAAK,aAAa,kBAAkB,GAEpC,KAAK,aAAa,UAAU,EAG9B,KAAK,aAAa,eAAe,QAE5BzF,EAAO,CACN,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,QAAQ,CAAA,CAC5B,CAMM,aAAoB,CACpB,MAAA2B,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAIE,KAAK,cACP,KAAK,YAAY,YAAc,gBAC/B,KAAK,iBAAiB,GAIpB,GAAA,CAEF,MAAM+D,EAAY,yBAGjB/D,EAAc,SAAW,GAGpB,MAAAgE,EAAQ,IAAI,MAAMD,CAAS,EACjCC,EAAM,OAAS,GAGf,MAAMC,EAAkBjE,EAAc,gBAClCiE,GAEF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,MAAMF,CAAS,CAAC,CAAA,CAChD,EAAE,KAAK,IAAM,CACZ,KAAK,aAAa,mBAAmB,EAGrC,WAAW,IAAM,CACX,KAAK,cACP,KAAK,YAAY,YAAc,qBAEhC,GAAI,CAAA,CACR,EAAE,MAAe1F,GAAA,CACR,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,sBAAsB,CAAA,CACzC,EAGK2F,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,UAAU,EACxB,KAAK,cACP,KAAK,YAAY,YAAc,YACjC,CACD,GAED,KAAK,aAAa,WAAW,QAExB3F,EAAO,CACN,QAAA,MAAM,YAAaA,CAAK,EAChC,KAAK,aAAa,kBAAkB,CAAA,CACtC,CASF,MAAa,YAAY6F,EAAiD,CACpE,GAAA,CAEE,GAAA,OAAOA,GAAoB,SAAU,CACnCA,GAAmB,GAAKA,EAAkBC,GACvC,KAAA,eAAe,cAAcD,CAAe,EACjD,KAAK,aAAa,SAASA,EAAkB,CAAC,EAAE,GAE3C,KAAA,aAAa,YAAYA,CAAe,EAAE,EAEjD,MAAA,CAIE,GAAA,OAAOA,GAAoB,SAAU,CAEvC,MAAMnE,EAAkB,MAAM,KAAK,eAAe,sBAAsB,EACpDA,EAAgB,KAAcC,GAAAA,EAAM,OAASkE,CAAe,GAGxE,MAAA,KAAK,kBAAkBA,CAAe,EACvC,KAAA,aAAa,UAAUA,CAAe,EAAE,IAExC,KAAA,aAAa,YAAYA,CAAe,EAAE,EACvC,QAAA,KAAK,4BAA4BA,CAAe,EAAE,EAC1D,QAAQ,IAAI,wBAAyBnE,EAAgB,IAASqE,GAAAA,EAAE,IAAI,CAAC,GAEvE,MAAA,CAIF,KAAK,aAAa,eAAe,OAAOF,CAAe,EAAE,QAClD7F,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,EAC9C,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EACrE,KAAA,aAAa,WAAWG,CAAY,EAAE,CAAA,CAC7C,CAQK,WAAWkE,EAAezC,EAAgB,EAAS,CAC1C,KAAK,eAAe,gBAAgB,GAE3C,KAAA,eAAe,WAAWyC,EAAOzC,CAAK,EAC3C,KAAK,aAAa,SAASyC,CAAK,IAAIzC,EAAQ,CAAC,EAAE,GAE/C,KAAK,aAAa,UAAU,CAC9B,CAOK,eAAeoE,EAA6C,CAC3D,MAAAC,EAAkB,KAAK,eAAe,mBAAmB,EAE3D,GAAA,CACF,IAAInB,EAAgC,KAGhC,GAAA,OAAOkB,GAAyB,SAClC,GAAIA,GAAwB,GAAKA,EAAuBC,EAAgB,OACtEnB,EAAiBmB,EAAgBD,CAAoB,MAChD,CACA,KAAA,aAAa,YAAYA,CAAoB,EAAE,EACpD,MAAA,CAKA,GAAA,OAAOA,GAAyB,SAC9B,GAAAC,EAAgB,SAASD,CAAoB,EAC9BlB,EAAAkB,MACZ,CACA,KAAA,aAAa,YAAYA,CAAoB,EAAE,EAC5C,QAAA,KAAK,4BAA4BA,CAAoB,EAAE,EACvD,QAAA,IAAI,wBAAyBC,CAAe,EACpD,MAAA,CAKAnB,GACG,KAAA,eAAe,cAAcA,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,GAE3C,KAAK,aAAa,eAAe,OAAOkB,CAAoB,EAAE,QAEzDhG,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,EAC9C,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EACrE,KAAA,aAAa,WAAWG,CAAY,EAAE,CAAA,CAC7C,CAMK,sBAA6B,CAClC,KAAK,eAAe,oBAAoB,EACxC,KAAK,aAAa,QAAQ,CAAA,CAQrB,WAAW+F,EAAcC,EAAoB,GAAY,CAC9D,GAAI,KAAK,YAAa,CACpB,KAAK,YAAY,YAAcD,EAGzB,MAAA3C,EAAkB,SAAS,eAAe,iBAAiB,EAC7DA,IACFA,EAAgB,MAAQ2C,EAC1B,CAGF,KAAK,iBAAiB,EAEjBC,GAEC,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,KAE1B,CAMK,YAAmB,CACxB,KAAK,iBAAiB,CAAA,CAOjB,qBAAqBT,EAAyB,CAC7C,MAAA/D,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAGE,GAAA,CAEDA,EAAc,SAAW,GAG1B,MAAMgE,EAAQ,IAAI,MAAM,mBAAmBD,CAAS,EAAE,EACtDC,EAAM,OAAS,GAGf,MAAMC,EAAkBjE,EAAc,gBAClCiE,GACF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,MAAM,mBAAmBF,CAAS,EAAE,CAAC,CAAA,CACrE,EAAE,KAAK,IAAM,CACP,KAAA,aAAa,SAASA,CAAS,EAAE,CAAA,CACvC,EAAE,MAAe1F,GAAA,CACR,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,sBAAsB,CAAA,CACzC,EAEK2F,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,QAAQ,CAAA,CAC3B,GAED,KAAK,aAAa,WAAW,QAExB3F,EAAO,CACN,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,gBAAgB,CAAA,CACpC,CAOF,MAAa,oBAAoBP,EAAmC,CAC5D,MAAAkC,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAGE,GAAA,CACF,KAAK,aAAa,aAAa,EAG/B,MAAMyE,EAAiB,MAAM/E,EAAe,mBAAmB5B,CAAU,EAGxEkC,EAAc,SAAW,GAGpB,MAAA0E,EAAY,IAAI,KAAK,CAACD,CAAc,EAAG,CAAE,KAAM,YAAa,EAC5DE,EAAW,IAAI,gBAAgBD,CAAS,EACxCV,EAAQ,IAAI,MAAMW,CAAQ,EAChCX,EAAM,OAAS,GAGf,MAAMC,EAAkBjE,EAAc,gBAClCiE,GACF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,qBAAqBQ,CAAc,CAAC,CAAA,CACpE,EAAE,KAAK,IAAM,CACZ,KAAK,aAAa,cAAc,CAAA,CACjC,EAAE,MAAepG,GAAA,CACR,QAAA,MAAM,gBAAiBA,CAAK,EACpC,KAAK,aAAa,4BAA4B,CAAA,CAC/C,EAEK2F,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,cAAc,EAEhC,IAAI,gBAAgBW,CAAQ,CAAA,CAC7B,EAGKX,EAAA,iBAAiB,QAAU3F,GAAU,CACjC,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,QAAQ,EAC1B,IAAI,gBAAgBsG,CAAQ,CAAA,CAC7B,GAED,KAAK,aAAa,WAAW,QAExBtG,EAAO,CACN,QAAA,MAAM,gBAAiBA,CAAK,EACpC,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EACrE,KAAA,aAAa,iBAAiBG,CAAY,EAAE,CAAA,CACnD,CAMK,qBAA2B,CAEhC,OADc,KAAK,eAAe,gBAAgB,EAEzC,CACL,UAAW,gBACX,SAAU,GACV,aAAc,KAAK,eAAe,gBAAgB,EAClD,YAAa,KAAK,eAAe,mBAAmB,CACtD,EAEK,CACL,UAAW,WACX,SAAU,GACV,aAAc,CAAC,EACf,YAAa,CAAA,CACf,CAAA,CAQM,oBAA2B,CAC3B,MAAAoG,EAAe,SAAS,cAAc,gBAAgB,EACxDA,IACWA,EAAA,UAAU,OAAO,QAAQ,EACtC,QAAQ,IAAI,aAAa,EAC3B,CAMM,aAAaC,EAAuB,CACpC,MAAAC,EAAgB,SAAS,eAAe,YAAY,EACtDA,IACFA,EAAc,YAAcD,GAItBE,EAAA,aAAa,mBAAmBF,CAAO,EAAE,CAAA,CAM3C,uBAA8B,CAC9B,MAAAG,EAAY,SAAS,eAAe,gBAAgB,EACpDC,EAAgB,SAAS,eAAe,eAAe,EACvDC,EAAY,SAAS,eAAe,WAAW,EAC/CC,EAAmB,SAAS,eAAe,kBAAkB,EAGrDF,EAAA,iBAAiB,QAAS,IAAM,CAC5CD,EAAU,MAAM,CAAA,CACjB,EAGSA,EAAA,iBAAiB,SAAW7E,GAAU,OAC9C,MAAMC,EAASD,EAAM,OACfmB,GAAOC,EAAAnB,EAAO,QAAP,YAAAmB,EAAe,GAExBD,IACEA,EAAK,OAAS,mBAAqBA,EAAK,KAAK,SAAS,MAAM,GAC7C6D,EAAA,YAAc,QAAQ7D,EAAK,IAAI,GAChD6D,EAAiB,MAAM,QAAU,QACjCD,EAAU,MAAM,QAAU,QAC1B,KAAK,aAAa,UAAU5D,EAAK,IAAI,EAAE,IAEvC,MAAM,aAAa,EACnBlB,EAAO,MAAQ,IAEnB,CACD,EAGS8E,EAAA,iBAAiB,QAAS,IAAM,OAClC,MAAA5D,GAAOC,EAAAyD,EAAU,QAAV,YAAAzD,EAAkB,GAC3BD,GACF,KAAK,YAAYA,CAAI,CACvB,CACD,CAAA,CAMH,MAAa,YAAYA,EAA2B,CAC5C,MAAA8D,EAAe,SAAS,eAAe,cAAc,EACrDC,EAAmB,SAAS,eAAe,kBAAkB,EAC7DC,EAAc,SAAS,eAAe,aAAa,EACnDJ,EAAY,SAAS,eAAe,WAAW,EAEjD,GAAA,CAEFE,EAAa,MAAM,QAAU,QAC7BA,EAAa,UAAY,gBACzBC,EAAiB,YAAc,UAC/BC,EAAY,MAAM,MAAQ,KAC1BJ,EAAU,SAAW,GAGf,MAAAK,EAAW,IAAI,SACZA,EAAA,OAAO,WAAYjE,CAAI,EAG1B,MAAAkE,EAAW,MAAM,MAAM,yCAA0C,CACrE,OAAQ,OACR,KAAMD,CAAA,CACP,EAED,GAAIC,EAAS,GAAI,CACT,MAAAC,EAAS,MAAMD,EAAS,KAAK,EAQnC,GALAJ,EAAa,UAAY,wBACRC,EAAA,YAAc,YAAYI,EAAO,SAAS,OAC3DH,EAAY,MAAM,MAAQ,OAGtBG,EAAO,SAAU,CACnB,MAAMC,EAAyB,CAAC,EAC5BD,EAAO,SAAS,gBAClBC,EAAa,KAAK,OAAOD,EAAO,SAAS,YAAY,MAAM,GAAG,EAE5DA,EAAO,SAAS,YAClBC,EAAa,KAAK,QAAQD,EAAO,SAAS,aAAa,MAAM,GAAG,EAE9DA,EAAO,SAAS,QAAUA,EAAO,SAAS,OAAO,OAAS,GAC5DC,EAAa,KAAK,OAAOD,EAAO,SAAS,OAAO,MAAM,GAAG,EAGvDC,EAAa,OAAS,IACxBL,EAAiB,aAAe,KAAKK,EAAa,KAAK,IAAI,CAAC,IAC9D,CAIF,MAAM,KAAK,iBAAiB,EAG5B,KAAK,mBAAmB,EAGnB,KAAA,2BAA2BD,EAAO,SAAS,EAEhD,KAAK,aAAa,MAAMA,EAAO,SAAS,OAAO,CAAA,KAC1C,CACC,MAAApH,EAAQ,MAAMmH,EAAS,KAAK,EAClC,MAAM,IAAI,MAAMnH,EAAM,OAAS,MAAM,CAAA,QAEhCA,EAAO,CAEd,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAC9D+G,EAAa,UAAY,sBACRC,EAAA,YAAc,SAAS7G,CAAY,GAC/C,KAAA,aAAa,SAASA,CAAY,EAAE,CAAA,QACzC,CACA0G,EAAU,SAAW,EAAA,CACvB,CAMF,MAAa,kBAAkC,CACzC,GAAA,CAEF,MAAM,KAAK,wBAAwB,EACnC,KAAK,aAAa,SAAS,QACpB7G,EAAO,CACN,QAAA,MAAM,YAAaA,CAAK,EAChC,KAAK,aAAa,UAAU,CAAA,CAC9B,CAMM,oBAA2B,CAC3B,MAAA2G,EAAY,SAAS,eAAe,gBAAgB,EACpDG,EAAmB,SAAS,eAAe,kBAAkB,EAC7DD,EAAY,SAAS,eAAe,WAAW,EAErDF,EAAU,MAAQ,GAClBG,EAAiB,MAAM,QAAU,OACjCD,EAAU,MAAM,QAAU,MAAA,CAM5B,MAAa,YAAY3E,EAAkC,CACzD,GAAK,QAAQ,YAAYA,CAAS,cAAc,EAI5C,GAAA,CACG,KAAA,aAAa,WAAWA,CAAS,KAAK,EAE3C,MAAMiF,EAAW,MAAM,MAAM,oCAAoCjF,CAAS,GAAI,CAC5E,OAAQ,QAAA,CACT,EAED,GAAIiF,EAAS,GAAI,CACf,MAAMA,EAAS,KAAK,EACf,KAAA,aAAa,MAAMjF,CAAS,OAAO,EAGxC,MAAM,KAAK,iBAAiB,EAGtB,MAAAT,EAAiB,SAAS,eAAe,gBAAgB,EAC/DA,EAAe,MAAM,QAAU,OAG/B,KAAK,0BAA0BS,CAAS,CAAA,KAEnC,CACC,MAAAlC,EAAQ,MAAMmH,EAAS,KAAK,EAClC,MAAM,IAAI,MAAMnH,EAAM,OAAS,MAAM,CAAA,QAEhCA,EAAO,CACd,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OACzD,KAAA,aAAa,SAASG,CAAY,EAAE,EACnC,MAAA,WAAWA,CAAY,EAAE,CAAA,CACjC,CAMM,2BAA2B+B,EAAyB,CACtD,GAAA,CAEE,OAAO,QAAU,OAAO,SAAW,QACrC,OAAO,OAAO,YAAY,CACxB,KAAM,wBACN,UAAAA,GACC,GAAG,EAIH,OAAe,uBAChB,OAAQ,OAAe,sBAAsB,qBAAwB,YACtE,OAAe,sBAAsB,oBAAoB,QAErDlC,EAAO,CACN,QAAA,KAAK,eAAgBA,CAAK,CAAA,CACpC,CAMM,0BAA0BkC,EAAyB,CACrD,GAAA,CAEE,OAAO,QAAU,OAAO,SAAW,QACrC,OAAO,OAAO,YAAY,CACxB,KAAM,uBACN,UAAAA,GACC,GAAG,EAIH,OAAe,uBAChB,OAAQ,OAAe,sBAAsB,qBAAwB,YACtE,OAAe,sBAAsB,oBAAoB,QAErDlC,EAAO,CACN,QAAA,KAAK,eAAgBA,CAAK,CAAA,CACpC,CAMK,qBAA4B,CAC5B,KAAA,eAAiB,CAAC,KAAK,eAEtB,MAAAsH,EAA2B,SAAS,eAAe,0BAA0B,EAE/E,KAAK,gBAEmBA,GAAA,MAAAA,EAAA,MAAM,YAAY,UAAW,SACvD,KAAK,8BAA8B,EACnC,KAAK,aAAa,oBAAoB,IAGZA,GAAA,MAAAA,EAAA,MAAM,YAAY,UAAW,QACvD,KAAK,wBAAwB,EAC7B,KAAK,+BAA+B,EACpC,KAAK,aAAa,wBAAwB,EAC5C,CAMM,+BAAsC,CACxC,GAAA,CAAC,KAAK,eAAgB,OAG1B,MAAMC,EAAO,SAAS,KACMA,EAAK,MAAM,iBAAmB,SAAS,eAAe,yBAAyB,EAOzG,KAAK,wBAAwB,EAH7BA,EAAK,MAAM,gBAAkB,aAI/B,CAMM,gCAAuC,CAE7C,MAAMA,EAAO,SAAS,KACtBA,EAAK,MAAM,gBAAkB,cAG7B,KAAK,wBAAwB,CAAA,CAMvB,yBAAgC,CAWlC,GATJ,KAAK,wBAAwB,EAG7B,KAAK,wBAAwB,EAMzB,KAAK,qBAAuB,EAE9B,OAGE,GAAA,KAAK,qBAAuB,IAAK,CAG7BC,MAAAA,EAAU,SAAS,cAAc,KAAK,EAC5CA,EAAQ,GAAK,4BACbA,EAAQ,MAAM,SAAW,QACzBA,EAAQ,MAAM,IAAM,IACpBA,EAAQ,MAAM,KAAO,IACrBA,EAAQ,MAAM,MAAQ,QACtBA,EAAQ,MAAM,OAAS,QACvBA,EAAQ,MAAM,cAAgB,OAC9BA,EAAQ,MAAM,OAAS,KACvBA,EAAQ,MAAM,gBAAkB,cAGvB,SAAA,KAAK,MAAM,gBAAkB,cAGtC,MAAMD,EAAO,SAAS,KAChBE,EAAkBF,EAAK,MAAM,gBAC7BG,EAAkB,SAAS,eAAe,yBAAyB,EAErED,IACFF,EAAK,MAAM,gBAAkB,GACxBA,EAAA,aAAa,yBAA0BE,CAAe,GAEzDC,IACFA,EAAgB,MAAM,QAAU,QAGlC,MAAA,CAII,MAAAF,EAAU,SAAS,cAAc,KAAK,EAC5CA,EAAQ,GAAK,4BACbA,EAAQ,MAAM,SAAW,QACzBA,EAAQ,MAAM,IAAM,IACpBA,EAAQ,MAAM,KAAO,IACrBA,EAAQ,MAAM,MAAQ,QACtBA,EAAQ,MAAM,OAAS,QACvBA,EAAQ,MAAM,cAAgB,OAC9BA,EAAQ,MAAM,OAAS,KAIjB,MAAAG,EAAU,KAAK,mBAAqB,IAClCH,EAAA,MAAM,gBAAkB,uBAAuBG,CAAO,IAErD,SAAA,KAAK,YAAYH,CAAO,CAAA,CAM3B,yBAAgC,CACtC,MAAMD,EAAO,SAAS,KAChBK,EAAmBL,EAAK,aAAa,wBAAwB,EAC7DG,EAAkB,SAAS,eAAe,yBAAyB,EAErEE,IACFL,EAAK,MAAM,gBAAkBK,EAC7BL,EAAK,gBAAgB,wBAAwB,GAG3CG,IACFA,EAAgB,MAAM,QAAU,QAClC,CAMM,yBAAgC,CAChC,MAAAF,EAAU,SAAS,eAAe,2BAA2B,EAC/DA,GACFA,EAAQ,OAAO,CACjB,CAMM,yBAAgC,CAClC,KAAK,gBACP,KAAK,8BAA8B,CACrC,CAMK,qBAAqBG,EAAuB,CAC7C,GAAAA,EAAU,GAAKA,EAAU,IAAK,CAChC,QAAQ,KAAK,eAAe,EAC5B,MAAA,CAGF,KAAK,mBAAqBA,EAGpB,MAAAhF,EAA0B,SAAS,eAAe,yBAAyB,EAC3EC,EAAe,SAAS,eAAe,cAAc,EAEvDD,IACsBA,EAAA,MAAQgF,EAAQ,SAAS,GAE/C/E,IACWA,EAAA,YAAc,GAAG+E,CAAO,KAInC,KAAK,gBACP,KAAK,wBAAwB,CAC/B,CAMM,2BAA2B1E,EAAkB,CACnD,KAAK,uBAAyBA,EAExB,MAAA4E,EAAqB,SAAS,eAAe,oBAAoB,EACjE1E,EAAqB,SAAS,eAAe,oBAAoB,EAEnE0E,IACiBA,EAAA,YAAc,QAAQ5E,EAAK,IAAI,GAClD4E,EAAmB,MAAM,QAAU,SAGjC1E,IACFA,EAAmB,MAAM,QAAU,gBAGrC,KAAK,aAAa,YAAYF,EAAK,IAAI,EAAE,CAAA,CAMnC,iBAAwB,CAC1B,GAAA,CAAC,KAAK,uBAAwB,CAChC,KAAK,aAAa,UAAU,EAC5B,MAAA,CAIE,KAAK,uBACH,IAAA,gBAAgB,KAAK,qBAAqB,EAIhD,KAAK,sBAAwB,IAAI,gBAAgB,KAAK,sBAAsB,EAG5E,KAAK,qBAAqB,KAAK,sBAAuB,KAAK,uBAAuB,IAAI,EAEtF,KAAK,aAAa,UAAU,KAAK,uBAAuB,IAAI,EAAE,EACzD,KAAA,qBAAqB,UAAW,QAAQ,CAAA,CAMxC,qBAAqB6E,EAAaC,EAAqB,CAE5D,KAAK,wBAAwB,EAG7B,MAAMR,EAAO,SAAS,KAEtB,GAAIQ,GAAQA,EAAK,WAAW,QAAQ,EAAG,CAE/B,MAAAC,EAAQ,SAAS,cAAc,OAAO,EAC5CA,EAAM,IAAMF,EACZE,EAAM,SAAW,GACjBA,EAAM,KAAO,GACbA,EAAM,MAAQ,GACdA,EAAM,MAAM,SAAW,QACvBA,EAAM,MAAM,IAAM,IAClBA,EAAM,MAAM,KAAO,IACnBA,EAAM,MAAM,MAAQ,QACpBA,EAAM,MAAM,OAAS,QACrBA,EAAM,MAAM,UAAY,QACxBA,EAAM,MAAM,OAAS,MACrBA,EAAM,MAAM,cAAgB,OAC5BA,EAAM,GAAK,0BAEXT,EAAK,YAAYS,CAAK,EACtB,KAAK,aAAa,kBAAkB,CAAA,MAG/BT,EAAA,MAAM,gBAAkB,OAAOO,CAAG,IACvCP,EAAK,MAAM,eAAiB,QAC5BA,EAAK,MAAM,mBAAqB,SAChCA,EAAK,MAAM,iBAAmB,YAC9BA,EAAK,MAAM,qBAAuB,QAClC,KAAK,aAAa,kBAAkB,CACtC,CAMK,iBAAwB,CAC7B,KAAK,wBAAwB,EAGvB,MAAAM,EAAqB,SAAS,eAAe,oBAAoB,EACjE1E,EAAqB,SAAS,eAAe,oBAAoB,EACjEH,EAAsB,SAAS,eAAe,qBAAqB,EAErE6E,IACFA,EAAmB,MAAM,QAAU,QAEjC1E,IACFA,EAAmB,MAAM,QAAU,QAEjCH,IACFA,EAAoB,MAAQ,IAI1B,KAAK,wBACH,IAAA,gBAAgB,KAAK,qBAAqB,EAC9C,KAAK,sBAAwB,MAG/B,KAAK,uBAAyB,KAC9B,KAAK,aAAa,OAAO,EACpB,KAAA,qBAAqB,UAAW,OAAO,CAAA,CAMtC,yBAAgC,CACtC,MAAMuE,EAAO,SAAS,KAGtBA,EAAK,MAAM,gBAAkB,GAC7BA,EAAK,MAAM,eAAiB,GAC5BA,EAAK,MAAM,mBAAqB,GAChCA,EAAK,MAAM,iBAAmB,GAC9BA,EAAK,MAAM,qBAAuB,GAGlCA,EAAK,gBAAgB,wBAAwB,EAGvC,MAAAG,EAAkB,SAAS,eAAe,yBAAyB,EACrEA,GACFA,EAAgB,OAAO,EAIzB,KAAK,wBAAwB,EAGzB,KAAK,eACP,KAAK,8BAA8B,EAEnCH,EAAK,MAAM,gBAAkB,aAC/B,CAMM,qBAAqBQ,EAA2BvB,EAAuB,CACvE,MAAAyB,EAAmB,SAAS,eAAe,kBAAkB,EAC7DC,EAAuB,SAAS,eAAe,sBAAsB,EAEvED,GAAoBC,IACLD,EAAA,UAAY,iBAAiBF,CAAI,GAClDG,EAAqB,YAAc1B,EACnCyB,EAAiB,MAAM,QAAU,QAGjC,WAAW,IAAM,CACfA,EAAiB,MAAM,QAAU,QAChC,GAAI,EACT,CAMK,SAAgB,CAEjB,KAAK,uBACH,IAAA,gBAAgB,KAAK,qBAAqB,EAEhD1G,EAAa,gBAAgB,CAAA,CAEjC,CAGA,IAAI4G,EAKJ,OAAO,iBAAiB,mBAAoB,SAAY,CACtDA,EAAM,IAAI7G,EACV,MAAM6G,EAAI,WAAW,EAGpB,OAAe,mBAAqB,CAEnC,YAActC,GAAqCsC,EAAI,YAAYtC,CAAe,EAGlF,WAAY,CAACxB,EAAezC,EAAgB,IAAMuG,EAAI,WAAW9D,EAAOzC,CAAK,EAG7E,eAAiBoE,GAA0CmC,EAAI,eAAenC,CAAoB,EAClG,qBAAsB,IAAMmC,EAAI,qBAAqB,EAGrD,WAAY,CAACjC,EAAcC,EAAoB,KAASgC,EAAI,WAAWjC,EAAMC,CAAQ,EACrF,WAAY,IAAMgC,EAAI,WAAW,EAGjC,UAAYzC,GAAsByC,EAAI,qBAAqBzC,CAAS,EACpE,oBAAsBjG,GAAuB0I,EAAI,oBAAoB1I,CAAU,EAG/E,YAAcwD,GAAekF,EAAI,YAAYlF,CAAI,EACjD,iBAAkB,IAAMkF,EAAI,iBAAiB,EAC7C,YAAcjG,GAAsBiG,EAAI,YAAYjG,CAAS,EAG7D,aAAc,IAAMiG,EAAI,oBAAoB,EAG5C,oBAAqB,IAAMA,EAAI,oBAAoB,EACnD,qBAAuBR,GAAoBQ,EAAI,qBAAqBR,CAAO,EAG3E,qBAAsB,CAACG,EAAaC,IAAkBI,EAAI,qBAAqBL,EAAKC,CAAI,EACxF,gBAAiB,IAAMI,EAAI,gBAAgB,EAG3C,KAAMA,CACR,EAGO,OAAA,iBAAiB,UAAYrG,GAAU,WAE5C,GAAIA,EAAM,MAAQA,EAAM,KAAK,OAAS,eAAgB,CAC9C,MAAAyE,EAAe,SAAS,cAAc,gBAAgB,EACxDA,IACWA,EAAA,UAAU,OAAO,QAAQ,EACtC,QAAQ,IAAI,aAAa,GAE3B,MAAA,CAIF,GAAIzE,EAAM,MAAQA,EAAM,KAAK,OAAS,kBAAmB,CACvD,KAAM,CAAE,UAAAsG,EAAW,OAAAC,EAAQ,KAAAC,GAASxG,EAAM,KAEtC,GAAA,CAEF,MAAMyG,EAAO,OAAe,mBAC5B,GAAIA,GAAO,OAAOA,EAAIF,CAAM,GAAM,WAAY,CAE5C,MAAMjB,EAASmB,EAAIF,CAAM,EAAE,GAAGC,CAAI,EAG9BlB,GAAU,OAAOA,EAAO,MAAS,WAC5BA,EAAA,KAAMoB,GAAa,QAEvBtF,EAAApB,EAAM,SAAN,MAAAoB,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAkF,EACA,QAAS,GACT,OAAQI,GACP,IAAG,CACP,EAAE,MAAOxI,GAAe,QAEtBkD,EAAApB,EAAM,SAAN,MAAAoB,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAkF,EACA,QAAS,GACT,MAAOpI,EAAM,SAAW,QACvB,IAAG,CACP,GAGAkD,EAAApB,EAAM,SAAN,MAAAoB,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAkF,EACA,QAAS,GACT,OAAAhB,GACC,IACL,MAGCqB,EAAA3G,EAAM,SAAN,MAAA2G,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAL,EACA,QAAS,GACT,MAAO,MAAMC,CAAM,QAClB,WAEErI,EAAY,EAElB0I,EAAA5G,EAAM,SAAN,MAAA4G,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAN,EACA,QAAS,GACT,MAAOpI,EAAM,SAAW,QACvB,IAAG,CACR,CACF,CACD,CACH,CAAC,EAKD,OAAO,iBAAiB,eAAgB,IAAM,CACxCmI,GACFA,EAAI,QAAQ,CAEhB,CAAC"}