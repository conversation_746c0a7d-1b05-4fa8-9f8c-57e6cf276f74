{"version": 3, "file": "control-panel-CZSjFbo9.js", "sources": ["../../src/audio-converter.ts", "../../src/control-panel.ts"], "sourcesContent": ["/**\n * 音频格式检测和转换工具类\n * 支持MP3和WAV格式的检测，以及MP3到WAV的转换\n */\nexport class AudioConverter {\n  private static audioContext: AudioContext | null = null;\n\n  /**\n   * 获取或创建AudioContext实例\n   */\n  private static getAudioContext(): AudioContext {\n    if (!this.audioContext) {\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    }\n    return this.audioContext;\n  }\n\n  /**\n   * 检测base64音频数据的格式\n   * @param base64Data base64编码的音频数据\n   * @returns 'mp3' | 'wav' | 'unknown'\n   */\n  static detectAudioFormat(base64Data: string): 'mp3' | 'wav' | 'unknown' {\n    try {\n      // 移除data URL前缀（如果存在）\n      const cleanBase64 = base64Data.replace(/^data:audio\\/[^;]+;base64,/, '');\n      \n      // 解码base64获取前几个字节\n      const binaryString = atob(cleanBase64);\n      const bytes = new Uint8Array(Math.min(12, binaryString.length));\n      for (let i = 0; i < bytes.length; i++) {\n        bytes[i] = binaryString.charCodeAt(i);\n      }\n\n      // 检测WAV格式 (RIFF...WAVE)\n      if (bytes.length >= 12) {\n        const riff = String.fromCharCode(bytes[0], bytes[1], bytes[2], bytes[3]);\n        const wave = String.fromCharCode(bytes[8], bytes[9], bytes[10], bytes[11]);\n        if (riff === 'RIFF' && wave === 'WAVE') {\n          return 'wav';\n        }\n      }\n\n      // 检测MP3格式\n      if (bytes.length >= 3) {\n        // ID3v2标签\n        if (bytes[0] === 0x49 && bytes[1] === 0x44 && bytes[2] === 0x33) {\n          return 'mp3';\n        }\n        \n        // MP3帧头 (11111111 111xxxxx)\n        if (bytes[0] === 0xFF && (bytes[1] & 0xE0) === 0xE0) {\n          return 'mp3';\n        }\n      }\n\n      return 'unknown';\n    } catch (error) {\n      console.error('检测音频格式失败:', error);\n      return 'unknown';\n    }\n  }\n\n  /**\n   * 将base64音频数据转换为ArrayBuffer\n   * @param base64Data base64编码的音频数据\n   * @returns ArrayBuffer\n   */\n  static base64ToArrayBuffer(base64Data: string): ArrayBuffer {\n    // 移除data URL前缀（如果存在）\n    const cleanBase64 = base64Data.replace(/^data:audio\\/[^;]+;base64,/, '');\n    \n    const binaryString = atob(cleanBase64);\n    const bytes = new Uint8Array(binaryString.length);\n    for (let i = 0; i < binaryString.length; i++) {\n      bytes[i] = binaryString.charCodeAt(i);\n    }\n    return bytes.buffer;\n  }\n\n  /**\n   * 使用Web Audio API将MP3转换为WAV格式的ArrayBuffer\n   * @param mp3ArrayBuffer MP3格式的ArrayBuffer\n   * @returns Promise<ArrayBuffer> WAV格式的ArrayBuffer\n   */\n  static async convertMp3ToWav(mp3ArrayBuffer: ArrayBuffer): Promise<ArrayBuffer> {\n    try {\n      const audioContext = this.getAudioContext();\n\n      // 使用Web Audio API解码MP3\n      const audioBuffer = await audioContext.decodeAudioData(mp3ArrayBuffer.slice(0));\n\n      // 将AudioBuffer转换为WAV格式\n      return this.audioBufferToWav(audioBuffer);\n    } catch (error) {\n      console.error('MP3转WAV失败:', error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      throw new Error(`MP3转WAV失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 将AudioBuffer转换为WAV格式的ArrayBuffer\n   * @param audioBuffer Web Audio API的AudioBuffer\n   * @returns ArrayBuffer WAV格式的数据\n   */\n  static audioBufferToWav(audioBuffer: AudioBuffer): ArrayBuffer {\n    const numberOfChannels = audioBuffer.numberOfChannels;\n    const sampleRate = audioBuffer.sampleRate;\n    const length = audioBuffer.length;\n    const bitsPerSample = 16;\n    const bytesPerSample = bitsPerSample / 8;\n    \n    // 计算WAV文件大小\n    const dataSize = length * numberOfChannels * bytesPerSample;\n    const fileSize = 44 + dataSize; // WAV头部44字节 + 数据\n    \n    // 创建ArrayBuffer\n    const arrayBuffer = new ArrayBuffer(fileSize);\n    const view = new DataView(arrayBuffer);\n    \n    // 写入WAV头部\n    let offset = 0;\n    \n    // RIFF标识符\n    this.writeString(view, offset, 'RIFF'); offset += 4;\n    // 文件大小-8\n    view.setUint32(offset, fileSize - 8, true); offset += 4;\n    // WAVE标识符\n    this.writeString(view, offset, 'WAVE'); offset += 4;\n    \n    // fmt子块\n    this.writeString(view, offset, 'fmt '); offset += 4;\n    // fmt子块大小\n    view.setUint32(offset, 16, true); offset += 4;\n    // 音频格式 (PCM = 1)\n    view.setUint16(offset, 1, true); offset += 2;\n    // 声道数\n    view.setUint16(offset, numberOfChannels, true); offset += 2;\n    // 采样率\n    view.setUint32(offset, sampleRate, true); offset += 4;\n    // 字节率\n    view.setUint32(offset, sampleRate * numberOfChannels * bytesPerSample, true); offset += 4;\n    // 块对齐\n    view.setUint16(offset, numberOfChannels * bytesPerSample, true); offset += 2;\n    // 位深度\n    view.setUint16(offset, bitsPerSample, true); offset += 2;\n    \n    // data子块\n    this.writeString(view, offset, 'data'); offset += 4;\n    // data子块大小\n    view.setUint32(offset, dataSize, true); offset += 4;\n    \n    // 写入音频数据\n    for (let channel = 0; channel < numberOfChannels; channel++) {\n      const channelData = audioBuffer.getChannelData(channel);\n      let dataOffset = 44 + channel * bytesPerSample;\n      \n      for (let i = 0; i < length; i++) {\n        // 将浮点数转换为16位整数\n        const sample = Math.max(-1, Math.min(1, channelData[i]));\n        const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;\n        \n        view.setInt16(dataOffset, intSample, true);\n        dataOffset += numberOfChannels * bytesPerSample;\n      }\n    }\n    \n    return arrayBuffer;\n  }\n\n  /**\n   * 在DataView中写入字符串\n   * @param view DataView实例\n   * @param offset 偏移量\n   * @param string 要写入的字符串\n   */\n  private static writeString(view: DataView, offset: number, string: string): void {\n    for (let i = 0; i < string.length; i++) {\n      view.setUint8(offset + i, string.charCodeAt(i));\n    }\n  }\n\n  /**\n   * 处理base64音频数据，自动检测格式并转换为WAV\n   * @param base64Data base64编码的音频数据\n   * @returns Promise<ArrayBuffer> WAV格式的ArrayBuffer\n   */\n  static async processBase64Audio(base64Data: string): Promise<ArrayBuffer> {\n    const format = this.detectAudioFormat(base64Data);\n    const arrayBuffer = this.base64ToArrayBuffer(base64Data);\n    \n    switch (format) {\n      case 'wav':\n        console.log('检测到WAV格式，直接使用');\n        return arrayBuffer;\n        \n      case 'mp3':\n        console.log('检测到MP3格式，转换为WAV');\n        return await this.convertMp3ToWav(arrayBuffer);\n        \n      default:\n        console.warn('未知音频格式，尝试作为MP3处理');\n        try {\n          return await this.convertMp3ToWav(arrayBuffer);\n        } catch (error) {\n          console.error('作为MP3处理失败，尝试作为WAV处理');\n          return arrayBuffer;\n        }\n    }\n  }\n}\n", "/**\n * Copyright(c) Live2D Inc. All rights reserved.\n *\n * Use of this source code is governed by the Live2D Open Software license\n * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.\n */\n\nimport { LAppDelegate } from './lappdelegate';\nimport { LAppLive2DManager } from './lapplive2dmanager';\nimport { LAppPal } from './lapppal';\nimport * as LAppDefine from './lappdefine';\nimport { AudioConverter } from './audio-converter';\n\n/**\n * 控制面板应用程序类\n * 管理Live2D模型的控制界面\n */\nclass ControlPanelApp {\n  private _delegate: LAppDelegate;\n  private _live2DManager: LAppLive2DManager;\n  private _currentModelIndex: number = 0;\n  private _speechBubble: HTMLElement | null = null;\n  private _bubbleText: HTMLElement | null = null;\n  private _bubblePosition: string = 'center';\n  private _bubbleTimeout: number | null = null;\n  private _isDragging: boolean = false;\n  private _dragOffset: { x: number; y: number } = { x: 0, y: 0 };\n  private _bubbleManuallyPositioned: boolean = false;\n\n  /**\n   * 初始化应用程序\n   */\n  public async initialize(): Promise<void> {\n    // 获取应用程序委托实例\n    this._delegate = LAppDelegate.getInstance();\n    this._delegate.initialize();\n\n    // 获取Live2D管理器\n    this._live2DManager = this._delegate.getLive2DManager();\n\n    // 初始化UI\n    await this.initializeUI();\n\n    // 开始渲染循环\n    this._delegate.run();\n  }\n\n  /**\n   * 初始化用户界面\n   */\n  private async initializeUI(): Promise<void> {\n    // 初始化模型选择器\n    await this.initializeModelSelector();\n\n    // 初始化控制按钮\n    this.initializeControlButtons();\n\n    // 初始化文字气泡\n    this.initializeSpeechBubble();\n\n    // 初始化模型上传功能\n    this.initializeModelUpload();\n\n    // 监听模型加载完成事件\n    this.setupModelLoadListener();\n  }\n\n  /**\n   * 初始化模型选择器\n   */\n  private async initializeModelSelector(): Promise<void> {\n    const modelSelector = document.getElementById('modelSelector') as HTMLSelectElement;\n    const deleteModelBtn = document.getElementById('deleteModelBtn') as HTMLButtonElement;\n\n    // 动态检测可用模型\n    const availableModels = await this._live2DManager.detectAvailableModels();\n\n    // 清空现有选项\n    modelSelector.innerHTML = '<option value=\"\">选择模型...</option>';\n\n    // 添加模型选项\n    availableModels.forEach((model, index) => {\n      const option = document.createElement('option');\n      option.value = index.toString();\n      option.textContent = `${model.name} ${model.status === 'active' ? '✓' : '⚠️'}`;\n      option.setAttribute('data-model-name', model.name);\n      option.setAttribute('data-has-expressions', model.hasExpressions.toString());\n      option.setAttribute('data-has-motions', model.hasMotions.toString());\n      modelSelector.appendChild(option);\n    });\n\n    // 监听选择变化\n    modelSelector.addEventListener('change', async (event) => {\n      const target = event.target as HTMLSelectElement;\n      const selectedIndex = parseInt(target.value);\n\n      if (!isNaN(selectedIndex) && availableModels[selectedIndex]) {\n        const selectedModel = availableModels[selectedIndex];\n        await this.switchModelByName(selectedModel.name);\n        deleteModelBtn.style.display = 'block';\n        deleteModelBtn.setAttribute('data-model-name', selectedModel.name);\n\n        // 显示模型信息\n        this.displayModelInfo(selectedModel);\n      } else {\n        deleteModelBtn.style.display = 'none';\n        this.hideModelInfo();\n      }\n    });\n\n    // 删除模型按钮事件\n    deleteModelBtn.addEventListener('click', () => {\n      const modelName = deleteModelBtn.getAttribute('data-model-name');\n      if (modelName) {\n        this.deleteModel(modelName);\n      }\n    });\n\n    // 默认选择第一个模型\n    if (availableModels.length > 0) {\n      modelSelector.value = '0';\n      await this.switchModelByName(availableModels[0].name);\n      deleteModelBtn.style.display = 'block';\n      deleteModelBtn.setAttribute('data-model-name', availableModels[0].name);\n\n      // 显示模型信息\n      this.displayModelInfo(availableModels[0]);\n    }\n  }\n\n  /**\n   * 初始化控制按钮\n   */\n  private initializeControlButtons(): void {\n    // 面板切换按钮\n    const panelToggleBtn = document.getElementById('panelToggleBtn');\n    panelToggleBtn?.addEventListener('click', () => {\n      this.toggleControlPanel();\n    });\n\n    // 随机动作按钮\n    const randomMotionBtn = document.getElementById('randomMotionBtn');\n    randomMotionBtn?.addEventListener('click', () => {\n      this._live2DManager.playRandomMotion(LAppDefine.MotionGroupIdle);\n      this.updateStatus('播放随机动作');\n    });\n\n    // 停止动作按钮\n    const stopMotionBtn = document.getElementById('stopMotionBtn');\n    stopMotionBtn?.addEventListener('click', () => {\n      this._live2DManager.stopAllMotions();\n      this.updateStatus('停止所有动作');\n    });\n\n    // 随机表情按钮\n    const randomExpressionBtn = document.getElementById('randomExpressionBtn');\n    randomExpressionBtn?.addEventListener('click', () => {\n      this._live2DManager.setRandomExpression();\n      this.updateStatus('设置随机表情');\n    });\n\n    // 重置表情按钮\n    const resetExpressionBtn = document.getElementById('resetExpressionBtn');\n    resetExpressionBtn?.addEventListener('click', () => {\n      // 通过设置空表情来重置\n      const model = this._live2DManager.getCurrentModel();\n      if (model) {\n        model.getExpressionManager().stopAllMotions();\n        this.updateStatus('重置表情');\n      }\n    });\n\n    // 测试口型同步按钮\n    const testLipSyncBtn = document.getElementById('testLipSyncBtn');\n    testLipSyncBtn?.addEventListener('click', () => {\n      this.testLipSync();\n    });\n  }\n\n  /**\n   * 初始化文字气泡\n   */\n  private initializeSpeechBubble(): void {\n    // 获取气泡元素\n    this._speechBubble = document.getElementById('speechBubble');\n    this._bubbleText = document.getElementById('bubbleText');\n\n    // 显示气泡按钮\n    const showBubbleBtn = document.getElementById('showBubbleBtn');\n    showBubbleBtn?.addEventListener('click', () => {\n      this.showSpeechBubble();\n    });\n\n    // 隐藏气泡按钮\n    const hideBubbleBtn = document.getElementById('hideBubbleBtn');\n    hideBubbleBtn?.addEventListener('click', () => {\n      this.hideSpeechBubble();\n    });\n\n    // 文字输入框\n    const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n    bubbleTextInput?.addEventListener('input', (event) => {\n      const target = event.target as HTMLTextAreaElement;\n      if (this._bubbleText) {\n        this._bubbleText.textContent = target.value;\n      }\n    });\n\n    // 位置控制按钮\n    const positionButtons = document.querySelectorAll('.position-button');\n    positionButtons.forEach(button => {\n      button.addEventListener('click', (event) => {\n        const target = event.target as HTMLElement;\n        const position = target.getAttribute('data-position');\n\n        if (position) {\n          // 更新按钮状态\n          positionButtons.forEach(btn => btn.classList.remove('active'));\n          target.classList.add('active');\n\n          // 更新气泡位置\n          this.setBubblePosition(position);\n        }\n      });\n    });\n\n    // 设置初始位置\n    this.setBubblePosition(this._bubblePosition);\n\n    // 添加拖动事件\n    this.setupBubbleDragging();\n\n    // 页面加载后3秒显示欢迎气泡\n    setTimeout(() => {\n      if (this._bubbleText) {\n        this._bubbleText.textContent = '欢迎使用Live2D控制面板！';\n        this.showSpeechBubble();\n      }\n    }, 3000);\n  }\n\n  /**\n   * 设置模型加载监听器\n   */\n  private setupModelLoadListener(): void {\n    // 定期检查模型是否加载完成\n    const checkModelReady = async () => {\n      if (this._live2DManager.isModelReady()) {\n        this.updateModelInfo();\n        await this.updateMotionButtons();\n        await this.updateExpressionButtons();\n        this.updateStatus('模型加载完成');\n      } else {\n        setTimeout(checkModelReady, 100);\n      }\n    };\n\n    checkModelReady();\n  }\n\n  /**\n   * 切换模型\n   */\n  private switchModel(modelIndex: number): void {\n    this._currentModelIndex = modelIndex;\n    this._live2DManager.switchToModel(modelIndex);\n    this.updateStatus('正在加载模型...');\n\n    // 清空按钮\n    this.clearButtons();\n\n    // 等待模型加载完成\n    this.setupModelLoadListener();\n  }\n\n  /**\n   * 根据模型名称切换模型\n   */\n  private async switchModelByName(modelName: string): Promise<void> {\n    this._live2DManager.loadModelByName(modelName);\n    this.updateStatus(`正在加载模型: ${modelName}...`);\n\n    // 清空按钮\n    this.clearButtons();\n\n    // 等待模型加载完成\n    this.setupModelLoadListener();\n\n    // 验证模型\n    try {\n      const validation = await this._live2DManager.validateModel(modelName);\n      if (validation && !validation.isValid) {\n        this.updateStatus(`模型 ${modelName} 存在问题: ${validation.analysis.issues.join(', ')}`);\n      }\n    } catch (error) {\n      console.warn('模型验证失败:', error);\n    }\n  }\n\n  /**\n   * 显示模型信息\n   */\n  private displayModelInfo(model: any): void {\n    const modelInfo = document.getElementById('modelInfo');\n    const motionCount = document.getElementById('motionCount');\n    const expressionCount = document.getElementById('expressionCount');\n\n    if (modelInfo && motionCount && expressionCount) {\n      modelInfo.style.display = 'block';\n      motionCount.textContent = model.motionGroups ? model.motionGroups.length.toString() : '0';\n      expressionCount.textContent = model.expressions ? model.expressions.length.toString() : '0';\n    }\n  }\n\n  /**\n   * 隐藏模型信息\n   */\n  private hideModelInfo(): void {\n    const modelInfo = document.getElementById('modelInfo');\n    if (modelInfo) {\n      modelInfo.style.display = 'none';\n    }\n  }\n\n  /**\n   * 获取当前模型名称\n   */\n  private getCurrentModelName(): string | null {\n    const modelSelector = document.getElementById('modelSelector') as HTMLSelectElement;\n    if (modelSelector && modelSelector.selectedIndex > 0) {\n      const selectedOption = modelSelector.options[modelSelector.selectedIndex];\n      return selectedOption.getAttribute('data-model-name');\n    }\n    return null;\n  }\n\n  /**\n   * 更新模型信息\n   */\n  private updateModelInfo(): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model || !model.getModelSetting()) {\n      return;\n    }\n\n    const motionGroups = this._live2DManager.getMotionGroups();\n    const expressions = this._live2DManager.getExpressionNames();\n\n    // 计算总动作数\n    let totalMotions = 0;\n    motionGroups.forEach(group => {\n      totalMotions += this._live2DManager.getMotionCount(group);\n    });\n\n    // 更新显示\n    const motionCountElement = document.getElementById('motionCount');\n    const expressionCountElement = document.getElementById('expressionCount');\n    const modelInfoElement = document.getElementById('modelInfo');\n\n    if (motionCountElement) motionCountElement.textContent = totalMotions.toString();\n    if (expressionCountElement) expressionCountElement.textContent = expressions.length.toString();\n    if (modelInfoElement) modelInfoElement.style.display = 'grid';\n  }\n\n  /**\n   * 更新动作按钮\n   */\n  private async updateMotionButtons(): Promise<void> {\n    const motionButtonsContainer = document.getElementById('motionButtons');\n    if (!motionButtonsContainer) return;\n\n    motionButtonsContainer.innerHTML = '<div class=\"loading\">正在加载动作...</div>';\n\n    try {\n      // 尝试从服务器获取当前模型的动作信息\n      const currentModel = this._live2DManager.getCurrentModel();\n      if (currentModel) {\n        // 获取模型名称（这里需要一个方法来获取当前模型名称）\n        const modelName = this.getCurrentModelName();\n        if (modelName) {\n          const motionsData = await this._live2DManager.getModelMotionsFromServer(modelName);\n\n          motionButtonsContainer.innerHTML = '';\n\n          for (const groupName in motionsData) {\n            if (motionsData.hasOwnProperty(groupName)) {\n              const motionArray = motionsData[groupName] as any[];\n              motionArray.forEach((motion, index) => {\n                if (motion.exists) {\n                  const button = document.createElement('button');\n                  button.className = 'control-button motion';\n                  button.textContent = `${groupName} ${index + 1}`;\n                  button.addEventListener('click', () => {\n                    this._live2DManager.playMotion(groupName, index);\n                    this.updateStatus(`播放动作: ${groupName} ${index + 1}`);\n                    this.showMotionBubble(groupName, index);\n                  });\n                  motionButtonsContainer.appendChild(button);\n                }\n              });\n            }\n          }\n\n          if (motionButtonsContainer.children.length === 0) {\n            motionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用动作</div>';\n          }\n          return;\n        }\n      }\n    } catch (error) {\n      console.warn('从服务器获取动作信息失败，使用本地方法:', error);\n    }\n\n    // 回退到本地方法\n    motionButtonsContainer.innerHTML = '';\n    const motionGroups = this._live2DManager.getMotionGroups();\n\n    motionGroups.forEach(group => {\n      const motionCount = this._live2DManager.getMotionCount(group);\n\n      for (let i = 0; i < motionCount; i++) {\n        const button = document.createElement('button');\n        button.className = 'control-button motion';\n        button.textContent = `${group} ${i + 1}`;\n        button.addEventListener('click', () => {\n          this._live2DManager.playMotion(group, i);\n          this.updateStatus(`播放动作: ${group} ${i + 1}`);\n          this.showMotionBubble(group, i);\n        });\n        motionButtonsContainer.appendChild(button);\n      }\n    });\n\n    if (motionButtonsContainer.children.length === 0) {\n      motionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用动作</div>';\n    }\n  }\n\n  /**\n   * 更新表情按钮\n   */\n  private async updateExpressionButtons(): Promise<void> {\n    const expressionButtonsContainer = document.getElementById('expressionButtons');\n    if (!expressionButtonsContainer) return;\n\n    expressionButtonsContainer.innerHTML = '<div class=\"loading\">正在加载表情...</div>';\n\n    try {\n      // 尝试从服务器获取当前模型的表情信息\n      const currentModel = this._live2DManager.getCurrentModel();\n      if (currentModel) {\n        const modelName = this.getCurrentModelName();\n        if (modelName) {\n          const expressions = await this._live2DManager.getModelExpressionsFromServer(modelName);\n\n          expressionButtonsContainer.innerHTML = '';\n\n          expressions.forEach(expressionName => {\n            const button = document.createElement('button');\n            button.className = 'control-button expression';\n            button.textContent = expressionName;\n            button.addEventListener('click', () => {\n              this._live2DManager.setExpression(expressionName);\n              this.updateStatus(`设置表情: ${expressionName}`);\n            });\n            expressionButtonsContainer.appendChild(button);\n          });\n\n          if (expressionButtonsContainer.children.length === 0) {\n            expressionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用表情</div>';\n          }\n          return;\n        }\n      }\n    } catch (error) {\n      console.warn('从服务器获取表情信息失败，使用本地方法:', error);\n    }\n\n    // 回退到本地方法\n    expressionButtonsContainer.innerHTML = '';\n    const expressions = this._live2DManager.getExpressionNames();\n\n    expressions.forEach(expressionName => {\n      const button = document.createElement('button');\n      button.className = 'control-button expression';\n      button.textContent = expressionName;\n      button.addEventListener('click', () => {\n        this._live2DManager.setExpression(expressionName);\n        this.updateStatus(`设置表情: ${expressionName}`);\n      });\n      expressionButtonsContainer.appendChild(button);\n    });\n\n    if (expressionButtonsContainer.children.length === 0) {\n      expressionButtonsContainer.innerHTML = '<div class=\"loading\">暂无可用表情</div>';\n    }\n  }\n\n  /**\n   * 清空按钮\n   */\n  private clearButtons(): void {\n    const motionButtonsContainer = document.getElementById('motionButtons');\n    const expressionButtonsContainer = document.getElementById('expressionButtons');\n    const modelInfoElement = document.getElementById('modelInfo');\n\n    if (motionButtonsContainer) {\n      motionButtonsContainer.innerHTML = '<div class=\"loading\">加载中...</div>';\n    }\n    if (expressionButtonsContainer) {\n      expressionButtonsContainer.innerHTML = '<div class=\"loading\">加载中...</div>';\n    }\n    if (modelInfoElement) {\n      modelInfoElement.style.display = 'none';\n    }\n  }\n\n  /**\n   * 显示文字气泡\n   */\n  private showSpeechBubble(): void {\n    if (!this._speechBubble) return;\n\n    // 清除之前的自动隐藏定时器\n    if (this._bubbleTimeout) {\n      clearTimeout(this._bubbleTimeout);\n      this._bubbleTimeout = null;\n    }\n\n    // 显示气泡\n    this._speechBubble.classList.add('show');\n    this.updateStatus('显示文字气泡');\n\n    // 5秒后自动隐藏\n    this._bubbleTimeout = window.setTimeout(() => {\n      this.hideSpeechBubble();\n    }, 5000);\n  }\n\n  /**\n   * 隐藏文字气泡\n   */\n  private hideSpeechBubble(): void {\n    if (!this._speechBubble) return;\n\n    // 清除自动隐藏定时器\n    if (this._bubbleTimeout) {\n      clearTimeout(this._bubbleTimeout);\n      this._bubbleTimeout = null;\n    }\n\n    // 隐藏气泡\n    this._speechBubble.classList.remove('show');\n    this.updateStatus('隐藏文字气泡');\n  }\n\n  /**\n   * 显示与动作同步的文字气泡\n   */\n  private showMotionBubble(group: string, motionIndex: number): void {\n    if (!this._bubbleText) return;\n\n    // 根据动作类型显示不同的文字\n    const motionTexts: { [key: string]: string[] } = {\n      'Idle': [\n        '我在这里等你哦~',\n        '今天天气真不错呢！',\n        '你想和我聊什么呢？',\n        '我正在想你呢~',\n        '有什么我可以帮助你的吗？'\n      ],\n      'TapBody': [\n        '哎呀，你在摸我呢！',\n        '好痒啊~',\n        '嘻嘻，你真调皮！',\n        '不要乱摸啦~',\n        '你的手好温暖呢！'\n      ]\n    };\n\n    // 获取对应的文字数组\n    const texts = motionTexts[group] || ['正在播放动作...'];\n\n    // 选择文字（如果有多个动作，按索引选择，否则随机选择）\n    let selectedText: string;\n    if (motionIndex < texts.length) {\n      selectedText = texts[motionIndex];\n    } else {\n      selectedText = texts[Math.floor(Math.random() * texts.length)];\n    }\n\n    // 更新气泡文字\n    this._bubbleText.textContent = selectedText;\n\n    // 同时更新输入框\n    const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n    if (bubbleTextInput) {\n      bubbleTextInput.value = selectedText;\n    }\n\n    // 显示气泡\n    this.showSpeechBubble();\n  }\n\n  /**\n   * 设置气泡拖动功能\n   */\n  private setupBubbleDragging(): void {\n    if (!this._speechBubble) return;\n\n    // 鼠标按下事件\n    this._speechBubble.addEventListener('mousedown', (e: MouseEvent) => {\n      this._isDragging = true;\n      this._speechBubble!.classList.add('dragging');\n\n      const rect = this._speechBubble!.getBoundingClientRect();\n      this._dragOffset.x = e.clientX - rect.left;\n      this._dragOffset.y = e.clientY - rect.top;\n\n      e.preventDefault();\n    });\n\n    // 鼠标移动事件\n    document.addEventListener('mousemove', (e: MouseEvent) => {\n      if (!this._isDragging || !this._speechBubble) return;\n\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n\n      // 计算新位置（相对于canvas容器）\n      let newX = e.clientX - containerRect.left - this._dragOffset.x;\n      let newY = e.clientY - containerRect.top - this._dragOffset.y;\n\n      // 获取气泡的尺寸\n      const bubbleWidth = this._speechBubble.offsetWidth;\n      const bubbleHeight = this._speechBubble.offsetHeight;\n\n      // 限制在画布范围内\n      newX = Math.max(10, Math.min(newX, containerRect.width - bubbleWidth - 10));\n      newY = Math.max(10, Math.min(newY, containerRect.height - bubbleHeight - 10));\n\n      // 设置位置\n      this._speechBubble.style.left = `${newX}px`;\n      this._speechBubble.style.top = `${newY}px`;\n      this._speechBubble.style.right = '';\n      this._speechBubble.style.bottom = '';\n      this._speechBubble.style.transform = '';\n    });\n\n    // 鼠标释放事件\n    document.addEventListener('mouseup', () => {\n      if (this._isDragging && this._speechBubble) {\n        this._isDragging = false;\n        this._speechBubble.classList.remove('dragging');\n        // 标记气泡已被用户手动拖动\n        this._bubbleManuallyPositioned = true;\n      }\n    });\n\n    // 触摸事件支持\n    this._speechBubble.addEventListener('touchstart', (e: TouchEvent) => {\n      this._isDragging = true;\n      this._speechBubble!.classList.add('dragging');\n\n      const touch = e.touches[0];\n      const rect = this._speechBubble!.getBoundingClientRect();\n      this._dragOffset.x = touch.clientX - rect.left;\n      this._dragOffset.y = touch.clientY - rect.top;\n\n      e.preventDefault();\n    });\n\n    document.addEventListener('touchmove', (e: TouchEvent) => {\n      if (!this._isDragging || !this._speechBubble) return;\n\n      const touch = e.touches[0];\n      const canvasContainer = document.querySelector('.canvas-container') as HTMLElement;\n      if (!canvasContainer) return;\n\n      const containerRect = canvasContainer.getBoundingClientRect();\n\n      // 计算新位置（相对于canvas容器）\n      let newX = touch.clientX - containerRect.left - this._dragOffset.x;\n      let newY = touch.clientY - containerRect.top - this._dragOffset.y;\n\n      // 获取气泡的尺寸\n      const bubbleWidth = this._speechBubble.offsetWidth;\n      const bubbleHeight = this._speechBubble.offsetHeight;\n\n      // 限制在画布范围内\n      newX = Math.max(10, Math.min(newX, containerRect.width - bubbleWidth - 10));\n      newY = Math.max(10, Math.min(newY, containerRect.height - bubbleHeight - 10));\n\n      // 设置位置\n      this._speechBubble.style.left = `${newX}px`;\n      this._speechBubble.style.top = `${newY}px`;\n      this._speechBubble.style.right = '';\n      this._speechBubble.style.bottom = '';\n      this._speechBubble.style.transform = '';\n    });\n\n    document.addEventListener('touchend', () => {\n      if (this._isDragging && this._speechBubble) {\n        this._isDragging = false;\n        this._speechBubble.classList.remove('dragging');\n        // 标记气泡已被用户手动拖动\n        this._bubbleManuallyPositioned = true;\n      }\n    });\n  }\n\n  /**\n   * 设置气泡位置\n   */\n  private setBubblePosition(position: string): void {\n    if (!this._speechBubble) return;\n\n    // 如果气泡已被用户手动拖动，则不重置位置\n    if (this._bubbleManuallyPositioned) {\n      return;\n    }\n\n    this._bubblePosition = position;\n\n    // 移除所有位置类和样式\n    this._speechBubble.classList.remove('top', 'bottom', 'left', 'right');\n    this._speechBubble.style.left = '';\n    this._speechBubble.style.top = '';\n    this._speechBubble.style.right = '';\n    this._speechBubble.style.bottom = '';\n    this._speechBubble.style.transform = '';\n\n    // 根据位置设置样式\n    switch (position) {\n      case 'bottom-left':\n        this._speechBubble.style.left = '50px';\n        this._speechBubble.style.bottom = '50px';\n        this._speechBubble.classList.add('bottom');\n        break;\n      case 'bottom-right':\n        this._speechBubble.style.right = '50px';\n        this._speechBubble.style.bottom = '50px';\n        this._speechBubble.classList.add('bottom', 'right');\n        break;\n      case 'top-left':\n        this._speechBubble.style.left = '50px';\n        this._speechBubble.style.top = '50px';\n        this._speechBubble.classList.add('top');\n        break;\n      case 'top-right':\n        this._speechBubble.style.right = '50px';\n        this._speechBubble.style.top = '50px';\n        this._speechBubble.classList.add('top', 'right');\n        break;\n      case 'center':\n      default:\n        // 默认居中显示\n        this._speechBubble.style.left = '50%';\n        this._speechBubble.style.top = '50%';\n        this._speechBubble.style.transform = 'translate(-50%, -50%)';\n        break;\n    }\n  }\n\n  /**\n   * 测试口型同步功能\n   */\n  private testLipSync(): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    // 显示测试气泡\n    if (this._bubbleText) {\n      this._bubbleText.textContent = '正在测试口型同步功能...';\n      this.showSpeechBubble();\n    }\n\n    // 使用模型自带的wav文件处理器和同时播放音频\n    try {\n      // 获取测试音频文件路径\n      const audioPath = '../../Resources/测试.wav';\n\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audio = new Audio(audioPath);\n      audio.volume = 0.8; // 设置音量\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        // 同时启动音频播放和口型同步\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.start(audioPath))\n        ]).then(() => {\n          this.updateStatus('开始播放测试音频，观察口型同步效果');\n\n          // 更新气泡文字\n          setTimeout(() => {\n            if (this._bubbleText) {\n              this._bubbleText.textContent = '口型同步测试中，请观察嘴部动画！';\n            }\n          }, 1000);\n        }).catch(error => {\n          console.error('播放音频失败:', error);\n          this.updateStatus('播放音频失败，请检查音频文件和浏览器权限');\n        });\n\n        // 音频结束时的处理\n        audio.addEventListener('ended', () => {\n          this.updateStatus('口型同步测试完成');\n          if (this._bubbleText) {\n            this._bubbleText.textContent = '口型同步测试完成！';\n          }\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('测试口型同步失败:', error);\n      this.updateStatus('测试口型同步失败，请检查音频文件');\n    }\n  }\n\n  // ==================== 公共API接口 ====================\n\n  /**\n   * 更换模型\n   * @param modelIndex 模型索引 (0: Haru, 1: Hiyori, 2: Mark, 3: Natori, 4: Rice)\n   */\n  public changeModel(modelIndex: number): void {\n    if (modelIndex >= 0 && modelIndex < LAppDefine.ModelDirSize) {\n      this._live2DManager.switchToModel(modelIndex);\n      this.updateStatus(`切换到模型 ${modelIndex + 1}`);\n    } else {\n      this.updateStatus(`无效的模型索引: ${modelIndex}`);\n    }\n  }\n\n  /**\n   * 播放动作\n   * @param group 动作组名 (如: \"Idle\", \"TapBody\")\n   * @param index 动作索引 (从0开始)\n   */\n  public playMotion(group: string, index: number = 0): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (model) {\n      this._live2DManager.playMotion(group, index);\n      this.updateStatus(`播放动作: ${group} ${index + 1}`);\n\n      // 播放动作时显示相应的文字气泡\n      this.showMotionBubble(group, index);\n    } else {\n      this.updateStatus('请先选择一个模型');\n    }\n  }\n\n  /**\n   * 播放表情\n   * @param index 表情索引 (从0开始)\n   */\n  public playExpression(index: number): void {\n    const expressionNames = this._live2DManager.getExpressionNames();\n    if (index >= 0 && index < expressionNames.length) {\n      const expressionName = expressionNames[index];\n      this._live2DManager.setExpression(expressionName);\n      this.updateStatus(`播放表情: ${expressionName}`);\n    } else {\n      this.updateStatus(`无效的表情索引: ${index}`);\n    }\n  }\n\n  /**\n   * 播放随机表情\n   */\n  public playRandomExpression(): void {\n    this._live2DManager.setRandomExpression();\n    this.updateStatus('播放随机表情');\n  }\n\n  /**\n   * 显示文字气泡\n   * @param text 要显示的文字\n   * @param autoHide 是否自动隐藏 (默认: true, 5秒后隐藏)\n   */\n  public showBubble(text: string, autoHide: boolean = true): void {\n    if (this._bubbleText) {\n      this._bubbleText.textContent = text;\n\n      // 同时更新输入框\n      const bubbleTextInput = document.getElementById('bubbleTextInput') as HTMLTextAreaElement;\n      if (bubbleTextInput) {\n        bubbleTextInput.value = text;\n      }\n    }\n\n    this.showSpeechBubble();\n\n    if (!autoHide) {\n      // 清除自动隐藏定时器\n      if (this._bubbleTimeout) {\n        clearTimeout(this._bubbleTimeout);\n        this._bubbleTimeout = null;\n      }\n    }\n  }\n\n  /**\n   * 隐藏文字气泡\n   */\n  public hideBubble(): void {\n    this.hideSpeechBubble();\n  }\n\n  /**\n   * 播放音频并进行口型同步\n   * @param audioPath 音频文件路径 (相对于Resources目录)\n   */\n  public playAudioWithLipSync(audioPath: string): void {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    try {\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audio = new Audio(`../../Resources/${audioPath}`);\n      audio.volume = 0.8;\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.start(`../../Resources/${audioPath}`))\n        ]).then(() => {\n          this.updateStatus(`播放音频: ${audioPath}`);\n        }).catch(error => {\n          console.error('播放音频失败:', error);\n          this.updateStatus('播放音频失败，请检查音频文件和浏览器权限');\n        });\n\n        audio.addEventListener('ended', () => {\n          this.updateStatus('音频播放完成');\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('播放音频失败:', error);\n      this.updateStatus('播放音频失败，请检查音频文件');\n    }\n  }\n\n  /**\n   * 播放base64编码的音频并进行口型同步\n   * @param base64Data base64编码的音频数据 (支持WAV和MP3格式)\n   */\n  public async playAudioFromBase64(base64Data: string): Promise<void> {\n    const model = this._live2DManager.getCurrentModel();\n    if (!model) {\n      this.updateStatus('请先选择一个模型');\n      return;\n    }\n\n    try {\n      this.updateStatus('正在处理音频数据...');\n\n      // 检测音频格式并转换为WAV\n      const wavArrayBuffer = await AudioConverter.processBase64Audio(base64Data);\n\n      // 启用口型同步\n      (model as any)._lipsync = true;\n\n      // 创建音频元素播放声音\n      const audioBlob = new Blob([wavArrayBuffer], { type: 'audio/wav' });\n      const audioUrl = URL.createObjectURL(audioBlob);\n      const audio = new Audio(audioUrl);\n      audio.volume = 0.8;\n\n      // 使用模型的wav文件处理器进行口型同步\n      const wavFileHandler = (model as any)._wavFileHandler;\n      if (wavFileHandler) {\n        Promise.all([\n          audio.play(),\n          Promise.resolve(wavFileHandler.startFromArrayBuffer(wavArrayBuffer))\n        ]).then(() => {\n          this.updateStatus('播放base64音频成功');\n        }).catch(error => {\n          console.error('播放base64音频失败:', error);\n          this.updateStatus('播放base64音频失败，请检查音频数据和浏览器权限');\n        });\n\n        audio.addEventListener('ended', () => {\n          this.updateStatus('base64音频播放完成');\n          // 清理URL对象\n          URL.revokeObjectURL(audioUrl);\n        });\n\n        // 添加错误处理\n        audio.addEventListener('error', (error) => {\n          console.error('音频播放错误:', error);\n          this.updateStatus('音频播放出错');\n          URL.revokeObjectURL(audioUrl);\n        });\n      } else {\n        this.updateStatus('音频处理器未初始化');\n      }\n    } catch (error) {\n      console.error('处理base64音频失败:', error);\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      this.updateStatus(`处理base64音频失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 获取当前模型信息\n   */\n  public getCurrentModelInfo(): any {\n    const model = this._live2DManager.getCurrentModel();\n    if (model) {\n      return {\n        modelName: 'Current Model',\n        hasModel: true,\n        motionGroups: this._live2DManager.getMotionGroups(),\n        expressions: this._live2DManager.getExpressionNames()\n      };\n    }\n    return {\n      modelName: 'No Model',\n      hasModel: false,\n      motionGroups: [],\n      expressions: []\n    };\n  }\n\n  // ==================== 私有方法 ====================\n\n  /**\n   * 切换控制面板显示状态\n   */\n  private toggleControlPanel(): void {\n    const controlPanel = document.querySelector('.control-panel') as HTMLElement;\n    if (controlPanel) {\n      controlPanel.classList.toggle('hidden');\n      console.log('控制面板显示状态已切换');\n    }\n  }\n\n  /**\n   * 更新状态信息\n   */\n  private updateStatus(message: string): void {\n    const statusElement = document.getElementById('statusText');\n    if (statusElement) {\n      statusElement.textContent = message;\n    }\n\n    // 在控制台也输出状态信息\n    LAppPal.printMessage(`[Control Panel] ${message}`);\n  }\n\n  /**\n   * 初始化模型上传功能\n   */\n  private initializeModelUpload(): void {\n    const fileInput = document.getElementById('modelFileInput') as HTMLInputElement;\n    const selectFileBtn = document.getElementById('selectFileBtn') as HTMLButtonElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n    const selectedFileName = document.getElementById('selectedFileName') as HTMLDivElement;\n\n    // 选择文件按钮点击事件\n    selectFileBtn.addEventListener('click', () => {\n      fileInput.click();\n    });\n\n    // 文件选择事件\n    fileInput.addEventListener('change', (event) => {\n      const target = event.target as HTMLInputElement;\n      const file = target.files?.[0];\n\n      if (file) {\n        if (file.type === 'application/zip' || file.name.endsWith('.zip')) {\n          selectedFileName.textContent = `已选择: ${file.name}`;\n          selectedFileName.style.display = 'block';\n          uploadBtn.style.display = 'block';\n          this.updateStatus(`已选择文件: ${file.name}`);\n        } else {\n          alert('请选择ZIP格式的文件');\n          target.value = '';\n        }\n      }\n    });\n\n    // 上传按钮点击事件\n    uploadBtn.addEventListener('click', () => {\n      const file = fileInput.files?.[0];\n      if (file) {\n        this.uploadModel(file);\n      }\n    });\n  }\n\n  /**\n   * 上传模型文件\n   */\n  public async uploadModel(file: File): Promise<void> {\n    const uploadStatus = document.getElementById('uploadStatus') as HTMLDivElement;\n    const uploadStatusText = document.getElementById('uploadStatusText') as HTMLParagraphElement;\n    const progressBar = document.getElementById('progressBar') as HTMLDivElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n\n    try {\n      // 显示上传状态\n      uploadStatus.style.display = 'block';\n      uploadStatus.className = 'upload-status';\n      uploadStatusText.textContent = '正在上传...';\n      progressBar.style.width = '0%';\n      uploadBtn.disabled = true;\n\n      // 创建FormData\n      const formData = new FormData();\n      formData.append('modelZip', file);\n\n      // 上传文件\n      const response = await fetch('http://localhost:3001/api/upload-model', {\n        method: 'POST',\n        body: formData\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n\n        // 上传成功\n        uploadStatus.className = 'upload-status success';\n        uploadStatusText.textContent = `上传成功! 模型 ${result.modelName} 已添加`;\n        progressBar.style.width = '100%';\n\n        // 显示分析结果\n        if (result.analysis) {\n          const analysisInfo: string[] = [];\n          if (result.analysis.hasExpressions) {\n            analysisInfo.push(`表情: ${result.analysis.expressions.length}个`);\n          }\n          if (result.analysis.hasMotions) {\n            analysisInfo.push(`动作组: ${result.analysis.motionGroups.length}个`);\n          }\n          if (result.analysis.issues && result.analysis.issues.length > 0) {\n            analysisInfo.push(`问题: ${result.analysis.issues.length}个`);\n          }\n\n          if (analysisInfo.length > 0) {\n            uploadStatusText.textContent += ` (${analysisInfo.join(', ')})`;\n          }\n        }\n\n        // 刷新模型列表\n        await this.refreshModelList();\n\n        // 清空文件选择\n        this.clearFileSelection();\n\n        // 通知主应用刷新内嵌模型数据\n        this.notifyMainAppModelUploaded(result.modelName);\n\n        this.updateStatus(`模型 ${result.modelName} 上传成功`);\n      } else {\n        const error = await response.json();\n        throw new Error(error.error || '上传失败');\n      }\n    } catch (error) {\n      // 上传失败\n      const errorMessage = error instanceof Error ? error.message : '未知错误';\n      uploadStatus.className = 'upload-status error';\n      uploadStatusText.textContent = `上传失败: ${errorMessage}`;\n      this.updateStatus(`上传失败: ${errorMessage}`);\n    } finally {\n      uploadBtn.disabled = false;\n    }\n  }\n\n  /**\n   * 刷新模型列表\n   */\n  public async refreshModelList(): Promise<void> {\n    try {\n      // 重新初始化模型选择器\n      await this.initializeModelSelector();\n      this.updateStatus('模型列表已更新');\n    } catch (error) {\n      console.error('刷新模型列表失败:', error);\n      this.updateStatus('刷新模型列表失败');\n    }\n  }\n\n  /**\n   * 清空文件选择\n   */\n  private clearFileSelection(): void {\n    const fileInput = document.getElementById('modelFileInput') as HTMLInputElement;\n    const selectedFileName = document.getElementById('selectedFileName') as HTMLDivElement;\n    const uploadBtn = document.getElementById('uploadBtn') as HTMLButtonElement;\n\n    fileInput.value = '';\n    selectedFileName.style.display = 'none';\n    uploadBtn.style.display = 'none';\n  }\n\n  /**\n   * 删除模型\n   */\n  public async deleteModel(modelName: string): Promise<void> {\n    if (!confirm(`确定要删除模型 \"${modelName}\" 吗？此操作不可撤销。`)) {\n      return;\n    }\n\n    try {\n      this.updateStatus(`正在删除模型: ${modelName}...`);\n\n      const response = await fetch(`http://localhost:3001/api/models/${modelName}`, {\n        method: 'DELETE'\n      });\n\n      if (response.ok) {\n        await response.json();\n        this.updateStatus(`模型 ${modelName} 删除成功`);\n\n        // 刷新模型列表\n        await this.refreshModelList();\n\n        // 隐藏删除按钮\n        const deleteModelBtn = document.getElementById('deleteModelBtn') as HTMLButtonElement;\n        deleteModelBtn.style.display = 'none';\n\n        // 通知主应用刷新内嵌模型数据\n        this.notifyMainAppModelDeleted(modelName);\n\n      } else {\n        const error = await response.json();\n        throw new Error(error.error || '删除失败');\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '未知错误';\n      this.updateStatus(`删除失败: ${errorMessage}`);\n      alert(`删除模型失败: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * 通知主应用模型已上传\n   */\n  private notifyMainAppModelUploaded(modelName: string): void {\n    try {\n      // 通过postMessage通知父窗口\n      if (window.parent && window.parent !== window) {\n        window.parent.postMessage({\n          type: 'live2d_model_uploaded',\n          modelName: modelName\n        }, '*');\n      }\n\n      // 如果在同一个窗口中，直接调用内嵌管理器的刷新方法\n      if ((window as any).embeddedLive2DManager &&\n          typeof (window as any).embeddedLive2DManager.refreshEmbeddedData === 'function') {\n        (window as any).embeddedLive2DManager.refreshEmbeddedData();\n      }\n    } catch (error) {\n      console.warn('通知主应用模型上传失败:', error);\n    }\n  }\n\n  /**\n   * 通知主应用模型已删除\n   */\n  private notifyMainAppModelDeleted(modelName: string): void {\n    try {\n      // 通过postMessage通知父窗口\n      if (window.parent && window.parent !== window) {\n        window.parent.postMessage({\n          type: 'live2d_model_deleted',\n          modelName: modelName\n        }, '*');\n      }\n\n      // 如果在同一个窗口中，直接调用内嵌管理器的刷新方法\n      if ((window as any).embeddedLive2DManager &&\n          typeof (window as any).embeddedLive2DManager.refreshEmbeddedData === 'function') {\n        (window as any).embeddedLive2DManager.refreshEmbeddedData();\n      }\n    } catch (error) {\n      console.warn('通知主应用模型删除失败:', error);\n    }\n  }\n\n  /**\n   * 释放资源\n   */\n  public release(): void {\n    LAppDelegate.releaseInstance();\n  }\n}\n\n// 全局应用程序实例\nlet app: ControlPanelApp;\n\n/**\n * 页面加载完成后初始化应用程序\n */\nwindow.addEventListener('DOMContentLoaded', async () => {\n  app = new ControlPanelApp();\n  await app.initialize();\n\n  // 将API暴露到全局对象，方便外部调用\n  (window as any).Live2DControlPanel = {\n    // 模型控制\n    changeModel: (index: number) => app.changeModel(index),\n\n    // 动作控制\n    playMotion: (group: string, index: number = 0) => app.playMotion(group, index),\n\n    // 表情控制\n    playExpression: (index: number) => app.playExpression(index),\n    playRandomExpression: () => app.playRandomExpression(),\n\n    // 气泡控制\n    showBubble: (text: string, autoHide: boolean = true) => app.showBubble(text, autoHide),\n    hideBubble: () => app.hideBubble(),\n\n    // 音频播放\n    playAudio: (audioPath: string) => app.playAudioWithLipSync(audioPath),\n    playAudioFromBase64: (base64Data: string) => app.playAudioFromBase64(base64Data),\n\n    // 模型上传\n    uploadModel: (file: File) => app.uploadModel(file),\n    refreshModelList: () => app.refreshModelList(),\n    deleteModel: (modelName: string) => app.deleteModel(modelName),\n\n    // 信息获取\n    getModelInfo: () => app.getCurrentModelInfo(),\n\n    // 内部实例（高级用户使用）\n    _app: app\n  };\n\n  // 添加消息监听器，处理来自父窗口的API调用\n  window.addEventListener('message', (event) => {\n    // 处理控制面板切换消息\n    if (event.data && event.data.type === 'toggle_panel') {\n      const controlPanel = document.querySelector('.control-panel') as HTMLElement;\n      if (controlPanel) {\n        controlPanel.classList.toggle('hidden');\n        console.log('控制面板显示状态已切换');\n      }\n      return;\n    }\n\n    // 验证消息来源（可选，根据需要调整）\n    if (event.data && event.data.type === 'live2d_api_call') {\n      const { messageId, method, args } = event.data;\n\n      try {\n        // 获取API方法\n        const api = (window as any).Live2DControlPanel;\n        if (api && typeof api[method] === 'function') {\n          // 调用API方法\n          const result = api[method](...args);\n\n          // 如果返回Promise，等待结果\n          if (result && typeof result.then === 'function') {\n            result.then((res: any) => {\n              // 发送成功响应\n              (event.source as any)?.postMessage({\n                type: 'live2d_api_response',\n                messageId: messageId,\n                success: true,\n                result: res\n              }, '*');\n            }).catch((error: any) => {\n              // 发送错误响应\n              (event.source as any)?.postMessage({\n                type: 'live2d_api_response',\n                messageId: messageId,\n                success: false,\n                error: error.message || '调用失败'\n              }, '*');\n            });\n          } else {\n            // 同步方法，直接发送结果\n            (event.source as any)?.postMessage({\n              type: 'live2d_api_response',\n              messageId: messageId,\n              success: true,\n              result: result\n            }, '*');\n          }\n        } else {\n          // 方法不存在\n          (event.source as any)?.postMessage({\n            type: 'live2d_api_response',\n            messageId: messageId,\n            success: false,\n            error: `方法 ${method} 不存在`\n          }, '*');\n        }\n      } catch (error: any) {\n        // 发送错误响应\n        (event.source as any)?.postMessage({\n          type: 'live2d_api_response',\n          messageId: messageId,\n          success: false,\n          error: error.message || '调用失败'\n        }, '*');\n      }\n    }\n  });\n});\n\n/**\n * 页面卸载时释放资源\n */\nwindow.addEventListener('beforeunload', () => {\n  if (app) {\n    app.release();\n  }\n});\n"], "names": ["_AudioConverter", "base64Data", "cleanBase64", "binaryString", "bytes", "i", "riff", "wave", "error", "mp3<PERSON><PERSON><PERSON><PERSON><PERSON>er", "audioBuffer", "errorMessage", "numberOfChannels", "sampleRate", "length", "bitsPerSample", "bytesPerSample", "dataSize", "fileSize", "arrayBuffer", "view", "offset", "channel", "channelData", "dataOffset", "sample", "intSample", "string", "format", "AudioConverter", "ControlPanelApp", "LAppDelegate", "modelSelector", "deleteModelBtn", "availableModels", "model", "index", "option", "event", "target", "selectedIndex", "selected<PERSON><PERSON>l", "modelName", "panelToggleBtn", "randomMotionBtn", "LAppDefine.MotionGroupIdle", "stopMotionBtn", "randomExpressionBtn", "resetExpressionBtn", "testLipSyncBtn", "showBubbleBtn", "hideBubbleBtn", "bubbleTextInput", "positionButtons", "button", "position", "btn", "checkModelReady", "modelIndex", "validation", "modelInfo", "motionCount", "expressionCount", "motionGroups", "expressions", "totalMotions", "group", "motionCountElement", "expressionCountElement", "modelInfoElement", "motionButtonsContainer", "motionsData", "groupName", "motion", "expressionButtonsContainer", "expressionName", "motionIndex", "texts", "selectedText", "rect", "canvasContainer", "containerRect", "newX", "newY", "bubbleWidth", "bubbleHeight", "touch", "audioPath", "audio", "wavFileHandler", "LAppDefine.ModelDirSize", "expressionNames", "text", "autoHide", "wavA<PERSON><PERSON><PERSON><PERSON>er", "audioBlob", "audioUrl", "controlPanel", "message", "statusElement", "LAppPal", "fileInput", "selectFileBtn", "uploadBtn", "selected<PERSON><PERSON><PERSON><PERSON>", "file", "_a", "uploadStatus", "uploadStatusText", "progressBar", "formData", "response", "result", "analysisInfo", "app", "messageId", "method", "args", "api", "res", "_b", "_c"], "mappings": "oEAIO,MAAMA,EAAN,MAAMA,CAAe,CAM1B,OAAe,iBAAgC,CACzC,OAAC,KAAK,eACR,KAAK,aAAe,IAAK,OAAO,cAAiB,OAAe,qBAE3D,KAAK,YAAA,CAQd,OAAO,kBAAkBC,EAA+C,CAClE,GAAA,CAEF,MAAMC,EAAcD,EAAW,QAAQ,6BAA8B,EAAE,EAGjEE,EAAe,KAAKD,CAAW,EAC/BE,EAAQ,IAAI,WAAW,KAAK,IAAI,GAAID,EAAa,MAAM,CAAC,EAC9D,QAASE,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAChCD,EAAMC,CAAC,EAAIF,EAAa,WAAWE,CAAC,EAIlC,GAAAD,EAAM,QAAU,GAAI,CACtB,MAAME,EAAO,OAAO,aAAaF,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EACjEG,EAAO,OAAO,aAAaH,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,EAAE,EAAGA,EAAM,EAAE,CAAC,EACrE,GAAAE,IAAS,QAAUC,IAAS,OACvB,MAAA,KACT,CAIE,OAAAH,EAAM,QAAU,IAEdA,EAAM,CAAC,IAAM,IAAQA,EAAM,CAAC,IAAM,IAAQA,EAAM,CAAC,IAAM,IAKvDA,EAAM,CAAC,IAAM,MAASA,EAAM,CAAC,EAAI,OAAU,KACtC,MAIJ,gBACAI,EAAO,CACN,eAAA,MAAM,YAAaA,CAAK,EACzB,SAAA,CACT,CAQF,OAAO,oBAAoBP,EAAiC,CAE1D,MAAMC,EAAcD,EAAW,QAAQ,6BAA8B,EAAE,EAEjEE,EAAe,KAAKD,CAAW,EAC/BE,EAAQ,IAAI,WAAWD,EAAa,MAAM,EAChD,QAASE,EAAI,EAAGA,EAAIF,EAAa,OAAQE,IACvCD,EAAMC,CAAC,EAAIF,EAAa,WAAWE,CAAC,EAEtC,OAAOD,EAAM,MAAA,CAQf,aAAa,gBAAgBK,EAAmD,CAC1E,GAAA,CAIF,MAAMC,EAAc,MAHC,KAAK,gBAAgB,EAGH,gBAAgBD,EAAe,MAAM,CAAC,CAAC,EAGvE,OAAA,KAAK,iBAAiBC,CAAW,QACjCF,EAAO,CACN,QAAA,MAAM,aAAcA,CAAK,EACjC,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EAC1E,MAAM,IAAI,MAAM,cAAcG,CAAY,EAAE,CAAA,CAC9C,CAQF,OAAO,iBAAiBD,EAAuC,CAC7D,MAAME,EAAmBF,EAAY,iBAC/BG,EAAaH,EAAY,WACzBI,EAASJ,EAAY,OACrBK,EAAgB,GAChBC,EAAiBD,EAAgB,EAGjCE,EAAWH,EAASF,EAAmBI,EACvCE,EAAW,GAAKD,EAGhBE,EAAc,IAAI,YAAYD,CAAQ,EACtCE,EAAO,IAAI,SAASD,CAAW,EAGrC,IAAIE,EAAS,EAGR,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAElDD,EAAK,UAAUC,EAAQH,EAAW,EAAG,EAAI,EAAaG,GAAA,EAEjD,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAG7C,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAE7CD,EAAA,UAAUC,EAAQ,GAAI,EAAI,EAAaA,GAAA,EAEvCD,EAAA,UAAUC,EAAQ,EAAG,EAAI,EAAaA,GAAA,EAEtCD,EAAA,UAAUC,EAAQT,EAAkB,EAAI,EAAaS,GAAA,EAErDD,EAAA,UAAUC,EAAQR,EAAY,EAAI,EAAaQ,GAAA,EAEpDD,EAAK,UAAUC,EAAQR,EAAaD,EAAmBI,EAAgB,EAAI,EAAaK,GAAA,EAExFD,EAAK,UAAUC,EAAQT,EAAmBI,EAAgB,EAAI,EAAaK,GAAA,EAEtED,EAAA,UAAUC,EAAQN,EAAe,EAAI,EAAaM,GAAA,EAGlD,KAAA,YAAYD,EAAMC,EAAQ,MAAM,EAAaA,GAAA,EAE7CD,EAAA,UAAUC,EAAQJ,EAAU,EAAI,EAAaI,GAAA,EAGlD,QAASC,EAAU,EAAGA,EAAUV,EAAkBU,IAAW,CACrD,MAAAC,EAAcb,EAAY,eAAeY,CAAO,EAClD,IAAAE,EAAa,GAAKF,EAAUN,EAEhC,QAASX,EAAI,EAAGA,EAAIS,EAAQT,IAAK,CAEzB,MAAAoB,EAAS,KAAK,IAAI,GAAI,KAAK,IAAI,EAAGF,EAAYlB,CAAC,CAAC,CAAC,EACjDqB,EAAYD,EAAS,EAAIA,EAAS,MAASA,EAAS,MAErDL,EAAA,SAASI,EAAYE,EAAW,EAAI,EACzCF,GAAcZ,EAAmBI,CAAA,CACnC,CAGK,OAAAG,CAAA,CAST,OAAe,YAAYC,EAAgBC,EAAgBM,EAAsB,CAC/E,QAAStB,EAAI,EAAGA,EAAIsB,EAAO,OAAQtB,IACjCe,EAAK,SAASC,EAAShB,EAAGsB,EAAO,WAAWtB,CAAC,CAAC,CAChD,CAQF,aAAa,mBAAmBJ,EAA0C,CAClE,MAAA2B,EAAS,KAAK,kBAAkB3B,CAAU,EAC1CkB,EAAc,KAAK,oBAAoBlB,CAAU,EAEvD,OAAQ2B,EAAQ,CACd,IAAK,MACH,eAAQ,IAAI,eAAe,EACpBT,EAET,IAAK,MACH,eAAQ,IAAI,iBAAiB,EACtB,MAAM,KAAK,gBAAgBA,CAAW,EAE/C,QACE,QAAQ,KAAK,kBAAkB,EAC3B,GAAA,CACK,OAAA,MAAM,KAAK,gBAAgBA,CAAW,OAC/B,CACd,eAAQ,MAAM,qBAAqB,EAC5BA,CAAA,CACT,CACJ,CAEJ,EA9MEnB,EAAe,aAAoC,KAD9C,IAAM6B,EAAN7B,ECaP,MAAM8B,CAAgB,CAAtB,aAAA,CAGE,KAAQ,mBAA6B,EACrC,KAAQ,cAAoC,KAC5C,KAAQ,YAAkC,KAC1C,KAAQ,gBAA0B,SAClC,KAAQ,eAAgC,KACxC,KAAQ,YAAuB,GAC/B,KAAQ,YAAwC,CAAE,EAAG,EAAG,EAAG,CAAE,EAC7D,KAAQ,0BAAqC,EAAA,CAK7C,MAAa,YAA4B,CAElC,KAAA,UAAYC,EAAa,YAAY,EAC1C,KAAK,UAAU,WAAW,EAGrB,KAAA,eAAiB,KAAK,UAAU,iBAAiB,EAGtD,MAAM,KAAK,aAAa,EAGxB,KAAK,UAAU,IAAI,CAAA,CAMrB,MAAc,cAA8B,CAE1C,MAAM,KAAK,wBAAwB,EAGnC,KAAK,yBAAyB,EAG9B,KAAK,uBAAuB,EAG5B,KAAK,sBAAsB,EAG3B,KAAK,uBAAuB,CAAA,CAM9B,MAAc,yBAAyC,CAC/C,MAAAC,EAAgB,SAAS,eAAe,eAAe,EACvDC,EAAiB,SAAS,eAAe,gBAAgB,EAGzDC,EAAkB,MAAM,KAAK,eAAe,sBAAsB,EAGxEF,EAAc,UAAY,oCAGVE,EAAA,QAAQ,CAACC,EAAOC,IAAU,CAClC,MAAAC,EAAS,SAAS,cAAc,QAAQ,EACvCA,EAAA,MAAQD,EAAM,SAAS,EACvBC,EAAA,YAAc,GAAGF,EAAM,IAAI,IAAIA,EAAM,SAAW,SAAW,IAAM,IAAI,GACrEE,EAAA,aAAa,kBAAmBF,EAAM,IAAI,EACjDE,EAAO,aAAa,uBAAwBF,EAAM,eAAe,UAAU,EAC3EE,EAAO,aAAa,mBAAoBF,EAAM,WAAW,UAAU,EACnEH,EAAc,YAAYK,CAAM,CAAA,CACjC,EAGaL,EAAA,iBAAiB,SAAU,MAAOM,GAAU,CACxD,MAAMC,EAASD,EAAM,OACfE,EAAgB,SAASD,EAAO,KAAK,EAE3C,GAAI,CAAC,MAAMC,CAAa,GAAKN,EAAgBM,CAAa,EAAG,CACrD,MAAAC,EAAgBP,EAAgBM,CAAa,EAC7C,MAAA,KAAK,kBAAkBC,EAAc,IAAI,EAC/CR,EAAe,MAAM,QAAU,QAChBA,EAAA,aAAa,kBAAmBQ,EAAc,IAAI,EAGjE,KAAK,iBAAiBA,CAAa,CAAA,MAEnCR,EAAe,MAAM,QAAU,OAC/B,KAAK,cAAc,CACrB,CACD,EAGcA,EAAA,iBAAiB,QAAS,IAAM,CACvC,MAAAS,EAAYT,EAAe,aAAa,iBAAiB,EAC3DS,GACF,KAAK,YAAYA,CAAS,CAC5B,CACD,EAGGR,EAAgB,OAAS,IAC3BF,EAAc,MAAQ,IACtB,MAAM,KAAK,kBAAkBE,EAAgB,CAAC,EAAE,IAAI,EACpDD,EAAe,MAAM,QAAU,QAC/BA,EAAe,aAAa,kBAAmBC,EAAgB,CAAC,EAAE,IAAI,EAGjE,KAAA,iBAAiBA,EAAgB,CAAC,CAAC,EAC1C,CAMM,0BAAiC,CAEjC,MAAAS,EAAiB,SAAS,eAAe,gBAAgB,EAC/CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC9C,KAAK,mBAAmB,CAAA,GAIpB,MAAAC,EAAkB,SAAS,eAAe,iBAAiB,EAChDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC1C,KAAA,eAAe,iBAAiBC,CAA0B,EAC/D,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,eAAe,eAAe,EACnC,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAsB,SAAS,eAAe,qBAAqB,EACpDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CACnD,KAAK,eAAe,oBAAoB,EACxC,KAAK,aAAa,QAAQ,CAAA,GAItB,MAAAC,EAAqB,SAAS,eAAe,oBAAoB,EACnDA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAE5C,MAAAb,EAAQ,KAAK,eAAe,gBAAgB,EAC9CA,IACIA,EAAA,uBAAuB,eAAe,EAC5C,KAAK,aAAa,MAAM,EAC1B,GAII,MAAAc,EAAiB,SAAS,eAAe,gBAAgB,EAC/CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC9C,KAAK,YAAY,CAAA,EAClB,CAMK,wBAA+B,CAEhC,KAAA,cAAgB,SAAS,eAAe,cAAc,EACtD,KAAA,YAAc,SAAS,eAAe,YAAY,EAGjD,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,iBAAiB,CAAA,GAIlB,MAAAC,EAAgB,SAAS,eAAe,eAAe,EAC9CA,GAAA,MAAAA,EAAA,iBAAiB,QAAS,IAAM,CAC7C,KAAK,iBAAiB,CAAA,GAIlB,MAAAC,EAAkB,SAAS,eAAe,iBAAiB,EAChDA,GAAA,MAAAA,EAAA,iBAAiB,QAAUd,GAAU,CACpD,MAAMC,EAASD,EAAM,OACjB,KAAK,cACF,KAAA,YAAY,YAAcC,EAAO,MACxC,GAII,MAAAc,EAAkB,SAAS,iBAAiB,kBAAkB,EACpEA,EAAgB,QAAkBC,GAAA,CACzBA,EAAA,iBAAiB,QAAUhB,GAAU,CAC1C,MAAMC,EAASD,EAAM,OACfiB,EAAWhB,EAAO,aAAa,eAAe,EAEhDgB,IAEFF,EAAgB,QAAeG,GAAAA,EAAI,UAAU,OAAO,QAAQ,CAAC,EACtDjB,EAAA,UAAU,IAAI,QAAQ,EAG7B,KAAK,kBAAkBgB,CAAQ,EACjC,CACD,CAAA,CACF,EAGI,KAAA,kBAAkB,KAAK,eAAe,EAG3C,KAAK,oBAAoB,EAGzB,WAAW,IAAM,CACX,KAAK,cACP,KAAK,YAAY,YAAc,kBAC/B,KAAK,iBAAiB,IAEvB,GAAI,CAAA,CAMD,wBAA+B,CAErC,MAAME,EAAkB,SAAY,CAC9B,KAAK,eAAe,gBACtB,KAAK,gBAAgB,EACrB,MAAM,KAAK,oBAAoB,EAC/B,MAAM,KAAK,wBAAwB,EACnC,KAAK,aAAa,QAAQ,GAE1B,WAAWA,EAAiB,GAAG,CAEnC,EAEgBA,EAAA,CAAA,CAMV,YAAYC,EAA0B,CAC5C,KAAK,mBAAqBA,EACrB,KAAA,eAAe,cAAcA,CAAU,EAC5C,KAAK,aAAa,WAAW,EAG7B,KAAK,aAAa,EAGlB,KAAK,uBAAuB,CAAA,CAM9B,MAAc,kBAAkBhB,EAAkC,CAC3D,KAAA,eAAe,gBAAgBA,CAAS,EACxC,KAAA,aAAa,WAAWA,CAAS,KAAK,EAG3C,KAAK,aAAa,EAGlB,KAAK,uBAAuB,EAGxB,GAAA,CACF,MAAMiB,EAAa,MAAM,KAAK,eAAe,cAAcjB,CAAS,EAChEiB,GAAc,CAACA,EAAW,SACvB,KAAA,aAAa,MAAMjB,CAAS,UAAUiB,EAAW,SAAS,OAAO,KAAK,IAAI,CAAC,EAAE,QAE7EnD,EAAO,CACN,QAAA,KAAK,UAAWA,CAAK,CAAA,CAC/B,CAMM,iBAAiB2B,EAAkB,CACnC,MAAAyB,EAAY,SAAS,eAAe,WAAW,EAC/CC,EAAc,SAAS,eAAe,aAAa,EACnDC,EAAkB,SAAS,eAAe,iBAAiB,EAE7DF,GAAaC,GAAeC,IAC9BF,EAAU,MAAM,QAAU,QAC1BC,EAAY,YAAc1B,EAAM,aAAeA,EAAM,aAAa,OAAO,WAAa,IACtF2B,EAAgB,YAAc3B,EAAM,YAAcA,EAAM,YAAY,OAAO,WAAa,IAC1F,CAMM,eAAsB,CACtB,MAAAyB,EAAY,SAAS,eAAe,WAAW,EACjDA,IACFA,EAAU,MAAM,QAAU,OAC5B,CAMM,qBAAqC,CACrC,MAAA5B,EAAgB,SAAS,eAAe,eAAe,EACzD,OAAAA,GAAiBA,EAAc,cAAgB,EAC1BA,EAAc,QAAQA,EAAc,aAAa,EAClD,aAAa,iBAAiB,EAE/C,IAAA,CAMD,iBAAwB,CACxB,MAAAG,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,GAAS,CAACA,EAAM,kBACnB,OAGI,MAAA4B,EAAe,KAAK,eAAe,gBAAgB,EACnDC,EAAc,KAAK,eAAe,mBAAmB,EAG3D,IAAIC,EAAe,EACnBF,EAAa,QAAiBG,GAAA,CACZD,GAAA,KAAK,eAAe,eAAeC,CAAK,CAAA,CACzD,EAGK,MAAAC,EAAqB,SAAS,eAAe,aAAa,EAC1DC,EAAyB,SAAS,eAAe,iBAAiB,EAClEC,EAAmB,SAAS,eAAe,WAAW,EAExDF,IAAoBA,EAAmB,YAAcF,EAAa,SAAS,GAC3EG,IAAwBA,EAAuB,YAAcJ,EAAY,OAAO,SAAS,GACzFK,IAAmCA,EAAA,MAAM,QAAU,OAAA,CAMzD,MAAc,qBAAqC,CAC3C,MAAAC,EAAyB,SAAS,eAAe,eAAe,EACtE,GAAI,CAACA,EAAwB,OAE7BA,EAAuB,UAAY,uCAE/B,GAAA,CAGF,GADqB,KAAK,eAAe,gBAAgB,EACvC,CAEV,MAAA5B,EAAY,KAAK,oBAAoB,EAC3C,GAAIA,EAAW,CACb,MAAM6B,EAAc,MAAM,KAAK,eAAe,0BAA0B7B,CAAS,EAEjF4B,EAAuB,UAAY,GAEnC,UAAWE,KAAaD,EAClBA,EAAY,eAAeC,CAAS,GAClBD,EAAYC,CAAS,EAC7B,QAAQ,CAACC,EAAQrC,IAAU,CACrC,GAAIqC,EAAO,OAAQ,CACX,MAAAnB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,wBACnBA,EAAO,YAAc,GAAGkB,CAAS,IAAIpC,EAAQ,CAAC,GACvCkB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,WAAWkB,EAAWpC,CAAK,EAC/C,KAAK,aAAa,SAASoC,CAAS,IAAIpC,EAAQ,CAAC,EAAE,EAC9C,KAAA,iBAAiBoC,EAAWpC,CAAK,CAAA,CACvC,EACDkC,EAAuB,YAAYhB,CAAM,CAAA,CAC3C,CACD,EAIDgB,EAAuB,SAAS,SAAW,IAC7CA,EAAuB,UAAY,qCAErC,MAAA,CACF,QAEK9D,EAAO,CACN,QAAA,KAAK,uBAAwBA,CAAK,CAAA,CAI5C8D,EAAuB,UAAY,GACd,KAAK,eAAe,gBAAgB,EAE5C,QAAiBJ,GAAA,CAC5B,MAAML,EAAc,KAAK,eAAe,eAAeK,CAAK,EAE5D,QAAS7D,EAAI,EAAGA,EAAIwD,EAAaxD,IAAK,CAC9B,MAAAiD,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,wBACnBA,EAAO,YAAc,GAAGY,CAAK,IAAI7D,EAAI,CAAC,GAC/BiD,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,WAAWY,EAAO7D,CAAC,EACvC,KAAK,aAAa,SAAS6D,CAAK,IAAI7D,EAAI,CAAC,EAAE,EACtC,KAAA,iBAAiB6D,EAAO7D,CAAC,CAAA,CAC/B,EACDiE,EAAuB,YAAYhB,CAAM,CAAA,CAC3C,CACD,EAEGgB,EAAuB,SAAS,SAAW,IAC7CA,EAAuB,UAAY,oCACrC,CAMF,MAAc,yBAAyC,CAC/C,MAAAI,EAA6B,SAAS,eAAe,mBAAmB,EAC9E,GAAI,CAACA,EAA4B,OAEjCA,EAA2B,UAAY,uCAEnC,GAAA,CAGF,GADqB,KAAK,eAAe,gBAAgB,EACvC,CACV,MAAAhC,EAAY,KAAK,oBAAoB,EAC3C,GAAIA,EAAW,CACb,MAAMsB,EAAc,MAAM,KAAK,eAAe,8BAA8BtB,CAAS,EAErFgC,EAA2B,UAAY,GAEvCV,EAAY,QAA0BW,GAAA,CAC9B,MAAArB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,4BACnBA,EAAO,YAAcqB,EACdrB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,cAAcqB,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,CAC5C,EACDD,EAA2B,YAAYpB,CAAM,CAAA,CAC9C,EAEGoB,EAA2B,SAAS,SAAW,IACjDA,EAA2B,UAAY,qCAEzC,MAAA,CACF,QAEKlE,EAAO,CACN,QAAA,KAAK,uBAAwBA,CAAK,CAAA,CAI5CkE,EAA2B,UAAY,GACnB,KAAK,eAAe,mBAAmB,EAE/C,QAA0BC,GAAA,CAC9B,MAAArB,EAAS,SAAS,cAAc,QAAQ,EAC9CA,EAAO,UAAY,4BACnBA,EAAO,YAAcqB,EACdrB,EAAA,iBAAiB,QAAS,IAAM,CAChC,KAAA,eAAe,cAAcqB,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,CAC5C,EACDD,EAA2B,YAAYpB,CAAM,CAAA,CAC9C,EAEGoB,EAA2B,SAAS,SAAW,IACjDA,EAA2B,UAAY,oCACzC,CAMM,cAAqB,CACrB,MAAAJ,EAAyB,SAAS,eAAe,eAAe,EAChEI,EAA6B,SAAS,eAAe,mBAAmB,EACxEL,EAAmB,SAAS,eAAe,WAAW,EAExDC,IACFA,EAAuB,UAAY,qCAEjCI,IACFA,EAA2B,UAAY,qCAErCL,IACFA,EAAiB,MAAM,QAAU,OACnC,CAMM,kBAAyB,CAC1B,KAAK,gBAGN,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,MAInB,KAAA,cAAc,UAAU,IAAI,MAAM,EACvC,KAAK,aAAa,QAAQ,EAGrB,KAAA,eAAiB,OAAO,WAAW,IAAM,CAC5C,KAAK,iBAAiB,GACrB,GAAI,EAAA,CAMD,kBAAyB,CAC1B,KAAK,gBAGN,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,MAInB,KAAA,cAAc,UAAU,OAAO,MAAM,EAC1C,KAAK,aAAa,QAAQ,EAAA,CAMpB,iBAAiBH,EAAeU,EAA2B,CAC7D,GAAA,CAAC,KAAK,YAAa,OAqBvB,MAAMC,EAlB2C,CAC/C,KAAQ,CACN,WACA,YACA,YACA,UACA,cACF,EACA,QAAW,CACT,YACA,OACA,WACA,SACA,UAAA,CAEJ,EAG0BX,CAAK,GAAK,CAAC,WAAW,EAG5C,IAAAY,EACAF,EAAcC,EAAM,OACtBC,EAAeD,EAAMD,CAAW,EAEjBE,EAAAD,EAAM,KAAK,MAAM,KAAK,OAAW,EAAAA,EAAM,MAAM,CAAC,EAI/D,KAAK,YAAY,YAAcC,EAGzB,MAAA1B,EAAkB,SAAS,eAAe,iBAAiB,EAC7DA,IACFA,EAAgB,MAAQ0B,GAI1B,KAAK,iBAAiB,CAAA,CAMhB,qBAA4B,CAC7B,KAAK,gBAGV,KAAK,cAAc,iBAAiB,YAAc,GAAkB,CAClE,KAAK,YAAc,GACd,KAAA,cAAe,UAAU,IAAI,UAAU,EAEtC,MAAAC,EAAO,KAAK,cAAe,sBAAsB,EACvD,KAAK,YAAY,EAAI,EAAE,QAAUA,EAAK,KACtC,KAAK,YAAY,EAAI,EAAE,QAAUA,EAAK,IAEtC,EAAE,eAAe,CAAA,CAClB,EAGQ,SAAA,iBAAiB,YAAc,GAAkB,CACxD,GAAI,CAAC,KAAK,aAAe,CAAC,KAAK,cAAe,OAExC,MAAAC,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EAG5D,IAAIE,EAAO,EAAE,QAAUD,EAAc,KAAO,KAAK,YAAY,EACzDE,EAAO,EAAE,QAAUF,EAAc,IAAM,KAAK,YAAY,EAGtD,MAAAG,EAAc,KAAK,cAAc,YACjCC,EAAe,KAAK,cAAc,aAGjCH,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMD,EAAc,MAAQG,EAAc,EAAE,CAAC,EACnED,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMF,EAAc,OAASI,EAAe,EAAE,CAAC,EAG5E,KAAK,cAAc,MAAM,KAAO,GAAGH,CAAI,KACvC,KAAK,cAAc,MAAM,IAAM,GAAGC,CAAI,KACjC,KAAA,cAAc,MAAM,MAAQ,GAC5B,KAAA,cAAc,MAAM,OAAS,GAC7B,KAAA,cAAc,MAAM,UAAY,EAAA,CACtC,EAGQ,SAAA,iBAAiB,UAAW,IAAM,CACrC,KAAK,aAAe,KAAK,gBAC3B,KAAK,YAAc,GACd,KAAA,cAAc,UAAU,OAAO,UAAU,EAE9C,KAAK,0BAA4B,GACnC,CACD,EAGD,KAAK,cAAc,iBAAiB,aAAe,GAAkB,CACnE,KAAK,YAAc,GACd,KAAA,cAAe,UAAU,IAAI,UAAU,EAEtC,MAAAG,EAAQ,EAAE,QAAQ,CAAC,EACnBP,EAAO,KAAK,cAAe,sBAAsB,EACvD,KAAK,YAAY,EAAIO,EAAM,QAAUP,EAAK,KAC1C,KAAK,YAAY,EAAIO,EAAM,QAAUP,EAAK,IAE1C,EAAE,eAAe,CAAA,CAClB,EAEQ,SAAA,iBAAiB,YAAc,GAAkB,CACxD,GAAI,CAAC,KAAK,aAAe,CAAC,KAAK,cAAe,OAExC,MAAAO,EAAQ,EAAE,QAAQ,CAAC,EACnBN,EAAkB,SAAS,cAAc,mBAAmB,EAClE,GAAI,CAACA,EAAiB,OAEhB,MAAAC,EAAgBD,EAAgB,sBAAsB,EAG5D,IAAIE,EAAOI,EAAM,QAAUL,EAAc,KAAO,KAAK,YAAY,EAC7DE,EAAOG,EAAM,QAAUL,EAAc,IAAM,KAAK,YAAY,EAG1D,MAAAG,EAAc,KAAK,cAAc,YACjCC,EAAe,KAAK,cAAc,aAGjCH,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMD,EAAc,MAAQG,EAAc,EAAE,CAAC,EACnED,EAAA,KAAK,IAAI,GAAI,KAAK,IAAIA,EAAMF,EAAc,OAASI,EAAe,EAAE,CAAC,EAG5E,KAAK,cAAc,MAAM,KAAO,GAAGH,CAAI,KACvC,KAAK,cAAc,MAAM,IAAM,GAAGC,CAAI,KACjC,KAAA,cAAc,MAAM,MAAQ,GAC5B,KAAA,cAAc,MAAM,OAAS,GAC7B,KAAA,cAAc,MAAM,UAAY,EAAA,CACtC,EAEQ,SAAA,iBAAiB,WAAY,IAAM,CACtC,KAAK,aAAe,KAAK,gBAC3B,KAAK,YAAc,GACd,KAAA,cAAc,UAAU,OAAO,UAAU,EAE9C,KAAK,0BAA4B,GACnC,CACD,EAAA,CAMK,kBAAkB5B,EAAwB,CAC5C,GAAC,KAAK,eAGN,MAAK,0BAeT,OAXA,KAAK,gBAAkBA,EAGvB,KAAK,cAAc,UAAU,OAAO,MAAO,SAAU,OAAQ,OAAO,EAC/D,KAAA,cAAc,MAAM,KAAO,GAC3B,KAAA,cAAc,MAAM,IAAM,GAC1B,KAAA,cAAc,MAAM,MAAQ,GAC5B,KAAA,cAAc,MAAM,OAAS,GAC7B,KAAA,cAAc,MAAM,UAAY,GAG7BA,EAAU,CAChB,IAAK,cACE,KAAA,cAAc,MAAM,KAAO,OAC3B,KAAA,cAAc,MAAM,OAAS,OAC7B,KAAA,cAAc,UAAU,IAAI,QAAQ,EACzC,MACF,IAAK,eACE,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,OAAS,OAClC,KAAK,cAAc,UAAU,IAAI,SAAU,OAAO,EAClD,MACF,IAAK,WACE,KAAA,cAAc,MAAM,KAAO,OAC3B,KAAA,cAAc,MAAM,IAAM,OAC1B,KAAA,cAAc,UAAU,IAAI,KAAK,EACtC,MACF,IAAK,YACE,KAAA,cAAc,MAAM,MAAQ,OAC5B,KAAA,cAAc,MAAM,IAAM,OAC/B,KAAK,cAAc,UAAU,IAAI,MAAO,OAAO,EAC/C,MACF,IAAK,SACL,QAEO,KAAA,cAAc,MAAM,KAAO,MAC3B,KAAA,cAAc,MAAM,IAAM,MAC1B,KAAA,cAAc,MAAM,UAAY,wBACrC,KAAA,CACJ,CAMM,aAAoB,CACpB,MAAApB,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAIE,KAAK,cACP,KAAK,YAAY,YAAc,gBAC/B,KAAK,iBAAiB,GAIpB,GAAA,CAEF,MAAMoD,EAAY,yBAGjBpD,EAAc,SAAW,GAGpB,MAAAqD,EAAQ,IAAI,MAAMD,CAAS,EACjCC,EAAM,OAAS,GAGf,MAAMC,EAAkBtD,EAAc,gBAClCsD,GAEF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,MAAMF,CAAS,CAAC,CAAA,CAChD,EAAE,KAAK,IAAM,CACZ,KAAK,aAAa,mBAAmB,EAGrC,WAAW,IAAM,CACX,KAAK,cACP,KAAK,YAAY,YAAc,qBAEhC,GAAI,CAAA,CACR,EAAE,MAAe/E,GAAA,CACR,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,sBAAsB,CAAA,CACzC,EAGKgF,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,UAAU,EACxB,KAAK,cACP,KAAK,YAAY,YAAc,YACjC,CACD,GAED,KAAK,aAAa,WAAW,QAExBhF,EAAO,CACN,QAAA,MAAM,YAAaA,CAAK,EAChC,KAAK,aAAa,kBAAkB,CAAA,CACtC,CASK,YAAYkD,EAA0B,CACvCA,GAAc,GAAKA,EAAagC,GAC7B,KAAA,eAAe,cAAchC,CAAU,EAC5C,KAAK,aAAa,SAASA,EAAa,CAAC,EAAE,GAEtC,KAAA,aAAa,YAAYA,CAAU,EAAE,CAC5C,CAQK,WAAWQ,EAAe9B,EAAgB,EAAS,CAC1C,KAAK,eAAe,gBAAgB,GAE3C,KAAA,eAAe,WAAW8B,EAAO9B,CAAK,EAC3C,KAAK,aAAa,SAAS8B,CAAK,IAAI9B,EAAQ,CAAC,EAAE,EAG1C,KAAA,iBAAiB8B,EAAO9B,CAAK,GAElC,KAAK,aAAa,UAAU,CAC9B,CAOK,eAAeA,EAAqB,CACnC,MAAAuD,EAAkB,KAAK,eAAe,mBAAmB,EAC/D,GAAIvD,GAAS,GAAKA,EAAQuD,EAAgB,OAAQ,CAC1C,MAAAhB,EAAiBgB,EAAgBvD,CAAK,EACvC,KAAA,eAAe,cAAcuC,CAAc,EAC3C,KAAA,aAAa,SAASA,CAAc,EAAE,CAAA,MAEtC,KAAA,aAAa,YAAYvC,CAAK,EAAE,CACvC,CAMK,sBAA6B,CAClC,KAAK,eAAe,oBAAoB,EACxC,KAAK,aAAa,QAAQ,CAAA,CAQrB,WAAWwD,EAAcC,EAAoB,GAAY,CAC9D,GAAI,KAAK,YAAa,CACpB,KAAK,YAAY,YAAcD,EAGzB,MAAAxC,EAAkB,SAAS,eAAe,iBAAiB,EAC7DA,IACFA,EAAgB,MAAQwC,EAC1B,CAGF,KAAK,iBAAiB,EAEjBC,GAEC,KAAK,iBACP,aAAa,KAAK,cAAc,EAChC,KAAK,eAAiB,KAE1B,CAMK,YAAmB,CACxB,KAAK,iBAAiB,CAAA,CAOjB,qBAAqBN,EAAyB,CAC7C,MAAApD,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAGE,GAAA,CAEDA,EAAc,SAAW,GAG1B,MAAMqD,EAAQ,IAAI,MAAM,mBAAmBD,CAAS,EAAE,EACtDC,EAAM,OAAS,GAGf,MAAMC,EAAkBtD,EAAc,gBAClCsD,GACF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,MAAM,mBAAmBF,CAAS,EAAE,CAAC,CAAA,CACrE,EAAE,KAAK,IAAM,CACP,KAAA,aAAa,SAASA,CAAS,EAAE,CAAA,CACvC,EAAE,MAAe/E,GAAA,CACR,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,sBAAsB,CAAA,CACzC,EAEKgF,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,QAAQ,CAAA,CAC3B,GAED,KAAK,aAAa,WAAW,QAExBhF,EAAO,CACN,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,gBAAgB,CAAA,CACpC,CAOF,MAAa,oBAAoBP,EAAmC,CAC5D,MAAAkC,EAAQ,KAAK,eAAe,gBAAgB,EAClD,GAAI,CAACA,EAAO,CACV,KAAK,aAAa,UAAU,EAC5B,MAAA,CAGE,GAAA,CACF,KAAK,aAAa,aAAa,EAG/B,MAAM2D,EAAiB,MAAMjE,EAAe,mBAAmB5B,CAAU,EAGxEkC,EAAc,SAAW,GAGpB,MAAA4D,EAAY,IAAI,KAAK,CAACD,CAAc,EAAG,CAAE,KAAM,YAAa,EAC5DE,EAAW,IAAI,gBAAgBD,CAAS,EACxCP,EAAQ,IAAI,MAAMQ,CAAQ,EAChCR,EAAM,OAAS,GAGf,MAAMC,EAAkBtD,EAAc,gBAClCsD,GACF,QAAQ,IAAI,CACVD,EAAM,KAAK,EACX,QAAQ,QAAQC,EAAe,qBAAqBK,CAAc,CAAC,CAAA,CACpE,EAAE,KAAK,IAAM,CACZ,KAAK,aAAa,cAAc,CAAA,CACjC,EAAE,MAAetF,GAAA,CACR,QAAA,MAAM,gBAAiBA,CAAK,EACpC,KAAK,aAAa,4BAA4B,CAAA,CAC/C,EAEKgF,EAAA,iBAAiB,QAAS,IAAM,CACpC,KAAK,aAAa,cAAc,EAEhC,IAAI,gBAAgBQ,CAAQ,CAAA,CAC7B,EAGKR,EAAA,iBAAiB,QAAUhF,GAAU,CACjC,QAAA,MAAM,UAAWA,CAAK,EAC9B,KAAK,aAAa,QAAQ,EAC1B,IAAI,gBAAgBwF,CAAQ,CAAA,CAC7B,GAED,KAAK,aAAa,WAAW,QAExBxF,EAAO,CACN,QAAA,MAAM,gBAAiBA,CAAK,EACpC,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EACrE,KAAA,aAAa,iBAAiBG,CAAY,EAAE,CAAA,CACnD,CAMK,qBAA2B,CAEhC,OADc,KAAK,eAAe,gBAAgB,EAEzC,CACL,UAAW,gBACX,SAAU,GACV,aAAc,KAAK,eAAe,gBAAgB,EAClD,YAAa,KAAK,eAAe,mBAAmB,CACtD,EAEK,CACL,UAAW,WACX,SAAU,GACV,aAAc,CAAC,EACf,YAAa,CAAA,CACf,CAAA,CAQM,oBAA2B,CAC3B,MAAAsF,EAAe,SAAS,cAAc,gBAAgB,EACxDA,IACWA,EAAA,UAAU,OAAO,QAAQ,EACtC,QAAQ,IAAI,aAAa,EAC3B,CAMM,aAAaC,EAAuB,CACpC,MAAAC,EAAgB,SAAS,eAAe,YAAY,EACtDA,IACFA,EAAc,YAAcD,GAItBE,EAAA,aAAa,mBAAmBF,CAAO,EAAE,CAAA,CAM3C,uBAA8B,CAC9B,MAAAG,EAAY,SAAS,eAAe,gBAAgB,EACpDC,EAAgB,SAAS,eAAe,eAAe,EACvDC,EAAY,SAAS,eAAe,WAAW,EAC/CC,EAAmB,SAAS,eAAe,kBAAkB,EAGrDF,EAAA,iBAAiB,QAAS,IAAM,CAC5CD,EAAU,MAAM,CAAA,CACjB,EAGSA,EAAA,iBAAiB,SAAW/D,GAAU,OAC9C,MAAMC,EAASD,EAAM,OACfmE,GAAOC,EAAAnE,EAAO,QAAP,YAAAmE,EAAe,GAExBD,IACEA,EAAK,OAAS,mBAAqBA,EAAK,KAAK,SAAS,MAAM,GAC7CD,EAAA,YAAc,QAAQC,EAAK,IAAI,GAChDD,EAAiB,MAAM,QAAU,QACjCD,EAAU,MAAM,QAAU,QAC1B,KAAK,aAAa,UAAUE,EAAK,IAAI,EAAE,IAEvC,MAAM,aAAa,EACnBlE,EAAO,MAAQ,IAEnB,CACD,EAGSgE,EAAA,iBAAiB,QAAS,IAAM,OAClC,MAAAE,GAAOC,EAAAL,EAAU,QAAV,YAAAK,EAAkB,GAC3BD,GACF,KAAK,YAAYA,CAAI,CACvB,CACD,CAAA,CAMH,MAAa,YAAYA,EAA2B,CAC5C,MAAAE,EAAe,SAAS,eAAe,cAAc,EACrDC,EAAmB,SAAS,eAAe,kBAAkB,EAC7DC,EAAc,SAAS,eAAe,aAAa,EACnDN,EAAY,SAAS,eAAe,WAAW,EAEjD,GAAA,CAEFI,EAAa,MAAM,QAAU,QAC7BA,EAAa,UAAY,gBACzBC,EAAiB,YAAc,UAC/BC,EAAY,MAAM,MAAQ,KAC1BN,EAAU,SAAW,GAGf,MAAAO,EAAW,IAAI,SACZA,EAAA,OAAO,WAAYL,CAAI,EAG1B,MAAAM,EAAW,MAAM,MAAM,yCAA0C,CACrE,OAAQ,OACR,KAAMD,CAAA,CACP,EAED,GAAIC,EAAS,GAAI,CACT,MAAAC,EAAS,MAAMD,EAAS,KAAK,EAQnC,GALAJ,EAAa,UAAY,wBACRC,EAAA,YAAc,YAAYI,EAAO,SAAS,OAC3DH,EAAY,MAAM,MAAQ,OAGtBG,EAAO,SAAU,CACnB,MAAMC,EAAyB,CAAC,EAC5BD,EAAO,SAAS,gBAClBC,EAAa,KAAK,OAAOD,EAAO,SAAS,YAAY,MAAM,GAAG,EAE5DA,EAAO,SAAS,YAClBC,EAAa,KAAK,QAAQD,EAAO,SAAS,aAAa,MAAM,GAAG,EAE9DA,EAAO,SAAS,QAAUA,EAAO,SAAS,OAAO,OAAS,GAC5DC,EAAa,KAAK,OAAOD,EAAO,SAAS,OAAO,MAAM,GAAG,EAGvDC,EAAa,OAAS,IACxBL,EAAiB,aAAe,KAAKK,EAAa,KAAK,IAAI,CAAC,IAC9D,CAIF,MAAM,KAAK,iBAAiB,EAG5B,KAAK,mBAAmB,EAGnB,KAAA,2BAA2BD,EAAO,SAAS,EAEhD,KAAK,aAAa,MAAMA,EAAO,SAAS,OAAO,CAAA,KAC1C,CACC,MAAAxG,EAAQ,MAAMuG,EAAS,KAAK,EAClC,MAAM,IAAI,MAAMvG,EAAM,OAAS,MAAM,CAAA,QAEhCA,EAAO,CAEd,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAC9DmG,EAAa,UAAY,sBACRC,EAAA,YAAc,SAASjG,CAAY,GAC/C,KAAA,aAAa,SAASA,CAAY,EAAE,CAAA,QACzC,CACA4F,EAAU,SAAW,EAAA,CACvB,CAMF,MAAa,kBAAkC,CACzC,GAAA,CAEF,MAAM,KAAK,wBAAwB,EACnC,KAAK,aAAa,SAAS,QACpB/F,EAAO,CACN,QAAA,MAAM,YAAaA,CAAK,EAChC,KAAK,aAAa,UAAU,CAAA,CAC9B,CAMM,oBAA2B,CAC3B,MAAA6F,EAAY,SAAS,eAAe,gBAAgB,EACpDG,EAAmB,SAAS,eAAe,kBAAkB,EAC7DD,EAAY,SAAS,eAAe,WAAW,EAErDF,EAAU,MAAQ,GAClBG,EAAiB,MAAM,QAAU,OACjCD,EAAU,MAAM,QAAU,MAAA,CAM5B,MAAa,YAAY7D,EAAkC,CACzD,GAAK,QAAQ,YAAYA,CAAS,cAAc,EAI5C,GAAA,CACG,KAAA,aAAa,WAAWA,CAAS,KAAK,EAE3C,MAAMqE,EAAW,MAAM,MAAM,oCAAoCrE,CAAS,GAAI,CAC5E,OAAQ,QAAA,CACT,EAED,GAAIqE,EAAS,GAAI,CACf,MAAMA,EAAS,KAAK,EACf,KAAA,aAAa,MAAMrE,CAAS,OAAO,EAGxC,MAAM,KAAK,iBAAiB,EAGtB,MAAAT,EAAiB,SAAS,eAAe,gBAAgB,EAC/DA,EAAe,MAAM,QAAU,OAG/B,KAAK,0BAA0BS,CAAS,CAAA,KAEnC,CACC,MAAAlC,EAAQ,MAAMuG,EAAS,KAAK,EAClC,MAAM,IAAI,MAAMvG,EAAM,OAAS,MAAM,CAAA,QAEhCA,EAAO,CACd,MAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OACzD,KAAA,aAAa,SAASG,CAAY,EAAE,EACnC,MAAA,WAAWA,CAAY,EAAE,CAAA,CACjC,CAMM,2BAA2B+B,EAAyB,CACtD,GAAA,CAEE,OAAO,QAAU,OAAO,SAAW,QACrC,OAAO,OAAO,YAAY,CACxB,KAAM,wBACN,UAAAA,GACC,GAAG,EAIH,OAAe,uBAChB,OAAQ,OAAe,sBAAsB,qBAAwB,YACtE,OAAe,sBAAsB,oBAAoB,QAErDlC,EAAO,CACN,QAAA,KAAK,eAAgBA,CAAK,CAAA,CACpC,CAMM,0BAA0BkC,EAAyB,CACrD,GAAA,CAEE,OAAO,QAAU,OAAO,SAAW,QACrC,OAAO,OAAO,YAAY,CACxB,KAAM,uBACN,UAAAA,GACC,GAAG,EAIH,OAAe,uBAChB,OAAQ,OAAe,sBAAsB,qBAAwB,YACtE,OAAe,sBAAsB,oBAAoB,QAErDlC,EAAO,CACN,QAAA,KAAK,eAAgBA,CAAK,CAAA,CACpC,CAMK,SAAgB,CACrBuB,EAAa,gBAAgB,CAAA,CAEjC,CAGA,IAAImF,EAKJ,OAAO,iBAAiB,mBAAoB,SAAY,CACtDA,EAAM,IAAIpF,EACV,MAAMoF,EAAI,WAAW,EAGpB,OAAe,mBAAqB,CAEnC,YAAc9E,GAAkB8E,EAAI,YAAY9E,CAAK,EAGrD,WAAY,CAAC8B,EAAe9B,EAAgB,IAAM8E,EAAI,WAAWhD,EAAO9B,CAAK,EAG7E,eAAiBA,GAAkB8E,EAAI,eAAe9E,CAAK,EAC3D,qBAAsB,IAAM8E,EAAI,qBAAqB,EAGrD,WAAY,CAACtB,EAAcC,EAAoB,KAASqB,EAAI,WAAWtB,EAAMC,CAAQ,EACrF,WAAY,IAAMqB,EAAI,WAAW,EAGjC,UAAY3B,GAAsB2B,EAAI,qBAAqB3B,CAAS,EACpE,oBAAsBtF,GAAuBiH,EAAI,oBAAoBjH,CAAU,EAG/E,YAAcwG,GAAeS,EAAI,YAAYT,CAAI,EACjD,iBAAkB,IAAMS,EAAI,iBAAiB,EAC7C,YAAcxE,GAAsBwE,EAAI,YAAYxE,CAAS,EAG7D,aAAc,IAAMwE,EAAI,oBAAoB,EAG5C,KAAMA,CACR,EAGO,OAAA,iBAAiB,UAAY5E,GAAU,WAE5C,GAAIA,EAAM,MAAQA,EAAM,KAAK,OAAS,eAAgB,CAC9C,MAAA2D,EAAe,SAAS,cAAc,gBAAgB,EACxDA,IACWA,EAAA,UAAU,OAAO,QAAQ,EACtC,QAAQ,IAAI,aAAa,GAE3B,MAAA,CAIF,GAAI3D,EAAM,MAAQA,EAAM,KAAK,OAAS,kBAAmB,CACvD,KAAM,CAAE,UAAA6E,EAAW,OAAAC,EAAQ,KAAAC,GAAS/E,EAAM,KAEtC,GAAA,CAEF,MAAMgF,EAAO,OAAe,mBAC5B,GAAIA,GAAO,OAAOA,EAAIF,CAAM,GAAM,WAAY,CAE5C,MAAMJ,EAASM,EAAIF,CAAM,EAAE,GAAGC,CAAI,EAG9BL,GAAU,OAAOA,EAAO,MAAS,WAC5BA,EAAA,KAAMO,GAAa,QAEvBb,EAAApE,EAAM,SAAN,MAAAoE,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAS,EACA,QAAS,GACT,OAAQI,GACP,IAAG,CACP,EAAE,MAAO/G,GAAe,QAEtBkG,EAAApE,EAAM,SAAN,MAAAoE,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAS,EACA,QAAS,GACT,MAAO3G,EAAM,SAAW,QACvB,IAAG,CACP,GAGAkG,EAAApE,EAAM,SAAN,MAAAoE,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAS,EACA,QAAS,GACT,OAAAH,GACC,IACL,MAGCQ,EAAAlF,EAAM,SAAN,MAAAkF,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAL,EACA,QAAS,GACT,MAAO,MAAMC,CAAM,QAClB,WAEE5G,EAAY,EAElBiH,EAAAnF,EAAM,SAAN,MAAAmF,EAAsB,YAAY,CACjC,KAAM,sBACN,UAAAN,EACA,QAAS,GACT,MAAO3G,EAAM,SAAW,QACvB,IAAG,CACR,CACF,CACD,CACH,CAAC,EAKD,OAAO,iBAAiB,eAAgB,IAAM,CACxC0G,GACFA,EAAI,QAAQ,CAEhB,CAAC"}