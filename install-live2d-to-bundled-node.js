#!/usr/bin/env node

/**
 * 将Live2D依赖安装到内嵌的Node.js环境中
 * 确保打包后的应用能够正确运行Live2D服务器
 */

const { spawn, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('📦 安装Live2D依赖到内嵌Node.js环境...\n');

// 检查必要的目录和文件
const live2dDir = path.join(__dirname, 'app', 'live2d');
const live2dPackageJson = path.join(live2dDir, 'package.json');
// 查找实际的内嵌Node目录
let bundledNodeDir;
let bundledNodeModules;

const possibleNodeDirs = [
  path.join(__dirname, 'node_modules', 'bundled_node'),
  path.join(__dirname, 'node-v23.6.1-darwin-arm64'),
  path.join(__dirname, 'node-v23.6.1-windows-x64')
];

for (const dir of possibleNodeDirs) {
  if (fs.existsSync(dir)) {
    bundledNodeDir = dir;
    // 对于node-v23.6.1-darwin-arm64，node_modules在lib目录下
    if (dir.includes('node-v23.6.1-darwin-arm64')) {
      bundledNodeModules = path.join(dir, 'lib', 'node_modules');
    } else if (dir.includes('node-v23.6.1-windows-x64')) {
      // 对于Windows版本，node_modules在根目录下
      bundledNodeModules = path.join(dir, 'node_modules');
    } else {
      bundledNodeModules = path.join(dir, 'node_modules');
    }
    break;
  }
}

console.log('🔍 检查环境...');
console.log('  Live2D目录:', live2dDir);
console.log('  内嵌Node目录:', bundledNodeDir);

// 检查Live2D目录
if (!fs.existsSync(live2dDir)) {
  console.error('❌ Live2D目录不存在:', live2dDir);
  process.exit(1);
}

if (!fs.existsSync(live2dPackageJson)) {
  console.error('❌ Live2D package.json不存在:', live2dPackageJson);
  process.exit(1);
}

// 检查内嵌Node目录
if (!bundledNodeDir || !fs.existsSync(bundledNodeDir)) {
  console.error('❌ 未找到内嵌Node目录');
  console.error('💡 尝试的路径:');
  possibleNodeDirs.forEach(dir => console.error(`  - ${dir}`));
  console.error('💡 请确保内嵌Node.js已正确安装');
  process.exit(1);
}

// 确保内嵌Node的node_modules目录存在
if (!fs.existsSync(bundledNodeModules)) {
  console.log('📁 创建内嵌Node的node_modules目录...');
  fs.mkdirSync(bundledNodeModules, { recursive: true });
}

// 读取Live2D的依赖
console.log('\n📋 读取Live2D依赖...');
let live2dPackage;
try {
  live2dPackage = JSON.parse(fs.readFileSync(live2dPackageJson, 'utf8'));
} catch (error) {
  console.error('❌ 读取Live2D package.json失败:', error.message);
  process.exit(1);
}

const dependencies = live2dPackage.dependencies || {};
const dependencyNames = Object.keys(dependencies);

console.log('  依赖列表:', dependencyNames.join(', '));

if (dependencyNames.length === 0) {
  console.log('✅ Live2D没有依赖，无需安装');
  process.exit(0);
}

// 检查内嵌Node的npm路径
let nodePath, npmPath;

if (bundledNodeDir.includes('node-v23.6.1-darwin-arm64')) {
  // 对于node-v23.6.1-darwin-arm64，可执行文件在bin目录下
  nodePath = process.platform === 'win32'
    ? path.join(bundledNodeDir, 'bin', 'node.exe')
    : path.join(bundledNodeDir, 'bin', 'node');

  npmPath = process.platform === 'win32'
    ? path.join(bundledNodeDir, 'bin', 'npm.cmd')
    : path.join(bundledNodeDir, 'bin', 'npm');
} else if (bundledNodeDir.includes('node-v23.6.1-windows-x64')) {
  // 对于node-v23.6.1-windows-x64，可执行文件在根目录下
  nodePath = path.join(bundledNodeDir, 'node.exe');
  npmPath = path.join(bundledNodeDir, 'npm.cmd');
} else {
  // 对于bundled_node，可执行文件在根目录
  nodePath = process.platform === 'win32'
    ? path.join(bundledNodeDir, 'node.exe')
    : path.join(bundledNodeDir, 'node');

  npmPath = process.platform === 'win32'
    ? path.join(bundledNodeDir, 'npm.cmd')
    : path.join(bundledNodeDir, 'npm');
}

console.log('\n🔧 检查内嵌Node工具...');
console.log('  Node路径:', nodePath);
console.log('  NPM路径:', npmPath);

if (!fs.existsSync(nodePath)) {
  console.error('❌ 内嵌Node可执行文件不存在:', nodePath);
  process.exit(1);
}

// 安装依赖的方法
async function installDependencies() {
  console.log('\n📦 开始安装Live2D依赖到内嵌Node环境...');
  
  // 创建临时package.json
  const tempPackageJson = {
    name: 'live2d-deps-temp',
    version: '1.0.0',
    dependencies: dependencies
  };
  
  const tempDir = path.join(__dirname, 'temp-live2d-install');
  const tempPackageJsonPath = path.join(tempDir, 'package.json');
  
  try {
    // 创建临时目录
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    fs.mkdirSync(tempDir, { recursive: true });
    
    // 写入临时package.json
    fs.writeFileSync(tempPackageJsonPath, JSON.stringify(tempPackageJson, null, 2));
    console.log('📝 创建临时package.json:', tempPackageJsonPath);
    
    // 设置环境变量
    const env = {
      ...process.env,
      NODE_PATH: bundledNodeModules,
      NPM_CONFIG_PREFIX: bundledNodeDir
    };
    
    // 使用内嵌Node安装依赖
    console.log('🔄 正在安装依赖...');
    
    const installCommand = fs.existsSync(npmPath) ? npmPath : 'npm';
    const installArgs = ['install', '--production', '--no-package-lock'];
    
    console.log(`执行: ${installCommand} ${installArgs.join(' ')}`);
    
    await runCommand(installCommand, installArgs, {
      cwd: tempDir,
      env: env
    });
    
    // 复制安装的依赖到内嵌Node的node_modules
    const tempNodeModules = path.join(tempDir, 'node_modules');
    if (fs.existsSync(tempNodeModules)) {
      console.log('📂 复制依赖到内嵌Node环境...');
      
      const installedDeps = fs.readdirSync(tempNodeModules);
      for (const dep of installedDeps) {
        const srcPath = path.join(tempNodeModules, dep);
        const destPath = path.join(bundledNodeModules, dep);
        
        if (fs.existsSync(destPath)) {
          fs.rmSync(destPath, { recursive: true, force: true });
        }
        
        copyRecursive(srcPath, destPath);
        console.log(`  ✅ ${dep}`);
      }
    }
    
    // 清理临时目录
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log('🧹 清理临时文件');
    
    console.log('\n✅ Live2D依赖安装完成！');
    console.log('📍 依赖位置:', bundledNodeModules);
    
  } catch (error) {
    console.error('❌ 安装依赖失败:', error.message);
    
    // 清理临时目录
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    
    process.exit(1);
  }
}

// 运行命令的辅助函数
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const process = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    process.on('error', (err) => {
      reject(new Error(`命令执行失败: ${err.message}`));
    });
    
    process.on('exit', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令退出码: ${code}`));
      }
    });
  });
}

// 递归复制文件夹
function copyRecursive(src, dest) {
  const stat = fs.statSync(src);
  
  if (stat.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    for (const file of files) {
      copyRecursive(path.join(src, file), path.join(dest, file));
    }
  } else {
    fs.copyFileSync(src, dest);
  }
}

// 开始安装
installDependencies().catch((error) => {
  console.error('❌ 安装过程失败:', error.message);
  process.exit(1);
});
